import { afterAll, describe, expect, test } from "vitest";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { findValidatedNotification } from "./utils/validation";

describe("Account Notification System - RLS and Core Functionality", () => {
  const admin = mockAdmin();
  const customer = mockCustomer();
  const provider = mockProvider();
  const createdNotificationIds: string[] = [];
  const createdUserIds: string[] = [];

  describe("RLS Policies", () => {
    test("users can only see their own notifications", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");
      if (!provider.data) throw new Error("Provider not defined");

      // Create a notification for the customer
      const { data: notification } = await serviceClient
        .schema("app_account")
        .rpc("create_notification", {
          p_type: "test.notification",
          p_recipient_id: customer.data.id,
          p_title_key: "test.title",
          p_message_key: "test.message",
          p_data: { test: "data" }
        });

      if (notification?.id) createdNotificationIds.push(notification.id);

      // Customer should see their notification
      const { data: customerNotifications } = await customer.client
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("type", "test.notification");

      expect(customerNotifications).toBeDefined();
      expect(customerNotifications!.length).toBeGreaterThan(0);

      // Provider should not see customer's notification
      if (!provider.client) throw new Error("Provider client not defined");
      const { data: providerNotifications } = await provider.client
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("type", "test.notification");

      expect(providerNotifications).toBeDefined();
      expect(providerNotifications!.length).toBe(0);
    });

    test("users can mark their own notifications as read", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create a notification for the customer
      const { data: notification } = await serviceClient
        .schema("app_account")
        .rpc("create_notification", {
          p_type: "test.read",
          p_recipient_id: customer.data.id,
          p_title_key: "test.read.title",
          p_message_key: "test.read.message",
          p_data: { test: "read" }
        });

      if (notification?.id) createdNotificationIds.push(notification.id);

      // Customer marks notification as read
      const { error } = await customer.client
        .schema("app_account")
        .from("notification")
        .update({ read_at: new Date().toISOString() })
        .eq("id", notification!.id);

      expect(error).toBeNull();

      // Verify notification is marked as read
      const { data: readNotification } = await customer.client
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("id", notification!.id)
        .single();

      expect(readNotification).toBeDefined();
      expect(readNotification!.read_at).toBeDefined();
    });

    test("users can delete their own notifications", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create a notification for the customer
      const { data: notification } = await serviceClient
        .schema("app_account")
        .rpc("create_notification", {
          p_type: "test.delete",
          p_recipient_id: customer.data.id,
          p_title_key: "test.delete.title",
          p_message_key: "test.delete.message",
          p_data: { test: "delete" }
        });

      expect(notification).toBeDefined();

      // Customer deletes their notification
      const { error } = await customer.client
        .schema("app_account")
        .from("notification")
        .delete()
        .eq("id", notification!.id);

      expect(error).toBeNull();

      // Verify notification was deleted
      const { data: deletedNotification } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("id", notification!.id);

      expect(deletedNotification).toHaveLength(0);
    });
  });

  describe("Account Management Notifications", () => {
    test("welcome notification is sent on user signup", async () => {
      // Create a new user
      const newUser = await serviceClient.auth.admin.createUser({
        email: "<EMAIL>",
        password: "password123",
        email_confirm: true
      });

      expect(newUser.data.user).toBeDefined();

      if (newUser.data.user) {
        createdUserIds.push(newUser.data.user.id);

        // Check if welcome notification was created
        const { data: notifications } = await serviceClient
          .schema("app_account")
          .from("notification")
          .select("*")
          .eq("recipient_id", newUser.data.user.id)
          .eq("type", "account.welcome");

        expect(notifications).toBeDefined();
        const welcomeNotification = notifications?.find(
          (n) => n.type === "account.welcome"
        );
        expect(welcomeNotification).toBeDefined();
        expect(welcomeNotification!.title_key).toBe(
          "notifications.account.welcome.title"
        );

        if (welcomeNotification?.id)
          createdNotificationIds.push(welcomeNotification.id);
      }
    });

    test("KYC status change notification is sent", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create KYC record
      await customer.client.schema("app_account").from("kyc").insert({
        user_id: customer.data.id,
        full_name: "Test User",
        status: "pending"
      });

      // Admin updates KYC status
      await serviceClient
        .schema("app_account")
        .from("kyc")
        .update({ status: "approved" })
        .eq("user_id", customer.data.id);

      // Check if notification was created and validate its structure
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "account.kyc.status_change");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "account.kyc.status_change",
        (data) => data.new_status === "approved"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.new_status).toBe("approved");
      expect(validatedNotification!.data.old_status).toBe("pending");

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("ban notification is sent when user is banned", async () => {
      if (!customer.data || !admin.data)
        throw new Error("Customer or admin not defined");

      // Admin bans the customer
      await serviceClient.schema("app_account").from("banned_user").insert({
        user_id: customer.data.id,
        banned_by: admin.data.id,
        reason: "Test ban"
      });

      // Check if notification was created and validate its structure
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "account.banned");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "account.banned",
        (data) => data.reason === "Test ban"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.reason).toBe("Test ban");
      expect(validatedNotification!.data.banned_by).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }

      // Cleanup - unban user
      await serviceClient
        .schema("app_account")
        .from("banned_user")
        .delete()
        .eq("user_id", customer.data.id);
    });
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }

    // Cleanup created users
    if (createdUserIds.length > 0) {
      for (const userId of createdUserIds) {
        await serviceClient.auth.admin.deleteUser(userId);
      }
    }
  });
});
