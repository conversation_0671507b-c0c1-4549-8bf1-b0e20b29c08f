import { describe, expect, test } from "vitest";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { mockProviderActivity } from "./mocks/app_provider.activity";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { createRoleTestMatrix } from "./utils/matrix";

const admin = mockAdmin();
const provider = mockProvider();
const customer = mockCustomer();

const catalogActivity1 = mockCatalogActivity({ admin });
const catalogActivity2 = mockCatalogActivity({ admin });
const catalogActivity3 = mockCatalogActivity({ admin });

const sharedCatalogActivity = mockCatalogActivity({ admin });
const sharedProviderActivity = mockProviderActivity({
  provider,
  catalogActivity: sharedCatalogActivity
});

const testMatrix = createRoleTestMatrix<
  "admin" | "customer" | "provider",
  "activity"
>({
  admin: {
    user: admin,
    permissions: {
      activity: { view: true, insert: true, update: true, delete: true }
    }
  },
  customer: {
    user: customer,
    permissions: {
      activity: { view: true, insert: false, update: false, delete: false }
    }
  },
  provider: {
    user: provider,
    permissions: {
      activity: { view: true, insert: true, update: true, delete: true }
    }
  }
});

describe.each(testMatrix)("$role", ({ user, permissions }) => {
  const userActivity = mockProviderActivity({
    provider: user,
    catalogActivity: catalogActivity1
  });

  const activityToUpdate = mockProviderActivity({
    provider: user,
    catalogActivity: catalogActivity2
  });

  const activityToDelete = mockProviderActivity({
    provider: user,
    catalogActivity: catalogActivity3
  });

  test(`can view: ${permissions.activity.view}`, async () => {
    if (permissions.activity.view) {
      expect(sharedProviderActivity.id).toBeDefined();
    } else {
      expect(sharedProviderActivity.id).toBeNull();
    }
  });

  test(`can insert: ${permissions.activity.insert}`, () => {
    if (permissions.activity.insert) {
      expect(userActivity.id).toBeDefined();
    } else {
      expect(userActivity.id).toBeUndefined();
    }
  });

  test(`can update: ${permissions.activity.update}`, async () => {
    if (!user.client) throw new Error("User client is undefined");
    if (!activityToUpdate.id) return;

    // save initial value
    const { data: initialActivity } = await user.client
      .schema("app_provider")
      .from("activity")
      .select()
      .eq("id", activityToUpdate.id)
      .single();

    await user.client
      .schema("app_provider")
      .from("activity")
      .update({ created_at: new Date("2020-01-01").toISOString() })
      .eq("id", activityToUpdate.id);

    const { data: checkActivity } = await user.client
      .schema("app_provider")
      .from("activity")
      .select()
      .eq("id", activityToUpdate.id)
      .single();

    if (permissions.activity.update) {
      expect(checkActivity?.created_at).not.toBe(initialActivity?.created_at);
    } else {
      expect(checkActivity?.created_at).toBe(initialActivity?.created_at);
    }
  });

  test(`can delete: ${permissions.activity.delete}`, async () => {
    if (!user.client) throw new Error("User client is undefined");
    if (!activityToDelete.id) return;

    await user.client
      .schema("app_provider")
      .from("activity")
      .delete()
      .eq("id", activityToDelete.id);

    const { data: checkActivity } = await user.client
      .schema("app_provider")
      .from("activity")
      .select()
      .eq("id", activityToDelete.id)
      .single();

    if (permissions.activity.delete) {
      expect(checkActivity).toBeNull();
    } else {
      expect(checkActivity?.id).toBe(activityToDelete.id);
    }
  });
});
