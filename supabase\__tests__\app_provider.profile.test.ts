import { describe, test, expect } from "vitest";
import { mockProvider, mockProviderApplicant } from "./mocks/auth.user";
describe("app_provider.profile", () => {
  const provider = mockProvider();
  const providerApplicant = mockProviderApplicant();

  describe("slug validation using SLUG domain type", () => {
    test("accepts valid slug formats", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const validSlugs = [
        "user123",
        "test-user",
        "user-123",
        "a",
        "123",
        "user-name-123",
        "gaming-master",
        "pro-player-2024"
      ];

      for (const slug of validSlugs) {
        const { data, error } = await provider.client
          .schema("app_provider")
          .from("profile")
          .upsert({
            user_id: provider.data.id,
            slug: slug,
            bio: { en: "Test bio" }
          })
          .select()
          .single();

        expect(error).toBeNull();
        expect(data?.slug).toBe(slug);
      }
    });

    test("rejects invalid slug formats", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const invalidSlugs = [
        "User123", // uppercase
        "user_123", // underscore
        "user 123", // space
        "user@123", // special character
        "-user", // starts with hyphen
        "user-", // ends with hyphen
        "user--123", // double hyphen
        "user.123", // dot
        "user/123", // slash
        ""
      ];

      for (const slug of invalidSlugs) {
        const { error } = await provider.client
          .schema("app_provider")
          .from("profile")
          .upsert({
            user_id: provider.data.id,
            slug: slug,
            bio: { en: "Test bio" }
          });

        expect(error).not.toBeNull();
      }
    });

    test("enforces uniqueness", async () => {
      if (!provider.client || !provider.data || !providerApplicant.client || !providerApplicant.data)
        throw new Error("Providers not defined");

      const slug = "unique-provider";

      // First provider creates profile with slug
      const { error: firstError } = await provider.client
        .schema("app_provider")
        .from("profile")
        .upsert({
          user_id: provider.data.id,
          slug: slug,
          bio: { en: "First provider bio" }
        });

      expect(firstError).toBeNull();

      // Second provider tries to use same slug - should fail
      const { error: secondError } = await providerApplicant.client
        .schema("app_provider")
        .from("profile")
        .upsert({
          user_id: providerApplicant.data.id,
          slug: slug,
          bio: { en: "Second provider bio" }
        });

      expect(secondError).not.toBeNull();
      expect(secondError?.code).toBe("23505"); // unique_violation
    });
  });

  describe("bio validation", () => {
    test("accepts valid JSONB bio", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const validBios = [
        { en: "English bio" },
        { en: "English bio", tr: "Turkish bio" },
        { en: "English bio", tr: "Turkish bio", ko: "Korean bio", ja: "Japanese bio" },
        null // bio is optional
      ];

      for (const bio of validBios) {
        const { data, error } = await provider.client
          .schema("app_provider")
          .from("profile")
          .upsert({
            user_id: provider.data.id,
            slug: `test-provider-${Math.random().toString(36).substr(2, 9)}`,
            bio: bio
          })
          .select()
          .single();

        expect(error).toBeNull();
        expect(data?.bio).toEqual(bio);
      }
    });
  });

  describe("profile creation", () => {
    test("can create complete profile", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const profileData = {
        user_id: provider.data.id,
        slug: "complete-provider",
        bio: {
          en: "Professional gaming coach with 5+ years experience",
          tr: "5+ yıl deneyimli profesyonel oyun koçu"
        }
      };

      const { data, error } = await provider.client
        .schema("app_provider")
        .from("profile")
        .upsert(profileData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data?.user_id).toBe(profileData.user_id);
      expect(data?.slug).toBe(profileData.slug);
      expect(data?.bio).toEqual(profileData.bio);
    });

    test("can create minimal profile", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const profileData = {
        user_id: provider.data.id,
        slug: "minimal-provider",
        bio: null
      };

      const { data, error } = await provider.client
        .schema("app_provider")
        .from("profile")
        .upsert(profileData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data?.user_id).toBe(profileData.user_id);
      expect(data?.slug).toBe(profileData.slug);
      expect(data?.bio).toBeNull();
    });
  });

  describe("provider applicant permissions", () => {
    test("provider applicant can create profile", async () => {
      if (!providerApplicant.client || !providerApplicant.data)
        throw new Error("Provider applicant not defined");

      const { data, error } = await providerApplicant.client
        .schema("app_provider")
        .from("profile")
        .upsert({
          user_id: providerApplicant.data.id,
          slug: "applicant-profile",
          bio: { en: "Applicant bio" }
        })
        .select()
        .single();

      expect(error).toBeNull();
      expect(data?.user_id).toBe(providerApplicant.data.id);
      expect(data?.slug).toBe("applicant-profile");
    });
  });
});
