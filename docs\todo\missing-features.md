# Missing Features Comparison with epal.gg

This document outlines the features that are currently missing from the project when compared to the popular gaming companionship platform, epal.gg.

## Core Features

### 1. Advanced Skill-Based Matching

- **Description:** While the current system allows users to browse and select services, it lacks an advanced matching system that can connect users with ePals based on specific skills, playstyles, and interests.
- **epal.gg Equivalent:** Epal.gg has a sophisticated system for filtering and matching users with ePals who meet their specific gaming needs.

### 2. Community and Social Features

- **Description:** The platform is missing key community-building features that encourage user interaction and engagement beyond one-on-one services.
- **Missing Features:**
  - **Social Feed:** A timeline where users can post updates, share content, and interact with each other.
  - **User-Created Groups:** The ability for users to create and join groups based on games, interests, or other criteria.
  - **Forums:** Discussion boards for various topics.

#### Social Posts Suggestions

A social feed would be a central hub for community activity.

- **Post Types:**
  - **Text & Media:** Users can share text updates, images, and video clips.
  - **LFG (Looking for Group):** A structured post type for users to find teammates for specific games, including details like game, rank, and playstyle.
  - **Service Promotion:** Providers can create special, highlighted posts to announce new services or promotions.
- **Core Functionality:**
  - **Following System:** Users can follow each other to see their posts in a dedicated feed.
  - **Reactions & Comments:** Standard social features to drive interaction.
  - **Tagging:** Users can tag games (`#Valorant`) or other users (`@username`) in posts to increase visibility.

### 3. Enhanced ePal Profiles

- **Description:** The current ePal profiles are basic and could be enhanced to provide more detailed information and build more trust with potential clients.
- **Missing Features:**
  - **Verified Skills:** A system to verify and display specific skills on ePal profiles (e.g., "Top 1% in Valorant").
  - **"E-pro" Designation:** A special status for top-tier players or coaches to distinguish them from other ePals.

### 4. Streaming Integration

- **Description:** There is no integration with popular streaming platforms like Twitch or YouTube.
- **Missing Features:**
  - **Streamer Profiles:** Special profiles for streamers with links to their channels.
  - **Live Status:** Displaying when a streamer is live on their profile.
  - **Scheduling Tools:** Tools for streamers to schedule games and sessions with their fans.

### 5. Discord Integration

- **Description:** The platform lacks any integration with Discord, which is a primary communication tool for gamers. Epal.gg has an "ePal Finder" bot that allows users to find and match with ePals directly from their Discord servers.
- **Deeper Explanation & Suggestions:**
  - **Account Linking & Role Sync:**
    - Users should be able to link their Discord account to their platform profile via OAuth2.
    - This enables automatic role synchronization. When a user becomes a `provider` on the platform, they can be granted a "Provider" role in the official Discord server. This provides instant recognition and access to provider-only channels.
  - **ePal Finder Bot:**
    - A dedicated Discord bot that brings platform functionality into users' servers.
    - **Commands:**
      - `/find [game]`: The bot searches the platform for available providers for the specified game and displays a list of profiles.
      - `/profile [username]`: The bot fetches and displays a summary of a provider's profile, including their rating, services, and a link to their full profile.
      - `/set-notifications`: Users can opt-in to receive direct messages from the bot about order updates, new messages, etc.
  - **Server Notifications:**
    - A dedicated channel in the official Discord server can announce new providers or highlight top-rated services, driving traffic back to the platform.

### 6. Playlink (Linktree for Gamers)

- **Description:** There is no feature that allows gamers to create a single, shareable page with all their relevant links.
- **epal.gg Equivalent:** Epal.gg's "Playlink" feature provides a "Linktree for gamers."

## Monetization and Engagement

### 7. Gamification and Rewards

- **Description:** The platform could benefit from gamification features to increase user engagement and provide a sense of achievement.
- **Missing Features:**
  - **Achievements and Badges:** A system to reward users with achievements and badges for completing tasks or reaching milestones.
  - **Leaderboards:** Public leaderboards for various metrics (e.g., top-rated ePals, most active users).

#### Gamification and Badge Suggestions

- **Goal:** To motivate users, guide them through key actions, and create a more engaging experience.
- **Badge Categories & Examples:**
  - **Onboarding:**
    - `Welcome!` (First sign-in)
    - `Profile Pioneer` (Complete profile setup: bio, avatar, etc.)
    - `Service Starter` (Provider creates their first service)
  - **Provider Milestones:**
    - `First Order` (Complete first order)
    - `Rising Star` (10+ completed orders)
    - `Elite ePal` (100+ completed orders)
    - `Top Rated` (Maintain a 4.9+ average rating over 20+ reviews)
    - `Category Captain` (Be the top provider in a specific game category for a month)
  - **Customer Milestones:**
    - `First Purchase` (Complete first order)
    - `Patron` (Order from 5 different providers)
    - `Superfan` (Order from the same provider 5+ times)
  - **Community & Social:**
    - `First Review` (Leave a review after an order)
    - `Social Butterfly` (Make 10 social posts)
    - `Community Helper` (Receive 20+ likes on a single LFG post or helpful comment)

### 8. Gift and Tipping System

- **Description:** There is no way for users to send virtual gifts or tips to ePals outside of the formal ordering system. This is a common monetization feature on similar platforms.
- **Deeper Explanation & Suggestions:**
  - **Purpose:** Provides a direct, low-friction way for satisfied customers to show appreciation and increases provider earnings.
  - **Implementation:**
    - **Tipping:** A "Send a Tip" button on a provider's profile. A user can input a custom `soda_amount` to send directly to the provider. This can leverage the existing `app_transaction.transfer` table and its `handle_new_transfer` trigger.
    - **Gifts:** Pre-defined virtual items (e.g., a "Rose" for 10 soda, a "Game Controller" for 50 soda) that users can purchase and send. These would also trigger a `transfer` of the corresponding `soda_amount`. The gift itself could be a visual flair on the provider's profile for a limited time.
  - **UI/UX:**
    - When a gift is sent, a public notification could appear on the provider's profile feed, creating social proof and encouraging others to do the same.

## Quality of Life

### 9. Instant Matching ("Play Now")

- **Description:** The platform lacks a feature to quickly find an available ePal for a specific game without going through the full browsing and ordering process
