import { z } from "zod";

// Base notification schema
export const BaseNotificationSchema = z.object({
  id: z.string().uuid(),
  type: z.string(),
  recipient_id: z.string().uuid(),
  title_key: z.string(),
  message_key: z.string(),
  data: z.record(z.any()),
  action_url: z.string().optional().nullable(),
  is_read: z.boolean().default(false),
  created_at: z.string(),
  updated_at: z.string().optional(),
});

// Account notification data schemas
export const WelcomeNotificationDataSchema = z.object({
  user_id: z.string().uuid(),
  email: z.string().email(),
});

export const KycStatusChangeDataSchema = z.object({
  kyc_id: z.string().uuid(),
  old_status: z.string().nullable(),
  new_status: z.string(),
});

export const UserBannedDataSchema = z.object({
  banned_by: z.string(),
  reason: z.string(),
  banned_at: z.string(),
});

export const UserUnbannedDataSchema = z.object({
  unbanned_at: z.string(),
});

// Access control notification data schemas
export const RoleAssignedDataSchema = z.object({
  role_name: z.string(),
  assigned_at: z.string(),
});

export const RoleRemovedDataSchema = z.object({
  role_name: z.string(),
  removed_at: z.string(),
});

// Provider notification data schemas
export const ProviderApplicationStatusChangeDataSchema = z.object({
  application_id: z.string().uuid(),
  old_status: z.string().nullable(),
  new_status: z.string(),
});

export const NewOrderDataSchema = z.object({
  order_id: z.string().uuid(),
  customer_username: z.string(),
  service_name: z.string(),
  amount: z.number(),
});

export const OrderStatusChangeDataSchema = z.object({
  order_id: z.string().uuid(),
  provider_username: z.string().optional(),
  customer_username: z.string().optional(),
  service_name: z.string(),
  old_status: z.string(),
  new_status: z.string(),
});

// Transaction notification data schemas
export const DepositSuccessDataSchema = z.object({
  deposit_id: z.string().uuid(),
  amount: z.number(),
  currency: z.string(),
});

export const TransferReceivedDataSchema = z.object({
  transfer_id: z.string().uuid(),
  amount: z.number(),
  sender_username: z.string(),
});

export const WithdrawalStatusChangeDataSchema = z.object({
  withdrawal_id: z.string().uuid(),
  amount: z.number(),
  old_status: z.string(),
  new_status: z.string(),
});

export const EscrowStatusChangeDataSchema = z.object({
  escrow_id: z.string().uuid(),
  amount: z.number(),
  old_status: z.string(),
  new_status: z.string(),
  other_party: z.string(),
});

// Chat notification data schemas
export const NewConversationDataSchema = z.object({
  conversation_id: z.string().uuid(),
  initiator_username: z.string(),
});

export const NewMessageDataSchema = z.object({
  conversation_id: z.string().uuid(),
  message_id: z.string().uuid(),
  sender_username: z.string(),
  content_preview: z.string(),
});

// Support notification data schemas
export const TicketCreatedDataSchema = z.object({
  ticket_id: z.string().uuid(),
  title: z.string(),
  type: z.string(),
});

export const TicketStatusChangeDataSchema = z.object({
  ticket_id: z.string().uuid(),
  title: z.string(),
  old_status: z.string(),
  new_status: z.string(),
});

export const TicketCommentDataSchema = z.object({
  ticket_id: z.string().uuid(),
  comment_id: z.string().uuid(),
  ticket_title: z.string(),
  commenter_username: z.string(),
  content_preview: z.string(),
});

export const FlagCreatedDataSchema = z.object({
  flag_id: z.string().uuid(),
  reason: z.string(),
  flagged_id: z.string().uuid(),
});

// Notification type to schema mapping
export const NotificationDataSchemas = {
  "account.welcome": WelcomeNotificationDataSchema,
  "account.kyc.status_change": KycStatusChangeDataSchema,
  "account.banned": UserBannedDataSchema,
  "account.unbanned": UserUnbannedDataSchema,
  "access.role.assigned": RoleAssignedDataSchema,
  "access.role.removed": RoleRemovedDataSchema,
  "provider.application.status_change": ProviderApplicationStatusChangeDataSchema,
  "provider.order.new": NewOrderDataSchema,
  "provider.order.status_change": OrderStatusChangeDataSchema,
  "transaction.deposit.success": DepositSuccessDataSchema,
  "transaction.transfer.received": TransferReceivedDataSchema,
  "transaction.withdrawal.status_change": WithdrawalStatusChangeDataSchema,
  "transaction.escrow.status_change": EscrowStatusChangeDataSchema,
  "chat.conversation.new": NewConversationDataSchema,
  "chat.message.new": NewMessageDataSchema,
  "support.ticket.created": TicketCreatedDataSchema,
  "support.ticket.status_change": TicketStatusChangeDataSchema,
  "support.ticket.comment": TicketCommentDataSchema,
  "support.flag.created": FlagCreatedDataSchema,
} as const;

export type NotificationType = keyof typeof NotificationDataSchemas;

// Helper function to validate notification data
export function validateNotificationData<T extends NotificationType>(
  type: T,
  data: unknown
): z.infer<typeof NotificationDataSchemas[T]> {
  const schema = NotificationDataSchemas[type];
  return schema.parse(data);
}

// Helper function to validate complete notification
export function validateNotification(notification: unknown) {
  return BaseNotificationSchema.parse(notification);
}
