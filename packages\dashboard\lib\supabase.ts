import { createClient as createServiceClient } from "shared/lib/supabase/service";

// Create specialized clients for different schemas
export const catalogClient = createServiceClient().schema("app_catalog");
export const accountClient = createServiceClient().schema("app_account");
export const transactionClient = createServiceClient().schema("app_transaction");
export const providerClient = createServiceClient().schema("app_provider");
export const supportClient = createServiceClient().schema("app_support");
export const accessClient = createServiceClient().schema("app_access");
export const chatClient = createServiceClient().schema("app_chat");
export const wikiClient = createServiceClient().schema("app_wiki");
