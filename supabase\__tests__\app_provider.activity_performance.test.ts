import { describe, test, expect } from "vitest";
import { mockOrder } from "./mocks/app_provider.order";
import { mockReview } from "./mocks/app_provider.review";
import { mockService } from "./mocks/app_provider.service";
import { serviceClient } from "./utils/client";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
const customer = mockCustomer();
const provider = mockProvider();
const admin = mockAdmin();
const service = mockService(provider);

describe("update activity performance when", () => {
  describe("an order is completed and archived", () => {
    const order = mockOrder({
      customer,
      provider,
      service,
      status: "completed"
    });

    test("completed_orders is incremented", async () => {
      if (!order.id) throw new Error("Order ID is undefined");
      if (!service.selectedActivityId)
        throw new Error("Activity ID is undefined");

      // Get initial completed_orders count for the activity
      const initialPerformance = await serviceClient
        .schema("app_provider")
        .from("activity_performance")
        .select("completed_orders")
        .eq("activity_id", service.selectedActivityId)
        .single();

      const initialCompletedOrders =
        initialPerformance.data?.completed_orders || 0;

      // Delete the order to trigger archiving and activity performance update
      await serviceClient
        .schema("app_provider")
        .from("order")
        .delete()
        .eq("id", order.id);

      // Get final completed_orders count for the activity
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("activity_performance")
        .select("completed_orders")
        .eq("activity_id", service.selectedActivityId)
        .single();

      const finalCompletedOrders = finalPerformance.data?.completed_orders || 0;

      // Assert that completed_orders increased by 1
      expect(finalCompletedOrders).toBe(initialCompletedOrders + 1);
    });
  });

  describe("a review is approved", () => {
    const order = mockOrder({
      customer,
      provider,
      service,
      status: "completed"
    });
    mockReview({ order, customer, admin, approve: true });

    test("rating is updated", async () => {
      if (!service.selectedActivityId)
        throw new Error("Activity ID is undefined");

      // Get final rating for the activity
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("activity_performance")
        .select("rating")
        .eq("activity_id", service.selectedActivityId)
        .single();

      const finalRating = finalPerformance.data?.rating || 0;

      // Assert that rating is 5 (since one 5-star review was approved)
      expect(finalRating).toBe(5);
    });

    test("reviews count is incremented", async () => {
      if (!service.selectedActivityId)
        throw new Error("Activity ID is undefined");

      // Get final reviews count for the activity
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("activity_performance")
        .select("reviews")
        .eq("activity_id", service.selectedActivityId)
        .single();

      const finalReviews = finalPerformance.data?.reviews || 0;

      // Assert that reviews increased by 1
      expect(finalReviews).toBe(1);
    });
  });

  describe("multiple reviews are approved for the same activity", () => {
    const order1 = mockOrder({
      customer,
      provider,
      service,
      status: "completed"
    });
    const order2 = mockOrder({
      customer,
      provider,
      service,
      status: "completed"
    });

    mockReview({
      order: order1,
      customer,
      admin,
      approve: true,
      review: { rating: 5 }
    });
    mockReview({
      order: order2,
      customer,
      admin,
      approve: true,
      review: { rating: 3 }
    });

    test("average rating is calculated correctly", async () => {
      if (!service.selectedActivityId)
        throw new Error("Activity ID is undefined");

      // Get final rating for the activity
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("activity_performance")
        .select("rating")
        .eq("activity_id", service.selectedActivityId)
        .single();

      const finalRating = finalPerformance.data?.rating || 0;

      // Assert that rating is 4 (average of 5 and 3)
      expect(finalRating).toBe(4);
    });

    test("reviews count reflects total approved reviews", async () => {
      if (!service.selectedActivityId)
        throw new Error("Activity ID is undefined");

      // Get final reviews count for the activity
      const finalPerformance = await serviceClient
        .schema("app_provider")
        .from("activity_performance")
        .select("reviews")
        .eq("activity_id", service.selectedActivityId)
        .single();

      const finalReviews = finalPerformance.data?.reviews || 0;

      // Assert that reviews count is 2
      expect(finalReviews).toBe(2);
    });
  });
});
