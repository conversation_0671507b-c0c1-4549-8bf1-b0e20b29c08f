"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-query@5.79.0_react@19.1.0";
exports.ids = ["vendor-chunks/@tanstack+react-query@5.79.0_react@19.1.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ // src/IsRestoringProvider.ts\n\nvar IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nvar useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=IsRestoringProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytyZWFjdC1xdWVyeUA1Ljc5LjBfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnkvYnVpbGQvbW9kZXJuL0lzUmVzdG9yaW5nUHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUN1QjtBQUV2QixJQUFNLG1DQUEyQixpREFBYyxLQUFLO0FBRTdDLElBQU0saUJBQWlCLElBQVksOENBQVcsa0JBQWtCO0FBQ2hFLElBQU0sc0JBQXNCLG1CQUFtQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXHNyY1xcSXNSZXN0b3JpbmdQcm92aWRlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5jb25zdCBJc1Jlc3RvcmluZ0NvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KGZhbHNlKVxuXG5leHBvcnQgY29uc3QgdXNlSXNSZXN0b3JpbmcgPSAoKSA9PiBSZWFjdC51c2VDb250ZXh0KElzUmVzdG9yaW5nQ29udGV4dClcbmV4cG9ydCBjb25zdCBJc1Jlc3RvcmluZ1Byb3ZpZGVyID0gSXNSZXN0b3JpbmdDb250ZXh0LlByb3ZpZGVyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"QueryClientProvider.useEffect\": ()=>{\n            client.mount();\n            return ({\n                \"QueryClientProvider.useEffect\": ()=>{\n                    client.unmount();\n                }\n            })[\"QueryClientProvider.useEffect\"];\n        }\n    }[\"QueryClientProvider.useEffect\"], [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytyZWFjdC1xdWVyeUA1Ljc5LjBfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnkvYnVpbGQvbW9kZXJuL1F1ZXJ5Q2xpZW50UHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3VCO0FBdUNuQjtBQW5DRyxJQUFNLG1DQUEyQixpREFDdEM7QUFHSyxJQUFNLGlCQUFpQixDQUFDO0lBQzdCLE1BQU0sU0FBZSw4Q0FBVyxrQkFBa0I7SUFFbEQsSUFBSSxhQUFhO1FBQ2YsT0FBTztJQUNUO0lBRUEsSUFBSSxDQUFDLFFBQVE7UUFDWCxNQUFNLElBQUksTUFBTSx3REFBd0Q7SUFDMUU7SUFFQSxPQUFPO0FBQ1Q7QUFPTyxJQUFNLHNCQUFzQixDQUFDLEVBQ2xDLFFBQ0EsVUFDRjtJQUNRO3lDQUFVO1lBQ2QsT0FBTyxNQUFNO1lBQ2I7aURBQU87b0JBQ0wsT0FBTyxRQUFRO2dCQUNqQjs7UUFDRjt3Q0FBRztRQUFDLE1BQU07S0FBQztJQUVYLE9BQ0UsdUVBQUMsbUJBQW1CLFVBQW5CO1FBQTRCLE9BQU87UUFDakM7SUFBQSxDQUNIO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxzcmNcXFF1ZXJ5Q2xpZW50UHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmltcG9ydCB0eXBlIHsgUXVlcnlDbGllbnQgfSBmcm9tICdAdGFuc3RhY2svcXVlcnktY29yZSdcblxuZXhwb3J0IGNvbnN0IFF1ZXJ5Q2xpZW50Q29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQ8UXVlcnlDbGllbnQgfCB1bmRlZmluZWQ+KFxuICB1bmRlZmluZWQsXG4pXG5cbmV4cG9ydCBjb25zdCB1c2VRdWVyeUNsaWVudCA9IChxdWVyeUNsaWVudD86IFF1ZXJ5Q2xpZW50KSA9PiB7XG4gIGNvbnN0IGNsaWVudCA9IFJlYWN0LnVzZUNvbnRleHQoUXVlcnlDbGllbnRDb250ZXh0KVxuXG4gIGlmIChxdWVyeUNsaWVudCkge1xuICAgIHJldHVybiBxdWVyeUNsaWVudFxuICB9XG5cbiAgaWYgKCFjbGllbnQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIFF1ZXJ5Q2xpZW50IHNldCwgdXNlIFF1ZXJ5Q2xpZW50UHJvdmlkZXIgdG8gc2V0IG9uZScpXG4gIH1cblxuICByZXR1cm4gY2xpZW50XG59XG5cbmV4cG9ydCB0eXBlIFF1ZXJ5Q2xpZW50UHJvdmlkZXJQcm9wcyA9IHtcbiAgY2xpZW50OiBRdWVyeUNsaWVudFxuICBjaGlsZHJlbj86IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgY29uc3QgUXVlcnlDbGllbnRQcm92aWRlciA9ICh7XG4gIGNsaWVudCxcbiAgY2hpbGRyZW4sXG59OiBRdWVyeUNsaWVudFByb3ZpZGVyUHJvcHMpOiBSZWFjdC5KU1guRWxlbWVudCA9PiB7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2xpZW50Lm1vdW50KClcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xpZW50LnVubW91bnQoKVxuICAgIH1cbiAgfSwgW2NsaWVudF0pXG5cbiAgcmV0dXJuIChcbiAgICA8UXVlcnlDbGllbnRDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXtjbGllbnR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvUXVlcnlDbGllbnRDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ // src/QueryErrorResetBoundary.tsx\n\n\nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue());\nvar useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"QueryErrorResetBoundary.useState\": ()=>createValue()\n    }[\"QueryErrorResetBoundary.useState\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryErrorResetBoundaryContext.Provider, {\n        value,\n        children: typeof children === \"function\" ? children(value) : children\n    });\n};\n //# sourceMappingURL=QueryErrorResetBoundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytyZWFjdC1xdWVyeUA1Ljc5LjBfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnkvYnVpbGQvbW9kZXJuL1F1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQ3VCO0FBa0RuQjtBQXJDSixTQUFTLGNBQTRDO0lBQ25ELElBQUksVUFBVTtJQUNkLE9BQU87UUFDTCxZQUFZO1lBQ1YsVUFBVTtRQUNaO1FBQ0EsT0FBTztZQUNMLFVBQVU7UUFDWjtRQUNBLFNBQVM7WUFDUCxPQUFPO1FBQ1Q7SUFDRjtBQUNGO0FBRUEsSUFBTSwrQ0FBdUMsaURBQWMsWUFBWSxDQUFDO0FBSWpFLElBQU0sNkJBQTZCLElBQ2xDLDhDQUFXLDhCQUE4QjtBQVkxQyxJQUFNLDBCQUEwQixDQUFDLEVBQ3RDLFVBQ0Y7SUFDRSxNQUFNLENBQUMsS0FBSyxJQUFVOzRDQUFTLElBQU0sWUFBWSxDQUFDOztJQUNsRCxPQUNFLHVFQUFDLCtCQUErQixVQUEvQjtRQUF3QztRQUN0QyxpQkFBTyxhQUFhLGFBQWEsU0FBUyxLQUFLLElBQUk7SUFBQSxDQUN0RDtBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcc3JjXFxRdWVyeUVycm9yUmVzZXRCb3VuZGFyeS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcblxuLy8gQ09OVEVYVFxuZXhwb3J0IHR5cGUgUXVlcnlFcnJvclJlc2V0RnVuY3Rpb24gPSAoKSA9PiB2b2lkXG5leHBvcnQgdHlwZSBRdWVyeUVycm9ySXNSZXNldEZ1bmN0aW9uID0gKCkgPT4gYm9vbGVhblxuZXhwb3J0IHR5cGUgUXVlcnlFcnJvckNsZWFyUmVzZXRGdW5jdGlvbiA9ICgpID0+IHZvaWRcblxuZXhwb3J0IGludGVyZmFjZSBRdWVyeUVycm9yUmVzZXRCb3VuZGFyeVZhbHVlIHtcbiAgY2xlYXJSZXNldDogUXVlcnlFcnJvckNsZWFyUmVzZXRGdW5jdGlvblxuICBpc1Jlc2V0OiBRdWVyeUVycm9ySXNSZXNldEZ1bmN0aW9uXG4gIHJlc2V0OiBRdWVyeUVycm9yUmVzZXRGdW5jdGlvblxufVxuXG5mdW5jdGlvbiBjcmVhdGVWYWx1ZSgpOiBRdWVyeUVycm9yUmVzZXRCb3VuZGFyeVZhbHVlIHtcbiAgbGV0IGlzUmVzZXQgPSBmYWxzZVxuICByZXR1cm4ge1xuICAgIGNsZWFyUmVzZXQ6ICgpID0+IHtcbiAgICAgIGlzUmVzZXQgPSBmYWxzZVxuICAgIH0sXG4gICAgcmVzZXQ6ICgpID0+IHtcbiAgICAgIGlzUmVzZXQgPSB0cnVlXG4gICAgfSxcbiAgICBpc1Jlc2V0OiAoKSA9PiB7XG4gICAgICByZXR1cm4gaXNSZXNldFxuICAgIH0sXG4gIH1cbn1cblxuY29uc3QgUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dChjcmVhdGVWYWx1ZSgpKVxuXG4vLyBIT09LXG5cbmV4cG9ydCBjb25zdCB1c2VRdWVyeUVycm9yUmVzZXRCb3VuZGFyeSA9ICgpID0+XG4gIFJlYWN0LnVzZUNvbnRleHQoUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlDb250ZXh0KVxuXG4vLyBDT01QT05FTlRcblxuZXhwb3J0IHR5cGUgUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlGdW5jdGlvbiA9IChcbiAgdmFsdWU6IFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5VmFsdWUsXG4pID0+IFJlYWN0LlJlYWN0Tm9kZVxuXG5leHBvcnQgaW50ZXJmYWNlIFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5UHJvcHMge1xuICBjaGlsZHJlbjogUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlGdW5jdGlvbiB8IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgY29uc3QgUXVlcnlFcnJvclJlc2V0Qm91bmRhcnkgPSAoe1xuICBjaGlsZHJlbixcbn06IFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5UHJvcHMpID0+IHtcbiAgY29uc3QgW3ZhbHVlXSA9IFJlYWN0LnVzZVN0YXRlKCgpID0+IGNyZWF0ZVZhbHVlKCkpXG4gIHJldHVybiAoXG4gICAgPFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5Q29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge3R5cGVvZiBjaGlsZHJlbiA9PT0gJ2Z1bmN0aW9uJyA/IGNoaWxkcmVuKHZhbHVlKSA6IGNoaWxkcmVufVxuICAgIDwvUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.79.0/node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ // src/errorBoundaryUtils.ts\n\n\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useClearResetErrorBoundary.useEffect\": ()=>{\n            errorResetBoundary.clearReset();\n        }\n    }[\"useClearResetErrorBoundary.useEffect\"], [\n        errorResetBoundary\n    ]);\n};\nvar getHasError = ({ result, errorResetBoundary, throwOnError, query, suspense })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(throwOnError, [\n        result.error,\n        query\n    ]));\n};\n //# sourceMappingURL=errorBoundaryUtils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/suspense.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/suspense.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThrowOnError: () => (/* binding */ defaultThrowOnError),\n/* harmony export */   ensureSuspenseTimers: () => (/* binding */ ensureSuspenseTimers),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\n// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  if (defaultedOptions.suspense) {\n    const clamp = (value) => value === \"static\" ? value : Math.max(value ?? 1e3, 1e3);\n    const originalStaleTime = defaultedOptions.staleTime;\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => clamp(originalStaleTime(...args)) : clamp(originalStaleTime);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\n\n//# sourceMappingURL=suspense.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/suspense.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.79.0/node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.79.0/node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./IsRestoringProvider.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ // src/useBaseQuery.ts\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const isRestoring = (0,_IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedOptions = client.defaultQueryOptions(options);\n    client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n    if (true) {\n        if (!defaultedOptions.queryFn) {\n            console.error(`[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`);\n        }\n    }\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useBaseQuery.useState\": ()=>new Observer(client, defaultedOptions)\n    }[\"useBaseQuery.useState\"]);\n    const result = observer.getOptimisticResult(defaultedOptions);\n    const shouldSubscribe = !isRestoring && options.subscribed !== false;\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useBaseQuery.useSyncExternalStore.useCallback\": (onStoreChange)=>{\n            const unsubscribe = shouldSubscribe ? observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange)) : _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop;\n            observer.updateResult();\n            return unsubscribe;\n        }\n    }[\"useBaseQuery.useSyncExternalStore.useCallback\"], [\n        observer,\n        shouldSubscribe\n    ]), {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"], {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useBaseQuery.useEffect\": ()=>{\n            observer.setOptions(defaultedOptions);\n        }\n    }[\"useBaseQuery.useEffect\"], [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash),\n        suspense: defaultedOptions.suspense\n    })) {\n        throw result.error;\n    }\n    ;\n    client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n    if (defaultedOptions.experimental_prefetchInRender && !_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.isServer && (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n        const promise = isNewCacheEntry ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary) : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise;\n        promise?.catch(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop).finally(()=>{\n            observer.updateResult();\n        });\n    }\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useQuery.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useQuery.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.79.0/node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ // src/useQuery.ts\n\n\nfunction useQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.QueryObserver, queryClient);\n}\n //# sourceMappingURL=useQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.79.0_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useQuery.js\n");

/***/ })

};
;