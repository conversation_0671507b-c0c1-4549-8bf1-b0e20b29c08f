import { dbClient } from "./client";

export async function resetRateLimits() {
  await dbClient.query("DELETE FROM app_core.rate_limit");
}

export async function getRateLimit() {
  const result = await dbClient.query(
    "SELECT max_requests_per_minute FROM app_core.config WHERE id = TRUE"
  );
  return result.rows[0]?.max_requests_per_minute ?? 0;
}

export async function setRateLimit(maxRequestsPerMinute: number) {
  await dbClient.query(
    "UPDATE app_core.config SET max_requests_per_minute = $1 WHERE id = TRUE",
    [maxRequestsPerMinute]
  );
}
