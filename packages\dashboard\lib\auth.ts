import { createClient } from "shared/lib/supabase/client";
import { createClient as createServiceClient } from "shared/lib/supabase/service";

export async function checkAdminAccess(userId: string, capability: string) {
  const serviceClient = createServiceClient();

  const { data, error } = await serviceClient
    .schema("app_access")
    .rpc("has_capability", {
      p_user_id: userId,
      p_capability: capability
    });

  if (error) throw error;
  return data;
}

export async function requireAdminRole(requiredCapabilities: string[]) {
  const supabase = createClient();
  const {
    data: { user }
  } = await supabase.auth.getUser();

  if (!user) throw new Error("Unauthorized");

  for (const capability of requiredCapabilities) {
    const hasAccess = await checkAdminAccess(user.id, capability);
    if (!hasAccess) throw new Error(`Missing capability: ${capability}`);
  }

  return user;
}
