"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "shared/lib/supabase/client";
import { checkAdminAccess } from "@/lib/auth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredCapabilities: string[];
}

export function ProtectedRoute({
  children,
  requiredCapabilities
}: ProtectedRouteProps) {
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function checkAuth() {
      try {
        const supabase = createClient();
        const {
          data: { user }
        } = await supabase.auth.getUser();

        if (!user) {
          router.push("/login");
          return;
        }

        const hasAllCapabilities = await Promise.all(
          requiredCapabilities.map((cap) => checkAdminAccess(user.id, cap))
        );

        if (hasAllCapabilities.every(Boolean)) {
          setIsAuthorized(true);
        } else {
          router.push("/unauthorized");
        }
      } catch (error) {
        router.push("/login");
      }
    }

    checkAuth();
  }, [requiredCapabilities, router]);

  if (isAuthorized === null) {
    return <div>Loading...</div>;
  }

  return isAuthorized ? <>{children}</> : null;
}
