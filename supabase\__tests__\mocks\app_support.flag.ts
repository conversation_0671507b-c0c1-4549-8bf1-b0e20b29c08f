import { afterAll, afterEach, beforeAll, beforeEach, expect } from "vitest";
import { MockUser } from "./auth.user";
import { Database } from "shared/lib/supabase/database";
import { serviceClient } from "../utils/client";

export type MockFlag = {
  id?: string;
};

type MockFlagParams = {
  flagger: MockUser;
  flagged: MockUser;
  flag?: Partial<Database["app_support"]["Functions"]["create_flag"]["Args"]>;
};

async function createFlag(
  { flagger, flagged, flag }: MockFlagParams,
  mockFlag: MockFlag
) {
  if (!flagger.client) throw new Error("Flagger client is undefined");
  if (!flagged.data) throw new Error("Flagged user data is undefined");

  const { data, error } = await flagger.client
    .schema("app_support")
    .rpc("create_flag", {
      p_flagged_id: flagged.data.id,
      p_reason: "Test reason",
      ...(flag ?? {})
    });

  expect(error).toBeNull();
  mockFlag.id = data?.id;
}

async function cleanupFlag(mockFlag: MockFlag) {
  if (!mockFlag.id) return;

  await serviceClient
    .schema("app_support")
    .from("flag")
    .delete()
    .eq("id", mockFlag.id);
}

export function mockFlag(params: MockFlagParams) {
  const flag: MockFlag = {};

  beforeAll(async () => {
    await createFlag(params, flag);
  });

  afterAll(async () => {
    await cleanupFlag(flag);
  });

  return flag;
}

export function mockFlagEach(params: MockFlagParams) {
  const flag: MockFlag = {};

  beforeEach(async () => {
    await createFlag(params, flag);
  });

  afterEach(async () => {
    await cleanupFlag(flag);
  });

  return flag;
}
