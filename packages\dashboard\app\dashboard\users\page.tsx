"use client";

import { useQuery } from "@tanstack/react-query";
import { accountClient } from "@/lib/supabase";
import Link from "next/link";
import { Users, BarChart3, Shield, ArrowRight } from "lucide-react";

export default function UsersOverviewPage() {
  const { data: userStats, isLoading } = useQuery({
    queryKey: ["user-overview-stats"],
    queryFn: async () => {
      const [
        { count: totalUsers },
        { count: bannedUsers },
        { count: newUsersToday }
      ] = await Promise.all([
        accountClient
          .from("profile")
          .select("*", { count: "exact", head: true }),
        accountClient
          .from("banned_user")
          .select("*", { count: "exact", head: true }),
        accountClient
          .from("profile")
          .select("*", { count: "exact", head: true })
          .gte("join_date", new Date().toISOString().split("T")[0])
      ]);

      return {
        totalUsers: totalUsers || 0,
        bannedUsers: bannedUsers || 0,
        activeUsers: (totalUsers || 0) - (bannedUsers || 0),
        newUsersToday: newUsersToday || 0
      };
    }
  });

  const userSections = [
    {
      title: "User Analytics",
      description:
        "View user growth, engagement metrics, and platform statistics",
      icon: BarChart3,
      href: "/dashboard/users/analytics",
      stats: `${userStats?.totalUsers || 0} total users`,
      color: "bg-blue-500"
    },
    {
      title: "User Management",
      description:
        "Manage user accounts, permissions, and perform moderation actions",
      icon: Users,
      href: "/dashboard/users/management",
      stats: `${userStats?.activeUsers || 0} active users`,
      color: "bg-green-500"
    },
    {
      title: "Moderation",
      description:
        "Review flagged content, handle reports, and manage user behavior",
      icon: Shield,
      href: "/dashboard/users/moderation",
      stats: `${userStats?.bannedUsers || 0} banned users`,
      color: "bg-red-500"
    }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading user overview...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage users, analyze growth, and handle moderation across the
          platform
        </p>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Users
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {userStats?.totalUsers.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Users
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {userStats?.activeUsers.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    New Today
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {userStats?.newUsersToday.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                  <Shield className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Banned Users
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {userStats?.bannedUsers.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Management Sections */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {userSections.map((section) => {
          const Icon = section.icon;
          return (
            <Link
              key={section.title}
              href={section.href}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400 transition-colors"
            >
              <div>
                <span
                  className={`rounded-lg inline-flex p-3 ${section.color} text-white ring-4 ring-white`}
                >
                  <Icon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium flex items-center justify-between">
                  <span>{section.title}</span>
                  <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600" />
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  {section.description}
                </p>
                <div className="mt-3">
                  <span className="text-sm font-medium text-gray-900">
                    {section.stats}
                  </span>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Users className="h-4 w-4 mr-2" />
              Export User Data
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <BarChart3 className="h-4 w-4 mr-2" />
              Generate Report
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Shield className="h-4 w-4 mr-2" />
              Review Flags
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
