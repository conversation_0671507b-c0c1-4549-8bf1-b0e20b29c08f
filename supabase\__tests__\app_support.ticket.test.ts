import { test, expect, describe, beforeEach } from "vitest";
import {
  mockAdmin,
  mockCustomer,
  mockProvider,
  mockSupportAgent
} from "./mocks/auth.user";
import { mockOrder } from "./mocks/app_provider.order";
import { mockService } from "./mocks/app_provider.service";
import { serviceClient } from "./utils/client";

const customer = mockCustomer();
const customer2 = mockCustomer();
const provider = mockProvider();
const supportAgent = mockSupportAgent();
const admin = mockAdmin();
const service = mockService(provider);

describe("Ticket Creation", () => {
  test("should create a report ticket successfully", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { data, error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Test Report Ticket",
        p_problem_description: "This is a test report ticket",
        p_type: "report"
      });

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(data?.title).toBe("Test Report Ticket");
    expect(data?.type).toBe("report");
    expect(data?.status).toBe("open");
    expect(data?.disputed_order_id).toBeNull();
  });

  test("should create a dispute ticket with valid order ID", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!service.providerServiceId) throw new Error("Service ID is undefined");
    if (!service.providerServiceModifierId)
      throw new Error("Service modifier ID is undefined");

    // Create an order first
    const submitOrder = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: service.providerServiceId,
        p_unit_count: 1,
        p_service_modifier_ids: [service.providerServiceModifierId]
      });

    expect(submitOrder.data?.id).toBeDefined();
    const orderId = submitOrder.data!.id;

    const { data, error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Dispute Order",
        p_problem_description: "I want to dispute this order",
        p_type: "dispute",
        p_disputed_order_id: orderId
      });

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(data?.type).toBe("dispute");
    expect(data?.disputed_order_id).toBe(orderId);
  });

  test("should fail to create dispute ticket without order ID", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Invalid Dispute",
        p_problem_description: "This should fail",
        p_type: "dispute"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain(
      "Disputed order ID is required for dispute tickets"
    );
  });

  test("should fail to create dispute ticket with invalid order ID", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Invalid Dispute",
        p_problem_description: "This should fail",
        p_type: "dispute",
        p_disputed_order_id: "00000000-0000-0000-0000-000000000000"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain(
      "Invalid disputed order ID or user not involved in the order"
    );
  });

  test("should fail to create report ticket with order ID", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const order = mockOrder({
      status: "completed",
      customer,
      provider,
      service
    });

    const { error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Invalid Report",
        p_problem_description: "This should fail",
        p_type: "report",
        p_disputed_order_id: order.id
      });

    expect(error).toBeDefined();
    if (error?.message) {
      expect(error.message).toContain(
        "Disputed order ID should only be provided for dispute tickets"
      );
    }
  });

  test("should enforce 10 active ticket limit", async () => {
    if (!customer2.client) throw new Error("Customer2 client is undefined");

    // Create 10 tickets
    for (let i = 0; i < 10; i++) {
      const { error } = await customer2.client
        .schema("app_support")
        .rpc("create_ticket", {
          p_title: `Test Ticket ${i + 1}`,
          p_problem_description: `Test description ${i + 1}`,
          p_type: "report"
        });
      expect(error).toBeNull();
    }

    // 11th ticket should fail
    const { error } = await customer2.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "11th Ticket",
        p_problem_description: "This should fail",
        p_type: "report"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain(
      "Maximum active ticket limit (10) reached"
    );
  });
});

describe("Ticket Status Updates", () => {
  let ticketId: string | undefined;

  beforeEach(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { data } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Test Status Ticket",
        p_problem_description: "For testing status updates",
        p_type: "report"
      });
    ticketId = data?.id;
  });

  test("admin should be able to update ticket status", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!ticketId) throw new Error("Ticket ID is undefined");

    const { data, error } = await admin.client
      .schema("app_support")
      .rpc("update_ticket_status", {
        p_ticket_id: ticketId,
        p_new_status: "closed",
        p_resolution_notes: "Closed by admin"
      });

    expect(error).toBeNull();
    expect(data?.status).toBe("closed");
    expect(data?.resolution_notes).toBe("Closed by admin");
  });

  test("customer should not be able to update ticket status", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!ticketId) throw new Error("Ticket ID is undefined");

    const { error } = await customer.client
      .schema("app_support")
      .rpc("update_ticket_status", {
        p_ticket_id: ticketId,
        p_new_status: "closed"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain(
      "Insufficient permissions to update ticket status"
    );
  });

  test("support agent should be able to update assigned ticket status", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!ticketId) throw new Error("Ticket ID is undefined");
    if (!supportAgent.client)
      throw new Error("Support agent client is undefined");
    if (!supportAgent.data?.id)
      throw new Error("Support agent ID is undefined");

    // First assign the ticket to support agent
    await admin.client.schema("app_support").rpc("assign_ticket", {
      p_ticket_id: ticketId,
      p_assigned_to: supportAgent.data.id
    });

    // Support agent should be able to update status
    const { data, error } = await supportAgent.client
      .schema("app_support")
      .rpc("update_ticket_status", {
        p_ticket_id: ticketId,
        p_new_status: "closed"
      });

    expect(error).toBeNull();
    expect(data?.status).toBe("closed");
  });
});

describe("Order Dispute Integration", () => {
  test("should create a dispute ticket when order status is updated to 'in_dispute'", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!provider.client) throw new Error("Provider client is undefined");
    if (!service.providerServiceId) throw new Error("Service ID is undefined");
    if (!service.providerServiceModifierId)
      throw new Error("Service modifier ID is undefined");
    // Create and complete an order first
    const submitOrder = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: service.providerServiceId,
        p_unit_count: 1,
        p_service_modifier_ids: [service.providerServiceModifierId]
      });
    expect(submitOrder.data?.id).toBeDefined();
    const orderId = submitOrder.data!.id;
    // Provider accepts the order
    await provider.client.schema("app_provider").rpc("update_order_status", {
      p_order_id: orderId,
      p_new_status: "accepted"
    });
    // Provider completes the order
    await provider.client.schema("app_provider").rpc("update_order_status", {
      p_order_id: orderId,
      p_new_status: "completed"
    });
    // Customer disputes the order with description
    const disputeDescription = "The service was not delivered as promised";
    const disputeUpdate = await customer.client
      .schema("app_provider")
      .rpc("update_order_status", {
        p_order_id: orderId,
        p_new_status: "in_dispute",
        p_dispute_description: disputeDescription
      });
    expect(disputeUpdate.error).toBeNull();
    expect(disputeUpdate.data?.order_status).toBe("in_dispute");
    // Check if a dispute ticket was created
    const ticketQuery = await customer.client
      .schema("app_support")
      .from("ticket")
      .select("*")
      .eq("disputed_order_id", orderId)
      .eq("type", "dispute")
      .single();
    expect(ticketQuery.error).toBeNull();
    expect(ticketQuery.data).toBeDefined();
    expect(ticketQuery.data?.type).toBe("dispute");
    expect(ticketQuery.data?.disputed_order_id).toBe(orderId);
    expect(ticketQuery.data?.problem_description).toBe(disputeDescription);
    expect(ticketQuery.data?.user_id).toBe(customer.data.id);
    expect(ticketQuery.data?.title).toContain("Order Dispute");
  });

  test("should fail to update order to 'in_dispute' without dispute description", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!provider.client) throw new Error("Provider client is undefined");
    if (!service.providerServiceId) throw new Error("Service ID is undefined");
    if (!service.providerServiceModifierId)
      throw new Error("Service modifier ID is undefined");
    // Create and complete an order first
    const submitOrder = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: service.providerServiceId,
        p_unit_count: 1,
        p_service_modifier_ids: [service.providerServiceModifierId]
      });
    expect(submitOrder.data?.id).toBeDefined();
    const orderId = submitOrder.data!.id;
    // Provider accepts the order
    await provider.client.schema("app_provider").rpc("update_order_status", {
      p_order_id: orderId,
      p_new_status: "accepted"
    });
    // Provider completes the order
    await provider.client.schema("app_provider").rpc("update_order_status", {
      p_order_id: orderId,
      p_new_status: "completed"
    });
    // Try to dispute the order without description
    const disputeUpdate = await customer.client
      .schema("app_provider")
      .rpc("update_order_status", {
        p_order_id: orderId,
        p_new_status: "in_dispute"
      });
    expect(disputeUpdate.error).toBeDefined();
    expect(disputeUpdate.error?.message).toContain(
      "Dispute description is required when changing order status to in_dispute"
    );

    // Delete the order manually because completed order with with disputable_until in the future is not deleted by the cron job
    await serviceClient
      .schema("app_provider")
      .from("order")
      .delete()
      .eq("id", orderId);
  });
});

describe("Ticket Assignment", () => {
  let ticketId: string | undefined;

  beforeEach(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { data } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Test Assignment Ticket",
        p_problem_description: "For testing assignments",
        p_type: "report"
      });
    ticketId = data?.id;
  });

  test("admin should be able to assign ticket to support agent", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!supportAgent.data?.id)
      throw new Error("Support agent ID is undefined");
    if (!ticketId) throw new Error("Ticket ID is undefined");

    const { data, error } = await admin.client
      .schema("app_support")
      .rpc("assign_ticket", {
        p_ticket_id: ticketId,
        p_assigned_to: supportAgent.data.id
      });

    expect(error).toBeNull();
    expect(data?.assigned_to).toBe(supportAgent.data.id);
  });

  test("should fail to assign ticket to user without support capabilities", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data?.id) throw new Error("Customer ID is undefined");
    if (!ticketId) throw new Error("Ticket ID is undefined");

    const { error } = await admin.client
      .schema("app_support")
      .rpc("assign_ticket", {
        p_ticket_id: ticketId,
        p_assigned_to: customer.data.id
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain(
      "Assigned user does not have support capabilities"
    );
  });

  test("customer should not be able to assign tickets", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!supportAgent.data?.id)
      throw new Error("Support agent ID is undefined");
    if (!ticketId) throw new Error("Ticket ID is undefined");

    const { error } = await customer.client
      .schema("app_support")
      .rpc("assign_ticket", {
        p_ticket_id: ticketId,
        p_assigned_to: supportAgent.data.id
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain(
      "Insufficient permissions to assign tickets"
    );
  });
});
