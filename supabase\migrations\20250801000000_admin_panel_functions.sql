-- Admin Panel Database Functions
-- This migration adds database functions needed for the admin panel

-- !section
-- section <PERSON>HEMA
CREATE SCHEMA IF NOT EXISTS app_dashboard;

GRANT USAGE ON SCHEMA app_dashboard TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA app_dashboard TO anon, authenticated, service_role;
GRANT ALL ON ALL ROUTINES IN SCHEMA app_dashboard TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA app_dashboard TO anon, authenticated, service_role;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON TABLES TO anon, authenticated, service_role;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON ROUTINES TO anon, authenticated, service_role;

-- !section
-- section FUNCTIONS

-- anchor get_user_stats
CREATE OR REPLACE FUNCTION app_account.get_user_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM app_account.profile),
    'active_users', (SELECT COUNT(*) FROM app_account.profile WHERE join_date >= NOW() - INTERVAL '30 days'),
    'new_users_today', (SELECT COUNT(*) FROM app_account.profile WHERE join_date >= CURRENT_DATE),
    'banned_users', (SELECT COUNT(*) FROM app_account.banned_user)
  ) INTO result;
  
  RETURN result;
END;
$$;

-- anchor get_transaction_stats
CREATE OR REPLACE FUNCTION app_transaction.get_transaction_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'daily_revenue', COALESCE((
      SELECT SUM(soda_credited) 
      FROM app_transaction.deposit 
      WHERE created_at >= CURRENT_DATE
    ), 0),
    'total_transactions', (
      SELECT COUNT(*) 
      FROM app_transaction.deposit
    ),
    'pending_withdrawals', (
      SELECT COUNT(*) 
      FROM app_transaction.withdrawal_request 
      WHERE status = 'pending'
    )
  ) INTO result;
  
  RETURN result;
END;
$$;

-- anchor get_support_stats
CREATE OR REPLACE FUNCTION app_support.get_support_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'open_tickets', (SELECT COUNT(*) FROM app_support.ticket WHERE status = 'open'),
    'total_tickets', (SELECT COUNT(*) FROM app_support.ticket),
    'tickets_today', (SELECT COUNT(*) FROM app_support.ticket WHERE created_at >= CURRENT_DATE),
    'total_flags', (SELECT COUNT(*) FROM app_support.flag)
  ) INTO result;
  
  RETURN result;
END;
$$;

-- anchor get_provider_stats
CREATE OR REPLACE FUNCTION app_provider.get_provider_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'active_providers', (SELECT COUNT(*) FROM app_provider.approved_user),
    'pending_applications', (SELECT COUNT(*) FROM app_provider.application WHERE application_status = 'pending'),
    'total_services', (SELECT COUNT(*) FROM app_provider.service),
    'approved_services', (SELECT COUNT(*) FROM app_provider.service WHERE approval_status = 'approved')
  ) INTO result;
  
  RETURN result;
END;
$$;

-- anchor get_recent_activity
CREATE OR REPLACE FUNCTION app_dashboard.get_recent_activity()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  activity_data JSON[];
BEGIN
  -- Get recent activity from various tables
  WITH recent_activities AS (
    SELECT 
      'user_registration' as activity_type,
      'New user registered' as description,
      join_date as timestamp
    FROM app_account.profile 
    WHERE join_date >= NOW() - INTERVAL '7 days'
    
    UNION ALL
    
    SELECT 
      'ticket_created' as activity_type,
      'Support ticket created' as description,
      created_at as timestamp
    FROM app_support.ticket 
    WHERE created_at >= NOW() - INTERVAL '7 days'
    
    UNION ALL
    
    SELECT 
      'provider_application' as activity_type,
      'Provider application submitted' as description,
      created_at as timestamp
    FROM app_provider.application 
    WHERE created_at >= NOW() - INTERVAL '7 days'
    
    ORDER BY timestamp DESC
    LIMIT 50
  )
  SELECT json_agg(
    json_build_object(
      'activity_type', activity_type,
      'description', description,
      'timestamp', timestamp
    )
  ) INTO result
  FROM recent_activities;
  
  RETURN COALESCE(result, '[]'::JSON);
END;
$$;

-- anchor get_user_financial_summary
CREATE OR REPLACE FUNCTION app_transaction.get_user_financial_summary()
RETURNS TABLE(
  user_id UUID,
  total_deposits NUMERIC,
  total_withdrawals NUMERIC,
  current_soda_balance NUMERIC,
  current_cap_balance NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    w.user_id,
    COALESCE(d.total_deposits, 0) as total_deposits,
    COALESCE(wr.total_withdrawals, 0) as total_withdrawals,
    w.soda_balance as current_soda_balance,
    w.cap_balance as current_cap_balance
  FROM app_transaction.wallet w
  LEFT JOIN (
    SELECT 
      user_id, 
      SUM(soda_credited) as total_deposits
    FROM app_transaction.deposit
    GROUP BY user_id
  ) d ON w.user_id = d.user_id
  LEFT JOIN (
    SELECT 
      user_id, 
      SUM(soda_amount) as total_withdrawals
    FROM app_transaction.withdrawal_request
    WHERE status = 'completed'
    GROUP BY user_id
  ) wr ON w.user_id = wr.user_id;
END;
$$;

-- anchor process_moderation_item
CREATE OR REPLACE FUNCTION app_support.process_moderation_item(
  p_item_id UUID,
  p_action TEXT,
  p_moderator_note TEXT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- This is a placeholder function for moderation processing
  -- In a real implementation, this would handle different types of moderation actions
  -- For now, we'll just update a hypothetical moderation_queue table
  
  -- Note: The moderation_queue table doesn't exist in the current schema
  -- This function serves as a placeholder for the admin panel requirements
  
  RAISE NOTICE 'Processing moderation item % with action % and note %', p_item_id, p_action, p_moderator_note;
END;
$$;
