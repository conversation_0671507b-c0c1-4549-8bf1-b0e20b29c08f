"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { catalogClient } from "@/lib/supabase";
import { Settings, Plus, Search } from "lucide-react";

export default function FieldsPage() {
  const [search, setSearch] = useState("");

  const { data: fields, isLoading } = useQuery({
    queryKey: ["fields", search],
    queryFn: async () => {
      let query = catalogClient
        .from("field")
        .select("*")
        .order("created_at", { ascending: false });

      if (search) {
        query = query.or(
          `name->en.ilike.%${search}%,name->ja.ilike.%${search}%,name->ko.ilike.%${search}%,name->tr.ilike.%${search}%`
        );
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading fields...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Custom Fields</h1>
          <p className="mt-1 text-sm text-gray-600">
            Configure custom fields for activities and services
          </p>
        </div>
        <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <Plus className="h-4 w-4 mr-2" />
          Add Field
        </button>
      </div>

      {/* Search */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search fields..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </div>

      {/* Fields List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          {fields && fields.length > 0 ? (
            <div className="space-y-4">
              {fields.map((field) => (
                <div
                  key={field.id}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {field.name.en || field.name.ja || field.name.ko || field.name.tr}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Type: {field.type} | Required: {field.required ? 'Yes' : 'No'}
                      </p>
                      {field.description && (
                        <p className="mt-1 text-sm text-gray-700">
                          {field.description.en || field.description.ja || field.description.ko || field.description.tr}
                        </p>
                      )}
                    </div>
                    <Settings className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Settings className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No custom fields</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new custom field.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
