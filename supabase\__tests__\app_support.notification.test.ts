import { afterAll, describe, expect, test } from "vitest";
import {
  mockCustomer,
  mockProvider,
  mockSupportAgent
} from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { findValidatedNotification } from "./utils/validation";

describe("Support Notification System", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const supportAgent = mockSupportAgent();
  const createdNotificationIds: string[] = [];
  const createdTicketIds: string[] = [];
  const createdFlagIds: string[] = [];

  describe("Ticket Notifications", () => {
    test("ticket creation notification is sent", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create a support ticket using service client to bypass RLS
      const { data: ticket } = await serviceClient
        .schema("app_support")
        .from("ticket")
        .insert({
          user_id: customer.data.id,
          title: "Test Support Ticket",
          problem_description: "This is a test ticket",
          type: "report"
        })
        .select()
        .single();

      if (!ticket) {
        throw new Error("Ticket creation failed");
      }

      createdTicketIds.push(ticket.id);

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "support.ticket.created");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "support.ticket.created",
        (data) => data.ticket_id === ticket.id
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.ticket_id).toBe(ticket.id);
      expect(validatedNotification!.data.title).toBe("Test Support Ticket");
      expect(validatedNotification!.data.type).toBe("report");

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("ticket status change notification is sent", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create a support ticket using service client to bypass RLS
      const { data: ticket } = await serviceClient
        .schema("app_support")
        .from("ticket")
        .insert({
          user_id: customer.data.id,
          title: "Test Status Change Ticket",
          problem_description: "This is a test ticket for status change",
          type: "report",
          status: "open"
        })
        .select()
        .single();

      if (!ticket) {
        throw new Error("Ticket creation failed");
      }

      createdTicketIds.push(ticket.id);

      // Update ticket status
      await serviceClient
        .schema("app_support")
        .from("ticket")
        .update({ status: "closed" })
        .eq("id", ticket.id);

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "support.ticket.status_change");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "support.ticket.status_change",
        (data) => data.old_status === "open" && data.new_status === "closed"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.old_status).toBe("open");
      expect(validatedNotification!.data.new_status).toBe("closed");

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });
  });

  describe("Ticket Comment Notifications", () => {
    test("ticket comment notification is sent to ticket owner", async () => {
      if (!customer.client || !customer.data || !supportAgent.data) {
        throw new Error("Customer or support agent not defined");
      }

      // Create a support ticket using service client to bypass RLS
      const { data: ticket } = await serviceClient
        .schema("app_support")
        .from("ticket")
        .insert({
          user_id: customer.data.id,
          title: "Test Comment Ticket",
          problem_description: "This is a test ticket for comments",
          type: "report"
        })
        .select()
        .single();

      if (!ticket) {
        throw new Error("Ticket creation failed");
      }

      createdTicketIds.push(ticket.id);

      // Support agent adds a comment
      await serviceClient.schema("app_support").from("ticket_comment").insert({
        ticket_id: ticket.id,
        user_id: supportAgent.data.id,
        comment_message: "This is a support agent comment"
      });

      // Check if notification was created for the ticket owner
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "support.ticket.comment");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "support.ticket.comment",
        (data) => data.ticket_id === ticket.id
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.ticket_id).toBe(ticket.id);
      expect(validatedNotification!.data.content_preview).toBe(
        "This is a support agent comment"
      );

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("no notification sent when ticket owner comments on their own ticket", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Create a support ticket using service client to bypass RLS
      const { data: ticket } = await serviceClient
        .schema("app_support")
        .from("ticket")
        .insert({
          user_id: customer.data.id,
          title: "Test Self Comment Ticket",
          problem_description: "This is a test ticket for self comments",
          type: "report"
        })
        .select()
        .single();

      if (!ticket) {
        throw new Error("Ticket creation failed");
      }

      createdTicketIds.push(ticket.id);

      // Customer adds a comment to their own ticket
      const { data: comment } = await serviceClient
        .schema("app_support")
        .from("ticket_comment")
        .insert({
          ticket_id: ticket.id,
          user_id: customer.data.id,
          comment_message: "This is a customer self-comment"
        })
        .select()
        .single();

      // Wait a moment for any potential notifications
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Check that no notification was created for this specific self-comment
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "support.ticket.comment");

      // Verify no notification exists for this specific comment
      const selfCommentNotification = notifications?.find((n) => {
        try {
          const data = JSON.parse(n.data as string);
          return (
            data.ticket_id === ticket.id && data.comment_id === comment?.id
          );
        } catch {
          return false;
        }
      });

      expect(selfCommentNotification).toBeUndefined();
    });
  });

  describe("Flag Notifications", () => {
    test("flag creation notification is sent", async () => {
      if (!customer.data || !provider.data)
        throw new Error("Customer or provider not defined");

      // Create a flag using service client to bypass RLS
      const { data: flag } = await serviceClient
        .schema("app_support")
        .from("flag")
        .insert({
          flagger_id: customer.data.id,
          flagged_id: provider.data.id, // Flag the provider
          reason: "inappropriate_content"
        })
        .select()
        .single();

      if (!flag) {
        throw new Error("Flag creation failed");
      }

      createdFlagIds.push(flag.id);

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "support.flag.created");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "support.flag.created",
        (data) => data.flag_id === flag.id
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.flag_id).toBe(flag.id);
      expect(validatedNotification!.data.reason).toBe("inappropriate_content");
      expect(validatedNotification!.data.flagged_id).toBe(provider.data.id);

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }

    // Cleanup created tickets
    if (createdTicketIds.length > 0) {
      await serviceClient
        .schema("app_support")
        .from("ticket")
        .delete()
        .in("id", createdTicketIds);
    }

    // Cleanup created flags
    if (createdFlagIds.length > 0) {
      await serviceClient
        .schema("app_support")
        .from("flag")
        .delete()
        .in("id", createdFlagIds);
    }
  });
});
