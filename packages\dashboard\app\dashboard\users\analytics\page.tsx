"use client";

import { useQuery } from "@tanstack/react-query";
import { accountClient } from "@/lib/supabase";
import { Users, TrendingUp, UserPlus, UserX } from "lucide-react";
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from "recharts";

export default function UserAnalyticsPage() {
  const { data: userMetrics, isLoading } = useQuery({
    queryKey: ["user-analytics"],
    queryFn: async () => {
      // Get user growth data
      const { data: profiles, error } = await accountClient
        .from("profile")
        .select("join_date, banned_user(user_id)")
        .order("join_date");

      if (error) throw error;

      // Process user growth over time
      const userGrowthData = processUserGrowthData(profiles);
      
      // Get banned users count
      const bannedCount = profiles.filter(p => p.banned_user?.length > 0).length;
      
      return {
        totalUsers: profiles.length,
        activeUsers: profiles.filter(p => !p.banned_user?.length).length,
        bannedUsers: bannedCount,
        userGrowth: userGrowthData,
        newUsersToday: profiles.filter(p => 
          new Date(p.join_date).toDateString() === new Date().toDateString()
        ).length
      };
    }
  });

  const processUserGrowthData = (profiles: any[]) => {
    const monthlyData: Record<string, number> = {};
    
    profiles.forEach(profile => {
      const date = new Date(profile.join_date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    });

    return Object.entries(monthlyData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, count]) => ({
        month,
        users: count,
        cumulative: Object.entries(monthlyData)
          .filter(([m]) => m <= month)
          .reduce((sum, [, c]) => sum + c, 0)
      }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading user analytics...</div>
      </div>
    );
  }

  const metrics = [
    {
      title: "Total Users",
      value: userMetrics?.totalUsers || 0,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "Active Users",
      value: userMetrics?.activeUsers || 0,
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "New Today",
      value: userMetrics?.newUsersToday || 0,
      icon: UserPlus,
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      title: "Banned Users",
      value: userMetrics?.bannedUsers || 0,
      icon: UserX,
      color: "text-red-600",
      bgColor: "bg-red-50"
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">User Analytics</h1>
        <p className="mt-1 text-sm text-gray-600">
          Track user growth, engagement, and platform metrics
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <div key={metric.title} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 ${metric.bgColor} rounded-lg flex items-center justify-center`}>
                      <Icon className={`h-5 w-5 ${metric.color}`} />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {metric.title}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {metric.value.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* User Growth Chart */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            User Growth Over Time
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={userMetrics?.userGrowth || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="cumulative" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  name="Total Users"
                />
                <Line 
                  type="monotone" 
                  dataKey="users" 
                  stroke="#10B981" 
                  strokeWidth={2}
                  name="New Users"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Monthly Registration Chart */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Monthly Registrations
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={userMetrics?.userGrowth || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="users" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
}
