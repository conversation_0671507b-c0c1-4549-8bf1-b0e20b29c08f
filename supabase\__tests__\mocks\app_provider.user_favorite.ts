import { afterEach, beforeAll, beforeEach, expect } from "vitest";
import { serviceClient } from "../utils/client";
import { MockUser } from "./auth.user";
import { Database } from "shared/lib/supabase/database";

export type MockUserFavorite = {
  user_id?: string;
  provider_id?: string;
  created_at?: string;
};

type MockUserFavoriteParams = {
  customer: MockUser;
  provider: MockUser;
  favorite?: Partial<
    Database["app_provider"]["Tables"]["user_favorite"]["Insert"]
  >;
};

async function createUserFavorite(
  { customer, provider, favorite }: MockUserFavoriteParams,
  mockFavorite: MockUserFavorite
) {
  if (!customer.data) throw new Error("Customer not defined");
  if (!provider.data) throw new Error("Provider not defined");

  const { data, error } = await serviceClient
    .schema("app_provider")
    .from("user_favorite")
    .insert({
      user_id: customer.data.id,
      provider_id: provider.data.id,
      ...(favorite ?? {})
    })
    .select()
    .single();

  expect(error).toBeNull();
  mockFavorite.user_id = data?.user_id;
  mockFavorite.provider_id = data?.provider_id;
  mockFavorite.created_at = data?.created_at ?? undefined;
}

async function cleanupUserFavorite({
  customer,
  provider
}: MockUserFavoriteParams) {
  if (!customer.data || !provider.data) return;

  await serviceClient
    .schema("app_provider")
    .from("user_favorite")
    .delete()
    .eq("user_id", customer.data.id)
    .eq("provider_id", provider.data.id);
}

export function mockUserFavorite(params: MockUserFavoriteParams) {
  const favorite: MockUserFavorite = {};

  beforeAll(async () => {
    await createUserFavorite(params, favorite);
  });

  afterEach(async () => {
    await cleanupUserFavorite(params);
  });

  return favorite;
}

export function mockUserFavoriteEach(params: MockUserFavoriteParams) {
  const favorite: MockUserFavorite = {};

  beforeEach(async () => {
    await createUserFavorite(params, favorite);
  });

  afterEach(async () => {
    await cleanupUserFavorite(params);
  });

  return favorite;
}
