-- section <PERSON>HEMA
DROP POLICY IF EXISTS "account_avatar_select_all" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_insert_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_update_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_delete_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_insert_admin" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_update_admin" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_delete_admin" ON STORAGE.objects
;

DROP POLICY IF EXISTS "provider_gallery_select_all" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_insert_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_update_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_delete_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_insert_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_update_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_delete_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_select_all" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_insert_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_update_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_delete_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_insert_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_update_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_delete_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_select_all" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_insert_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_update_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_delete_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_insert_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_update_admin" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_delete_admin" ON storage.objects
;

DROP POLICY IF EXISTS "catalog_activity_select_all" ON storage.objects
;

DROP POLICY IF EXISTS "catalog_activity_insert_admin" ON storage.objects
;

DROP POLICY IF EXISTS "catalog_activity_update_admin" ON storage.objects
;

DROP POLICY IF EXISTS "catalog_activity_delete_admin" ON storage.objects
;

-- !section
-- section RLS POLICIES
-- anchor account_avatar
CREATE POLICY "account_avatar_select_all" ON STORAGE.objects FOR
SELECT
  USING (bucket_id = 'account_avatar')
;

CREATE POLICY "account_avatar_insert_own" ON STORAGE.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'account_avatar'
    AND app_access.has_capability ('storage.account_avatar.edit')
    AND (STORAGE.filename (NAME)) = (
      (
        SELECT
          auth.uid ()
      )::TEXT
    )
  )
;

CREATE POLICY "account_avatar_update_own" ON STORAGE.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'account_avatar'
    AND app_access.has_capability ('storage.account_avatar.edit')
    AND (STORAGE.filename (NAME)) = (
      (
        SELECT
          auth.uid ()
      )::TEXT
    )
  )
;

CREATE POLICY "account_avatar_delete_own" ON STORAGE.objects FOR DELETE TO authenticated USING (
  bucket_id = 'account_avatar'
  AND app_access.has_capability ('storage.account_avatar.edit')
  AND (STORAGE.filename (NAME)) = (
    (
      SELECT
        auth.uid ()
    )::TEXT
  )
)
;

CREATE POLICY "account_avatar_insert_admin" ON STORAGE.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'account_avatar'
    AND app_access.has_capability (
      'storage.account_avatar.all.edit'
    )
  )
;

CREATE POLICY "account_avatar_update_admin" ON STORAGE.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'account_avatar'
    AND app_access.has_capability (
      'storage.account_avatar.all.edit'
    )
  )
;

CREATE POLICY "account_avatar_delete_admin" ON STORAGE.objects FOR DELETE TO authenticated USING (
  bucket_id = 'account_avatar'
  AND app_access.has_capability (
    'storage.account_avatar.all.edit'
  )
)
;

-- anchor provider_gallery
CREATE POLICY "provider_gallery_select_all" ON storage.objects FOR
SELECT
  USING (
    bucket_id = 'provider_gallery'
  )
;

CREATE POLICY "provider_gallery_insert_own" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_gallery'
    AND app_access.has_capability (
      'storage.provider_gallery.edit'
    )
    AND (STORAGE.filename (NAME)) LIKE (
      (
        SELECT
          auth.uid ()
      )::TEXT || '_%'
    )
  )
;

CREATE POLICY "provider_gallery_update_own" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_gallery'
    AND app_access.has_capability (
      'storage.provider_gallery.edit'
    )
    AND (STORAGE.filename (NAME)) LIKE (
      (
        SELECT
          auth.uid ()
      )::TEXT || '_%'
    )
  )
;

CREATE POLICY "provider_gallery_delete_own" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_gallery'
  AND app_access.has_capability (
    'storage.provider_gallery.edit'
  )
  AND (STORAGE.filename (NAME)) LIKE (
    (
      SELECT
        auth.uid ()
    )::TEXT || '_%'
  )
)
;

CREATE POLICY "provider_gallery_insert_admin" ON storage.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_gallery'
    AND app_access.has_capability (
      'storage.provider_gallery.all.edit'
    )
  )
;

CREATE POLICY "provider_gallery_update_admin" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_gallery'
    AND app_access.has_capability (
      'storage.provider_gallery.all.edit'
    )
  )
;

CREATE POLICY "provider_gallery_delete_admin" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_gallery'
  AND app_access.has_capability (
    'storage.provider_gallery.all.edit'
  )
)
;

-- anchor provider_activity
CREATE POLICY "provider_activity_select_all" ON storage.objects FOR
SELECT
  USING (
    bucket_id = 'provider_activity'
  )
;

CREATE POLICY "provider_activity_insert_own" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_activity'
    AND app_access.has_capability (
      'storage.provider_activity.edit'
    )
    AND EXISTS (
      SELECT
        1
      FROM
        app_provider.activity a
      WHERE
        a.user_id = auth.uid ()
        AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
    )
  )
;

CREATE POLICY "provider_activity_update_own" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_activity'
    AND app_access.has_capability (
      'storage.provider_activity.edit'
    )
    AND EXISTS (
      SELECT
        1
      FROM
        app_provider.activity a
      WHERE
        a.user_id = auth.uid ()
        AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
    )
  )
;

CREATE POLICY "provider_activity_delete_own" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_activity'
  AND app_access.has_capability (
    'storage.provider_activity.edit'
  )
  AND EXISTS (
    SELECT
      1
    FROM
      app_provider.activity a
    WHERE
      a.user_id = auth.uid ()
      AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
  )
)
;

CREATE POLICY "provider_activity_insert_admin" ON storage.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_activity'
    AND app_access.has_capability (
      'storage.provider_activity.all.edit'
    )
  )
;

CREATE POLICY "provider_activity_update_admin" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_activity'
    AND app_access.has_capability (
      'storage.provider_activity.all.edit'
    )
  )
;

CREATE POLICY "provider_activity_delete_admin" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_activity'
  AND app_access.has_capability (
    'storage.provider_activity.all.edit'
  )
)
;

-- anchor provider_voice
CREATE POLICY "provider_voice_select_all" ON storage.objects FOR
SELECT
  USING (bucket_id = 'provider_voice')
;

CREATE POLICY "provider_voice_insert_own" ON storage.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_voice'
    AND app_access.has_capability ('storage.provider_voice.edit')
    AND (
      (STORAGE.filename (NAME)) = auth.uid ()::TEXT
      OR EXISTS (
        SELECT
          1
        FROM
          app_provider.activity a
        WHERE
          a.user_id = auth.uid ()
          AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
      )
    )
  )
;

CREATE POLICY "provider_voice_update_own" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_voice'
    AND app_access.has_capability ('storage.provider_voice.edit')
    AND (
      (STORAGE.filename (NAME)) = auth.uid ()::TEXT
      OR EXISTS (
        SELECT
          1
        FROM
          app_provider.activity a
        WHERE
          a.user_id = auth.uid ()
          AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
      )
    )
  )
;

CREATE POLICY "provider_voice_delete_own" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_voice'
  AND app_access.has_capability ('storage.provider_voice.edit')
  AND (
    (STORAGE.filename (NAME)) = auth.uid ()::TEXT
    OR EXISTS (
      SELECT
        1
      FROM
        app_provider.activity a
      WHERE
        a.user_id = auth.uid ()
        AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
    )
  )
)
;

CREATE POLICY "provider_voice_insert_admin" ON storage.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_voice'
    AND app_access.has_capability (
      'storage.provider_voice.all.edit'
    )
  )
;

CREATE POLICY "provider_voice_update_admin" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_voice'
    AND app_access.has_capability (
      'storage.provider_voice.all.edit'
    )
  )
;

CREATE POLICY "provider_voice_delete_admin" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_voice'
  AND app_access.has_capability (
    'storage.provider_voice.all.edit'
  )
)
;

-- anchor catalog_activity
CREATE POLICY "catalog_activity_select_all" ON storage.objects FOR
SELECT
  USING (
    bucket_id = 'catalog_activity'
  )
;

CREATE POLICY "catalog_activity_insert_admin" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'catalog_activity'
    AND app_access.has_capability (
      'storage.catalog_activity.all.edit'
    )
    -- app_catalog.activity with same id exists.
    AND EXISTS (
      SELECT
        1
      FROM
        app_catalog.activity a
      WHERE
        (
          storage.filename (storage.objects.name)
        ) = a.id::TEXT
    )
  )
;

CREATE POLICY "catalog_activity_update_admin" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'catalog_activity'
    AND app_access.has_capability (
      'storage.catalog_activity.all.edit'
    )
    AND EXISTS (
      SELECT
        1
      FROM
        app_catalog.activity a
      WHERE
        (
          storage.filename (storage.objects.name)
        ) = a.id::TEXT
    )
  )
;

CREATE POLICY "catalog_activity_delete_admin" ON storage.objects FOR delete TO authenticated USING (
  bucket_id = 'catalog_activity'
  AND app_access.has_capability (
    'storage.catalog_activity.all.edit'
  )
  AND EXISTS (
    SELECT
      1
    FROM
      app_catalog.activity a
    WHERE
      (
        storage.filename (storage.objects.name)
      ) = a.id::TEXT
  )
)
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'storage.account_avatar.all.edit',
      'storage.provider_gallery.all.edit',
      'storage.provider_activity.all.edit',
      'storage.provider_voice.all.edit',
      'storage.catalog_activity.all.edit'
    ]
  )
;

-- anchor customer
SELECT
  app_access.define_role_capability (
    'customer',
    ARRAY['storage.account_avatar.edit']
  )
;

-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY[
      'storage.account_avatar.edit',
      'storage.provider_gallery.edit',
      'storage.provider_activity.edit',
      'storage.provider_voice.edit'
    ]
  )
;

-- anchor provider_applicant
SELECT
  app_access.define_role_capability (
    'provider_applicant',
    ARRAY[
      'storage.account_avatar.edit',
      'storage.provider_gallery.edit',
      'storage.provider_activity.edit',
      'storage.provider_voice.edit'
    ]
  )
;

-- !section