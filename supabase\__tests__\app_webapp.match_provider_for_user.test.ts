import { test, expect } from "vitest";
import { mockCustomer, mockProvider, mockAdmin } from "./mocks/auth.user";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { mockService } from "./mocks/app_provider.service";
import { serviceClient } from "./utils/client";
const admin = mockAdmin();
const customer = mockCustomer();
const provider1 = mockProvider();
const provider2 = mockProvider();
const provider3 = mockProvider();

const activity1 = mockCatalogActivity({ admin });

const service1 = mockService(provider1);
const service2 = mockService(provider2);
const service3 = mockService(provider3);

test("should return null when user has no favorite activities", async () => {
  if (!customer.client || !customer.data || !provider1.data || !provider2.data)
    throw new Error("Customer or provider not defined");

  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: [provider1.data.id, provider2.data.id]
    });

  expect(result.data).toBeNull();
});

test("should return null when no providers match user's favorite activities", async () => {
  if (!customer.client || !customer.data || !activity1.id || !provider1.data)
    throw new Error("Required test data not defined");

  // Add user's favorite activity (different from provider's activity)
  const favoriteInsert = await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .upsert({
      user_id: customer.data.id,
      activity_id: activity1.id
    })
    .select()
    .single();

  // Verify the favorite activity was inserted/updated
  expect(favoriteInsert.data?.user_id).toBe(customer.data.id);
  expect(favoriteInsert.data?.activity_id).toBe(activity1.id);

  // Provider has different activity (service1.catalogActivityId), so no match
  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: [provider1.data.id]
    });

  expect(result.data).toBeNull();
});

test("should return null when provider is not open for orders", async () => {
  if (
    !customer.client ||
    !customer.data ||
    !provider1.data ||
    !service1.catalogActivityId
  )
    throw new Error("Required test data not defined");

  // Add user's favorite activity (matching provider's activity)
  const favoriteInsert = await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .upsert({
      user_id: customer.data.id,
      activity_id: service1.catalogActivityId
    })
    .select()
    .single();

  // Verify the favorite activity was inserted/updated
  expect(favoriteInsert.data?.user_id).toBe(customer.data.id);
  expect(favoriteInsert.data?.activity_id).toBe(service1.catalogActivityId);

  // Set provider to closed for orders
  const statusUpdate = await serviceClient
    .schema("app_provider")
    .from("status")
    .upsert({
      user_id: provider1.data.id,
      is_open_for_orders: false
    })
    .select()
    .single();

  // Verify the status was updated
  expect(statusUpdate.data?.user_id).toBe(provider1.data.id);
  expect(statusUpdate.data?.is_open_for_orders).toBe(false);

  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: [provider1.data.id]
    });

  expect(result.data).toBeNull();
});

test("should return matching provider when all conditions are met", async () => {
  if (
    !customer.client ||
    !customer.data ||
    !provider1.data ||
    !service1.catalogActivityId
  )
    throw new Error("Required test data not defined");

  // Add user's favorite activity (using the same activity as the provider's service)
  await serviceClient.schema("app_account").from("favorite_activity").upsert({
    user_id: customer.data.id,
    activity_id: service1.catalogActivityId
  });

  // Verify the favorite activity was inserted/updated with extra select request
  const { data: favoriteData } = await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .select("*")
    .eq("user_id", customer.data.id)
    .eq("activity_id", service1.catalogActivityId)
    .single();

  expect(favoriteData?.user_id).toBe(customer.data.id);
  expect(favoriteData?.activity_id).toBe(service1.catalogActivityId);

  // Set provider to open for orders
  const statusUpdate = await serviceClient
    .schema("app_provider")
    .from("status")
    .upsert({
      user_id: provider1.data.id,
      is_open_for_orders: true
    })
    .select()
    .single();

  // Verify the status was updated
  expect(statusUpdate.data?.user_id).toBe(provider1.data.id);
  expect(statusUpdate.data?.is_open_for_orders).toBe(true);

  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: [provider1.data.id]
    });

  expect(result.data).toBe(provider1.data.id);
});

test("should prioritize provider with higher rating", async () => {
  if (
    !customer.client ||
    !customer.data ||
    !provider1.data ||
    !provider2.data ||
    !service1.catalogActivityId ||
    !service2.catalogActivityId
  )
    throw new Error("Required test data not defined");

  // Add user's favorite activity (matching both providers' activities)
  const favoriteInserts = await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .upsert([
      { user_id: customer.data.id, activity_id: service1.catalogActivityId },
      { user_id: customer.data.id, activity_id: service2.catalogActivityId }
    ])
    .select();

  // Verify favorite activities were inserted
  expect(favoriteInserts.data).toHaveLength(2);
  expect(
    favoriteInserts.data?.some(
      (fav) => fav.activity_id === service1.catalogActivityId
    )
  ).toBe(true);
  expect(
    favoriteInserts.data?.some(
      (fav) => fav.activity_id === service2.catalogActivityId
    )
  ).toBe(true);

  // Set both providers to open for orders
  const statusUpdates = await serviceClient
    .schema("app_provider")
    .from("status")
    .upsert([
      { user_id: provider1.data.id, is_open_for_orders: true },
      { user_id: provider2.data.id, is_open_for_orders: true }
    ])
    .select();

  // Verify status updates
  expect(statusUpdates.data).toHaveLength(2);
  expect(
    statusUpdates.data?.every((status) => status.is_open_for_orders === true)
  ).toBe(true);

  // Clean up any existing performance data first
  await serviceClient
    .schema("app_provider")
    .from("performance")
    .delete()
    .in("user_id", [provider1.data.id, provider2.data.id]);

  // Set provider2 to have higher rating (using integers 0-5)
  const performanceInserts = await serviceClient
    .schema("app_provider")
    .from("performance")
    .insert([
      { user_id: provider1.data.id, rating: 3, completed_orders: 5 },
      { user_id: provider2.data.id, rating: 5, completed_orders: 3 }
    ])
    .select();

  // Verify performance data was inserted
  expect(performanceInserts.data).toHaveLength(2);
  expect(
    performanceInserts.data?.find((p) => p.user_id === provider1.data?.id)
      ?.rating
  ).toBe(3);
  expect(
    performanceInserts.data?.find((p) => p.user_id === provider2.data?.id)
      ?.rating
  ).toBe(5);

  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: [provider1.data.id, provider2.data.id]
    });

  expect(result.data).toBe(provider2.data.id);
});

test("should prioritize provider with more completed orders when ratings are equal", async () => {
  if (!customer.client || !customer.data || !provider1.data || !provider2.data)
    throw new Error("Required test data not defined");

  // Clean up any existing performance data first
  await serviceClient
    .schema("app_provider")
    .from("performance")
    .delete()
    .in("user_id", [provider1.data.id, provider2.data.id]);

  // Set equal ratings but different completed orders
  const performanceInserts = await serviceClient
    .schema("app_provider")
    .from("performance")
    .insert([
      { user_id: provider1.data.id, rating: 4, completed_orders: 10 },
      { user_id: provider2.data.id, rating: 4, completed_orders: 5 }
    ])
    .select();

  // Verify performance data was inserted correctly
  expect(performanceInserts.data).toHaveLength(2);
  expect(
    performanceInserts.data?.find((p) => p.user_id === provider1.data?.id)
      ?.completed_orders
  ).toBe(10);
  expect(
    performanceInserts.data?.find((p) => p.user_id === provider2.data?.id)
      ?.completed_orders
  ).toBe(5);

  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: [provider1.data.id, provider2.data.id]
    });

  expect(result.data).toBe(provider1.data.id);
});

test("should match from all providers when provider IDs array is empty", async () => {
  if (
    !customer.client ||
    !customer.data ||
    !provider1.data ||
    !service1.catalogActivityId
  )
    throw new Error("Required test data not defined");

  // Add user's favorite activity (matching provider1's activity)
  const favoriteInsert = await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .upsert({
      user_id: customer.data.id,
      activity_id: service1.catalogActivityId
    })
    .select()
    .single();

  // Verify the favorite activity was inserted/updated
  expect(favoriteInsert.data?.user_id).toBe(customer.data.id);
  expect(favoriteInsert.data?.activity_id).toBe(service1.catalogActivityId);

  // Set provider1 to open for orders
  const statusUpdate = await serviceClient
    .schema("app_provider")
    .from("status")
    .upsert({
      user_id: provider1.data.id,
      is_open_for_orders: true
    })
    .select()
    .single();

  // Verify the status was updated
  expect(statusUpdate.data?.user_id).toBe(provider1.data.id);
  expect(statusUpdate.data?.is_open_for_orders).toBe(true);

  // Call with empty array - should match from all providers
  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: []
    });

  // Should return provider1 since it matches the user's favorite activity
  expect(result.data).toBe(provider1.data.id);
});

test("should match from all providers when no parameter is provided", async () => {
  if (
    !customer.client ||
    !customer.data ||
    !provider2.data ||
    !service2.catalogActivityId ||
    !provider1.data ||
    !service1.catalogActivityId ||
    !provider3.data ||
    !service3.catalogActivityId
  )
    throw new Error("Required test data not defined");

  // Clean up any existing favorite activities and performance data first
  await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .delete()
    .eq("user_id", customer.data.id);

  await serviceClient
    .schema("app_provider")
    .from("performance")
    .delete()
    .in("user_id", [provider1.data.id, provider2.data.id, provider3.data.id]);

  // Add user's favorite activity (matching provider2's activity)
  await serviceClient.schema("app_account").from("favorite_activity").upsert({
    user_id: customer.data.id,
    activity_id: service2.catalogActivityId
  });

  // Verify the favorite activity was inserted/updated with extra select request
  const { data: favoriteData } = await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .select("*")
    .eq("user_id", customer.data.id)
    .eq("activity_id", service2.catalogActivityId)
    .single();

  expect(favoriteData?.user_id).toBe(customer.data.id);
  expect(favoriteData?.activity_id).toBe(service2.catalogActivityId);

  // Set provider2 to open for orders and give it the highest rating
  await serviceClient.schema("app_provider").from("status").upsert({
    user_id: provider2.data.id,
    is_open_for_orders: true
  });

  await serviceClient.schema("app_provider").from("performance").insert({
    user_id: provider2.data.id,
    rating: 5,
    completed_orders: 20
  });

  // Verify the status and performance were updated with extra select requests
  const { data: statusData } = await serviceClient
    .schema("app_provider")
    .from("status")
    .select("*")
    .eq("user_id", provider2.data.id)
    .single();

  expect(statusData?.user_id).toBe(provider2.data.id);
  expect(statusData?.is_open_for_orders).toBe(true);

  const { data: performanceData } = await serviceClient
    .schema("app_provider")
    .from("performance")
    .select("*")
    .eq("user_id", provider2.data.id)
    .single();

  expect(performanceData?.user_id).toBe(provider2.data.id);
  expect(performanceData?.rating).toBe(5);

  // Call without parameter - should match from all providers
  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {});

  // Should return provider2 since it matches the user's favorite activity and has highest rating
  expect(result.data).toBe(provider2.data.id);
});

test("should handle multiple favorite activities and find best match", async () => {
  if (
    !customer.client ||
    !customer.data ||
    !provider3.data ||
    !service3.catalogActivityId ||
    !provider1.data ||
    !service1.catalogActivityId ||
    !provider2.data ||
    !service2.catalogActivityId
  )
    throw new Error("Required test data not defined");

  // Clean up any existing favorite activities and performance data first
  await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .delete()
    .eq("user_id", customer.data.id);

  await serviceClient
    .schema("app_provider")
    .from("performance")
    .delete()
    .in("user_id", [provider1.data.id, provider2.data.id, provider3.data.id]);

  // Add provider3's activity as user's favorite activity
  await serviceClient.schema("app_account").from("favorite_activity").upsert({
    user_id: customer.data.id,
    activity_id: service3.catalogActivityId
  });

  // Verify the favorite activity was inserted/updated with extra select request
  const { data: favoriteData } = await serviceClient
    .schema("app_account")
    .from("favorite_activity")
    .select("*")
    .eq("user_id", customer.data.id)
    .eq("activity_id", service3.catalogActivityId)
    .single();

  expect(favoriteData?.user_id).toBe(customer.data.id);
  expect(favoriteData?.activity_id).toBe(service3.catalogActivityId);

  // Set provider3 to open for orders
  await serviceClient.schema("app_provider").from("status").upsert({
    user_id: provider3.data.id,
    is_open_for_orders: true
  });

  // Set provider3 to have highest rating
  await serviceClient.schema("app_provider").from("performance").insert({
    user_id: provider3.data.id,
    rating: 5,
    completed_orders: 25
  });

  // Verify the status and performance were updated with extra select requests
  const { data: statusData } = await serviceClient
    .schema("app_provider")
    .from("status")
    .select("*")
    .eq("user_id", provider3.data.id)
    .single();

  expect(statusData?.user_id).toBe(provider3.data.id);
  expect(statusData?.is_open_for_orders).toBe(true);

  const { data: performanceData } = await serviceClient
    .schema("app_provider")
    .from("performance")
    .select("*")
    .eq("user_id", provider3.data.id)
    .single();

  expect(performanceData?.user_id).toBe(provider3.data.id);
  expect(performanceData?.rating).toBe(5);
  expect(performanceData?.completed_orders).toBe(25);

  const result = await customer.client
    .schema("app_webapp")
    .rpc("match_provider_for_user", {
      p_provider_ids: [provider1.data.id, provider2.data.id, provider3.data.id]
    });

  expect(result.data).toBe(provider3.data.id);
});
