import { useEffect, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { createClient } from "shared/lib/supabase/client";
import type { RealtimeChannel } from "@supabase/supabase-js";

interface UseRealtimeSubscriptionProps {
  schema: string;
  table: string;
  queryKey: string[];
  filter?: string;
}

export function useRealtimeSubscription({
  schema,
  table,
  queryKey,
  filter
}: UseRealtimeSubscriptionProps) {
  const queryClient = useQueryClient();
  const channelRef = useRef<RealtimeChannel | null>(null);

  useEffect(() => {
    const supabase = createClient();

    const channel = supabase
      .channel(`${schema}_${table}_changes`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema,
          table,
          filter
        },
        () => {
          queryClient.invalidateQueries({ queryKey });
        }
      )
      .subscribe();

    channelRef.current = channel;

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [schema, table, queryKey, filter, queryClient]);
}
