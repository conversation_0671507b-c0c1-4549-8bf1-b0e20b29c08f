"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+functions-js@2.4.5";
exports.ids = ["vendor-chunks/@supabase+functions-js@2.4.5"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js":
/*!********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionsClient: () => (/* binding */ FunctionsClient)\n/* harmony export */ });\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helper */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nclass FunctionsClient {\n    constructor(url, { headers = {}, customFetch, region = _types__WEBPACK_IMPORTED_MODULE_0__.FunctionRegion.Any, } = {}) {\n        this.url = url;\n        this.headers = headers;\n        this.region = region;\n        this.fetch = (0,_helper__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(customFetch);\n    }\n    /**\n     * Updates the authorization header\n     * @param token - the new jwt token sent in the authorisation header\n     */\n    setAuth(token) {\n        this.headers.Authorization = `Bearer ${token}`;\n    }\n    /**\n     * Invokes a function\n     * @param functionName - The name of the Function to invoke.\n     * @param options - Options for invoking the Function.\n     */\n    invoke(functionName, options = {}) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const { headers, method, body: functionArgs } = options;\n                let _headers = {};\n                let { region } = options;\n                if (!region) {\n                    region = this.region;\n                }\n                // Add region as query parameter using URL API\n                const url = new URL(`${this.url}/${functionName}`);\n                if (region && region !== 'any') {\n                    _headers['x-region'] = region;\n                    url.searchParams.set('forceFunctionRegion', region);\n                }\n                let body;\n                if (functionArgs &&\n                    ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)) {\n                    if ((typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||\n                        functionArgs instanceof ArrayBuffer) {\n                        // will work for File as File inherits Blob\n                        // also works for ArrayBuffer as it is the same underlying structure as a Blob\n                        _headers['Content-Type'] = 'application/octet-stream';\n                        body = functionArgs;\n                    }\n                    else if (typeof functionArgs === 'string') {\n                        // plain string\n                        _headers['Content-Type'] = 'text/plain';\n                        body = functionArgs;\n                    }\n                    else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n                        // don't set content-type headers\n                        // Request will automatically add the right boundary value\n                        body = functionArgs;\n                    }\n                    else {\n                        // default, assume this is JSON\n                        _headers['Content-Type'] = 'application/json';\n                        body = JSON.stringify(functionArgs);\n                    }\n                }\n                const response = yield this.fetch(url.toString(), {\n                    method: method || 'POST',\n                    // headers priority is (high to low):\n                    // 1. invoke-level headers\n                    // 2. client-level headers\n                    // 3. default Content-Type header\n                    headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n                    body,\n                }).catch((fetchError) => {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsFetchError(fetchError);\n                });\n                const isRelayError = response.headers.get('x-relay-error');\n                if (isRelayError && isRelayError === 'true') {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsRelayError(response);\n                }\n                if (!response.ok) {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsHttpError(response);\n                }\n                let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n                let data;\n                if (responseType === 'application/json') {\n                    data = yield response.json();\n                }\n                else if (responseType === 'application/octet-stream') {\n                    data = yield response.blob();\n                }\n                else if (responseType === 'text/event-stream') {\n                    data = response;\n                }\n                else if (responseType === 'multipart/form-data') {\n                    data = yield response.formData();\n                }\n                else {\n                    // default to text\n                    data = yield response.text();\n                }\n                return { data, error: null, response };\n            }\n            catch (error) {\n                return {\n                    data: null,\n                    error,\n                    response: error instanceof _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsHttpError || error instanceof _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsRelayError\n                        ? error.context\n                        : undefined,\n                };\n            }\n        });\n    }\n}\n//# sourceMappingURL=FunctionsClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch)\n/* harmony export */ });\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\n//# sourceMappingURL=helper.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStmdW5jdGlvbnMtanNAMi40LjUvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9mdW5jdGlvbnMtanMvZGlzdC9tb2R1bGUvaGVscGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsbU9BQThCLFNBQVMsZ0JBQWdCO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2Z1bmN0aW9ucy1qc0AyLjQuNVxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGZ1bmN0aW9ucy1qc1xcZGlzdFxcbW9kdWxlXFxoZWxwZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHJlc29sdmVGZXRjaCA9IChjdXN0b21GZXRjaCkgPT4ge1xuICAgIGxldCBfZmV0Y2g7XG4gICAgaWYgKGN1c3RvbUZldGNoKSB7XG4gICAgICAgIF9mZXRjaCA9IGN1c3RvbUZldGNoO1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlb2YgZmV0Y2ggPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIF9mZXRjaCA9ICguLi5hcmdzKSA9PiBpbXBvcnQoJ0BzdXBhYmFzZS9ub2RlLWZldGNoJykudGhlbigoeyBkZWZhdWx0OiBmZXRjaCB9KSA9PiBmZXRjaCguLi5hcmdzKSk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBfZmV0Y2ggPSBmZXRjaDtcbiAgICB9XG4gICAgcmV0dXJuICguLi5hcmdzKSA9PiBfZmV0Y2goLi4uYXJncyk7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aGVscGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionRegion: () => (/* binding */ FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* binding */ FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* binding */ FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* binding */ FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* binding */ FunctionsRelayError)\n/* harmony export */ });\nclass FunctionsError extends Error {\n    constructor(message, name = 'FunctionsError', context) {\n        super(message);\n        this.name = name;\n        this.context = context;\n    }\n}\nclass FunctionsFetchError extends FunctionsError {\n    constructor(context) {\n        super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n    }\n}\nclass FunctionsRelayError extends FunctionsError {\n    constructor(context) {\n        super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n    }\n}\nclass FunctionsHttpError extends FunctionsError {\n    constructor(context) {\n        super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n    }\n}\n// Define the enum for the 'region' property\nvar FunctionRegion;\n(function (FunctionRegion) {\n    FunctionRegion[\"Any\"] = \"any\";\n    FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n    FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n    FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n    FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n    FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n    FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n    FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n    FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n    FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n    FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n    FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n    FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n    FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n    FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\n");

/***/ })

};
;