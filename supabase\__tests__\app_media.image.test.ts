import { test, expect, afterAll } from "vitest";
import { readFileSync } from "fs";
import { fileTypeFromBuffer } from "file-type";
import { mockProvider } from "./mocks/auth.user";

const provider = mockProvider();

afterAll(async () => {
  if (!provider.data) throw new Error("Customer data is undefined");
  if (!provider.client) throw new Error("Customer data is undefined");

  await provider.client.storage
    .from("account_avatar")
    .remove([provider.data.id]);
});

test(
  "Image upload should populate placeholder base64",
  async () => {
    if (!provider.data) throw new Error("Customer data is undefined");
    if (!provider.client) throw new Error("Customer data is undefined");

    const image = readFileSync("supabase/__tests__/objects/test-avatar.jpg");
    const imageType = await fileTypeFromBuffer(image);

    const { data: uploadInfo, error } = await provider.client.storage
      .from("account_avatar")
      .upload(provider.data.id, image, {
        upsert: true,
        metadata: {
          description: "Test Image"
        },
        contentType: imageType?.mime
      });

    expect(error).toBeNull();
    expect(uploadInfo?.id).toBeDefined();

    const { data: imageInfo, error: imageInfoError } =
      await provider.client.storage
        .from("account_avatar")
        .info(provider.data.id);

    expect(imageInfoError).toBeNull();
    expect(imageInfo?.id).toBe(uploadInfo?.id);
    expect(imageInfo?.metadata?.description).toBe("Test Image");

    if (!uploadInfo?.id) throw new Error("Upload info is undefined");

    let checkBase64;

    for (let i = 0; i < 5; i++) {
      checkBase64 = await provider.client
        .schema("app_media")
        .from("image")
        .select("*")
        .eq("object_id", uploadInfo?.id)
        .single();

      if (String(checkBase64?.data?.base64_placeholder).length > 10) break;

      console.log("Waiting for base64 placeholder...");

      await new Promise((resolve) => setTimeout(resolve, 3000));
    }

    console.log("Base64 placeholder:", checkBase64?.data?.base64_placeholder);

    expect(checkBase64?.data?.base64_placeholder?.length).greaterThan(
      10,
      "Base64 placeholder could not be generated. Make sure the API is running. To run the API, run `pnpm --filter api dev` in the root directory."
    );
  },
  3000 * 5 + 1000
);
