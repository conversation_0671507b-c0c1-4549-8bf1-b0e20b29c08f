"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+fast-memoize@2.2.7";
exports.ids = ["vendor-chunks/@formatjs+fast-memoize@2.2.7"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.7/node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.7/node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytmYXN0LW1lbW9pemVAMi4yLjcvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9mYXN0LW1lbW9pemUvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVGQUF1RjtBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGZvcm1hdGpzK2Zhc3QtbWVtb2l6ZUAyLjIuN1xcbm9kZV9tb2R1bGVzXFxAZm9ybWF0anNcXGZhc3QtbWVtb2l6ZVxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvL1xuLy8gTWFpblxuLy9cbmV4cG9ydCBmdW5jdGlvbiBtZW1vaXplKGZuLCBvcHRpb25zKSB7XG4gICAgdmFyIGNhY2hlID0gb3B0aW9ucyAmJiBvcHRpb25zLmNhY2hlID8gb3B0aW9ucy5jYWNoZSA6IGNhY2hlRGVmYXVsdDtcbiAgICB2YXIgc2VyaWFsaXplciA9IG9wdGlvbnMgJiYgb3B0aW9ucy5zZXJpYWxpemVyID8gb3B0aW9ucy5zZXJpYWxpemVyIDogc2VyaWFsaXplckRlZmF1bHQ7XG4gICAgdmFyIHN0cmF0ZWd5ID0gb3B0aW9ucyAmJiBvcHRpb25zLnN0cmF0ZWd5ID8gb3B0aW9ucy5zdHJhdGVneSA6IHN0cmF0ZWd5RGVmYXVsdDtcbiAgICByZXR1cm4gc3RyYXRlZ3koZm4sIHtcbiAgICAgICAgY2FjaGU6IGNhY2hlLFxuICAgICAgICBzZXJpYWxpemVyOiBzZXJpYWxpemVyLFxuICAgIH0pO1xufVxuLy9cbi8vIFN0cmF0ZWd5XG4vL1xuZnVuY3Rpb24gaXNQcmltaXRpdmUodmFsdWUpIHtcbiAgICByZXR1cm4gKHZhbHVlID09IG51bGwgfHwgdHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJyB8fCB0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJyk7IC8vIHx8IHR5cGVvZiB2YWx1ZSA9PT0gXCJzdHJpbmdcIiAndW5zYWZlJyBwcmltaXRpdmUgZm9yIG91ciBuZWVkc1xufVxuZnVuY3Rpb24gbW9uYWRpYyhmbiwgY2FjaGUsIHNlcmlhbGl6ZXIsIGFyZykge1xuICAgIHZhciBjYWNoZUtleSA9IGlzUHJpbWl0aXZlKGFyZykgPyBhcmcgOiBzZXJpYWxpemVyKGFyZyk7XG4gICAgdmFyIGNvbXB1dGVkVmFsdWUgPSBjYWNoZS5nZXQoY2FjaGVLZXkpO1xuICAgIGlmICh0eXBlb2YgY29tcHV0ZWRWYWx1ZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgY29tcHV0ZWRWYWx1ZSA9IGZuLmNhbGwodGhpcywgYXJnKTtcbiAgICAgICAgY2FjaGUuc2V0KGNhY2hlS2V5LCBjb21wdXRlZFZhbHVlKTtcbiAgICB9XG4gICAgcmV0dXJuIGNvbXB1dGVkVmFsdWU7XG59XG5mdW5jdGlvbiB2YXJpYWRpYyhmbiwgY2FjaGUsIHNlcmlhbGl6ZXIpIHtcbiAgICB2YXIgYXJncyA9IEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKGFyZ3VtZW50cywgMyk7XG4gICAgdmFyIGNhY2hlS2V5ID0gc2VyaWFsaXplcihhcmdzKTtcbiAgICB2YXIgY29tcHV0ZWRWYWx1ZSA9IGNhY2hlLmdldChjYWNoZUtleSk7XG4gICAgaWYgKHR5cGVvZiBjb21wdXRlZFZhbHVlID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICBjb21wdXRlZFZhbHVlID0gZm4uYXBwbHkodGhpcywgYXJncyk7XG4gICAgICAgIGNhY2hlLnNldChjYWNoZUtleSwgY29tcHV0ZWRWYWx1ZSk7XG4gICAgfVxuICAgIHJldHVybiBjb21wdXRlZFZhbHVlO1xufVxuZnVuY3Rpb24gYXNzZW1ibGUoZm4sIGNvbnRleHQsIHN0cmF0ZWd5LCBjYWNoZSwgc2VyaWFsaXplKSB7XG4gICAgcmV0dXJuIHN0cmF0ZWd5LmJpbmQoY29udGV4dCwgZm4sIGNhY2hlLCBzZXJpYWxpemUpO1xufVxuZnVuY3Rpb24gc3RyYXRlZ3lEZWZhdWx0KGZuLCBvcHRpb25zKSB7XG4gICAgdmFyIHN0cmF0ZWd5ID0gZm4ubGVuZ3RoID09PSAxID8gbW9uYWRpYyA6IHZhcmlhZGljO1xuICAgIHJldHVybiBhc3NlbWJsZShmbiwgdGhpcywgc3RyYXRlZ3ksIG9wdGlvbnMuY2FjaGUuY3JlYXRlKCksIG9wdGlvbnMuc2VyaWFsaXplcik7XG59XG5mdW5jdGlvbiBzdHJhdGVneVZhcmlhZGljKGZuLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIGFzc2VtYmxlKGZuLCB0aGlzLCB2YXJpYWRpYywgb3B0aW9ucy5jYWNoZS5jcmVhdGUoKSwgb3B0aW9ucy5zZXJpYWxpemVyKTtcbn1cbmZ1bmN0aW9uIHN0cmF0ZWd5TW9uYWRpYyhmbiwgb3B0aW9ucykge1xuICAgIHJldHVybiBhc3NlbWJsZShmbiwgdGhpcywgbW9uYWRpYywgb3B0aW9ucy5jYWNoZS5jcmVhdGUoKSwgb3B0aW9ucy5zZXJpYWxpemVyKTtcbn1cbi8vXG4vLyBTZXJpYWxpemVyXG4vL1xudmFyIHNlcmlhbGl6ZXJEZWZhdWx0ID0gZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBKU09OLnN0cmluZ2lmeShhcmd1bWVudHMpO1xufTtcbi8vXG4vLyBDYWNoZVxuLy9cbnZhciBPYmplY3RXaXRob3V0UHJvdG90eXBlQ2FjaGUgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gT2JqZWN0V2l0aG91dFByb3RvdHlwZUNhY2hlKCkge1xuICAgICAgICB0aGlzLmNhY2hlID0gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgICB9XG4gICAgT2JqZWN0V2l0aG91dFByb3RvdHlwZUNhY2hlLnByb3RvdHlwZS5nZXQgPSBmdW5jdGlvbiAoa2V5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNhY2hlW2tleV07XG4gICAgfTtcbiAgICBPYmplY3RXaXRob3V0UHJvdG90eXBlQ2FjaGUucHJvdG90eXBlLnNldCA9IGZ1bmN0aW9uIChrZXksIHZhbHVlKSB7XG4gICAgICAgIHRoaXMuY2FjaGVba2V5XSA9IHZhbHVlO1xuICAgIH07XG4gICAgcmV0dXJuIE9iamVjdFdpdGhvdXRQcm90b3R5cGVDYWNoZTtcbn0oKSk7XG52YXIgY2FjaGVEZWZhdWx0ID0ge1xuICAgIGNyZWF0ZTogZnVuY3Rpb24gY3JlYXRlKCkge1xuICAgICAgICByZXR1cm4gbmV3IE9iamVjdFdpdGhvdXRQcm90b3R5cGVDYWNoZSgpO1xuICAgIH0sXG59O1xuZXhwb3J0IHZhciBzdHJhdGVnaWVzID0ge1xuICAgIHZhcmlhZGljOiBzdHJhdGVneVZhcmlhZGljLFxuICAgIG1vbmFkaWM6IHN0cmF0ZWd5TW9uYWRpYyxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.7/node_modules/@formatjs/fast-memoize/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.7/node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.7/node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.7/node_modules/@formatjs/fast-memoize/lib/index.js\n");

/***/ })

};
;