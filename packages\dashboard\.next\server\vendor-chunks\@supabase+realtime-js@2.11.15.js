"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+realtime-js@2.11.15";
exports.ids = ["vendor-chunks/@supabase+realtime-js@2.11.15"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js":
/*!********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* binding */ REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* binding */ REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* binding */ REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* binding */ REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   \"default\": () => (/* binding */ RealtimeChannel)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_push__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/push */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n\n\n\n\n\n\nvar REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nvar REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nvar REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nconst REALTIME_CHANNEL_STATES = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nclass RealtimeChannel {\n    constructor(\n    /** Topic name can be any string. */\n    topic, params = { config: {} }, socket) {\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, '');\n        this.params.config = Object.assign({\n            broadcast: { ack: false, self: false },\n            presence: { key: '' },\n            private: false,\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive('ok', () => {\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent) => pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(() => {\n            this.rejoinTimer.reset();\n            this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('timeout', () => {\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__[\"default\"](this);\n        this.broadcastEndpointURL =\n            (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_4__.httpEndpointURL)(this.socket.endPoint) + '/api/broadcast';\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */\n    subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.state == _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed) {\n            const { config: { broadcast, presence, private: isPrivate }, } = this.params;\n            this._onError((e) => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence,\n                postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r) => r.filter)) !== null && _b !== void 0 ? _b : [],\n                private: isPrivate,\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this.updateJoinPayload(Object.assign({ config }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush\n                .receive('ok', async ({ postgres_changes }) => {\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n                else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for (let i = 0; i < bindingsLen; i++) {\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter }, } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter &&\n                            serverPostgresFilter.event === event &&\n                            serverPostgresFilter.schema === schema &&\n                            serverPostgresFilter.table === table &&\n                            serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), { id: serverPostgresFilter.id }));\n                        }\n                        else {\n                            this.unsubscribe();\n                            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            })\n                .receive('error', (error) => {\n                this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n                return;\n            })\n                .receive('timeout', () => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'track',\n            payload,\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'untrack',\n        }, opts);\n    }\n    on(type, filter, callback) {\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */\n    async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === 'broadcast') {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue\n                ? `Bearer ${this.socket.accessTokenValue}`\n                : '';\n            const options = {\n                method: 'POST',\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : '',\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private,\n                        },\n                    ],\n                }),\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? 'ok' : 'error';\n            }\n            catch (error) {\n                if (error.name === 'AbortError') {\n                    return 'timed out';\n                }\n                else {\n                    return 'error';\n                }\n            }\n        }\n        else {\n            return new Promise((resolve) => {\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve('ok');\n                }\n                push.receive('ok', () => resolve('ok'));\n                push.receive('error', () => resolve('error'));\n                push.receive('timeout', () => resolve('timed out'));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */\n    unsubscribe(timeout = this.timeout) {\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n        const onClose = () => {\n            this.socket.log('channel', `leave ${this.topic}`);\n            this._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, 'leave', this._joinRef());\n        };\n        this.joinPush.destroy();\n        let leavePush = null;\n        return new Promise((resolve) => {\n            leavePush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush\n                .receive('ok', () => {\n                onClose();\n                resolve('ok');\n            })\n                .receive('timeout', () => {\n                onClose();\n                resolve('timed out');\n            })\n                .receive('error', () => {\n                resolve('error');\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger('ok', {});\n            }\n        }).finally(() => {\n            leavePush === null || leavePush === void 0 ? void 0 : leavePush.destroy();\n        });\n    }\n    /**\n     * Teardown the channel.\n     *\n     * Destroys and stops related timers.\n     */\n    teardown() {\n        this.pushBuffer.forEach((push) => push.destroy());\n        this.rejoinTimer && clearTimeout(this.rejoinTimer.timer);\n        this.joinPush.destroy();\n    }\n    /** @internal */\n    async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(() => controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), { signal: controller.signal }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */\n    _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        }\n        else {\n            pushEvent.startTimeout();\n            this.pushBuffer.push(pushEvent);\n        }\n        return pushEvent;\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */\n    _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */\n    _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */\n    _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */\n    _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS;\n        const events = [close, error, leave, join];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n        }\n        if (['insert', 'update', 'delete'].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind) => {\n                var _a, _b, _c;\n                return (((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' ||\n                    ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower);\n            }).map((bind) => bind.callback(handledPayload, ref));\n        }\n        else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind) => {\n                var _a, _b, _c, _d, _e, _f;\n                if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n                    if ('id' in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return (bindId &&\n                            ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) &&\n                            (bindEvent === '*' ||\n                                (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) ===\n                                    ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase())));\n                    }\n                    else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return (bindEvent === '*' ||\n                            bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase()));\n                    }\n                }\n                else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind) => {\n                if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors,\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */\n    _isClosed() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n    }\n    /** @internal */\n    _isJoined() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n    }\n    /** @internal */\n    _isJoining() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n    }\n    /** @internal */\n    _isLeaving() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n    }\n    /** @internal */\n    _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */\n    _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback,\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        }\n        else {\n            this.bindings[typeLower] = [binding];\n        }\n        return this;\n    }\n    /** @internal */\n    _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n            var _a;\n            return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower &&\n                RealtimeChannel.isEqual(bind.filter, filter));\n        });\n        return this;\n    }\n    /** @internal */\n    static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for (const k in obj1) {\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */\n    _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */\n    _onClose(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */\n    _onError(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error, {}, (reason) => callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */\n    _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */\n    _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */\n    _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {},\n        };\n        if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n            records.new = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n            records.old = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n}\n//# sourceMappingURL=RealtimeChannel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeClient)\n/* harmony export */ });\n/* harmony import */ var isows__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! isows */ \"(ssr)/../../node_modules/.pnpm/isows@1.0.7_ws@8.18.2/node_modules/isows/_esm/index.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/serializer */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n\n\n\n\n\n\nconst noop = () => { };\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nclass RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers Deprecated: headers cannot be set on websocket connections and this option will be removed in the future.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.logLevel Sets the log level for Realtime\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */\n    constructor(endPoint, options) {\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = new Array();\n        this.endPoint = '';\n        this.httpEndpoint = '';\n        /** @deprecated headers cannot be set on websocket connections */\n        this.headers = {};\n        this.params = {};\n        this.timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs = 25000;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.heartbeatCallback = noop;\n        this.ref = 0;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new _lib_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: [],\n        };\n        this.accessToken = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */\n        this._resolveFetch = (customFetch) => {\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            }\n            else if (typeof fetch === 'undefined') {\n                _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n            }\n            else {\n                _fetch = fetch;\n            }\n            return (...args) => _fetch(...args);\n        };\n        this.endPoint = `${endPoint}/${_lib_constants__WEBPACK_IMPORTED_MODULE_0__.TRANSPORTS.websocket}`;\n        this.httpEndpoint = (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_3__.httpEndpointURL)(endPoint);\n        if (options === null || options === void 0 ? void 0 : options.transport) {\n            this.transport = options.transport;\n        }\n        else {\n            this.transport = null;\n        }\n        if (options === null || options === void 0 ? void 0 : options.params)\n            this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.timeout)\n            this.timeout = options.timeout;\n        if (options === null || options === void 0 ? void 0 : options.logger)\n            this.logger = options.logger;\n        if ((options === null || options === void 0 ? void 0 : options.logLevel) || (options === null || options === void 0 ? void 0 : options.log_level)) {\n            this.logLevel = options.logLevel || options.log_level;\n            this.params = Object.assign(Object.assign({}, this.params), { log_level: this.logLevel });\n        }\n        if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs)\n            this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n        const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n        if (accessTokenValue) {\n            this.accessTokenValue = accessTokenValue;\n            this.apiKey = accessTokenValue;\n        }\n        this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs)\n            ? options.reconnectAfterMs\n            : (tries) => {\n                return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n            };\n        this.encode = (options === null || options === void 0 ? void 0 : options.encode)\n            ? options.encode\n            : (payload, callback) => {\n                return callback(JSON.stringify(payload));\n            };\n        this.decode = (options === null || options === void 0 ? void 0 : options.decode)\n            ? options.decode\n            : this.serializer.decode.bind(this.serializer);\n        this.reconnectTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](async () => {\n            this.disconnect();\n            this.connect();\n        }, this.reconnectAfterMs);\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n        if (options === null || options === void 0 ? void 0 : options.worker) {\n            if (typeof window !== 'undefined' && !window.Worker) {\n                throw new Error('Web Worker is not supported');\n            }\n            this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n        this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */\n    connect() {\n        if (this.conn) {\n            return;\n        }\n        if (!this.transport) {\n            this.transport = isows__WEBPACK_IMPORTED_MODULE_5__.WebSocket;\n        }\n        if (!this.transport) {\n            throw new Error('No transport provided');\n        }\n        this.conn = new this.transport(this.endpointURL());\n        this.setupConnection();\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */\n    endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, { vsn: _lib_constants__WEBPACK_IMPORTED_MODULE_0__.VSN }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */\n    disconnect(code, reason) {\n        if (this.conn) {\n            this.conn.onclose = function () { }; // noop\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n            }\n            else {\n                this.conn.close();\n            }\n            this.conn = null;\n            // remove open handles\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.reconnectTimer.reset();\n            this.channels.forEach((channel) => channel.teardown());\n        }\n    }\n    /**\n     * Returns all created channels\n     */\n    getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */\n    async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */\n    async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel) => channel.unsubscribe()));\n        this.channels = [];\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */\n    log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */\n    connectionState() {\n        switch (this.conn && this.conn.readyState) {\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.connecting:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Connecting;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.open:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.closing:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closing;\n            default:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */\n    isConnected() {\n        return this.connectionState() === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n    }\n    channel(topic, params = { config: {} }) {\n        const realtimeTopic = `realtime:${topic}`;\n        const exists = this.getChannels().find((c) => c.topic === realtimeTopic);\n        if (!exists) {\n            const chan = new _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__[\"default\"](`realtime:${topic}`, params, this);\n            this.channels.push(chan);\n            return chan;\n        }\n        else {\n            return exists;\n        }\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */\n    push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = () => {\n            this.encode(data, (result) => {\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log('push', `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        }\n        else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */\n    async setAuth(token = null) {\n        let tokenToSend = token ||\n            (this.accessToken && (await this.accessToken())) ||\n            this.accessTokenValue;\n        if (this.accessTokenValue != tokenToSend) {\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel) => {\n                const payload = {\n                    access_token: tokenToSend,\n                    version: _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_VERSION,\n                };\n                tokenToSend && channel.updateJoinPayload(payload);\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend,\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */\n    async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            this.heartbeatCallback('disconnected');\n            return;\n        }\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n            this.heartbeatCallback('timeout');\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.WS_CLOSE_NORMAL, 'hearbeat timeout');\n            return;\n        }\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: 'phoenix',\n            event: 'heartbeat',\n            payload: {},\n            ref: this.pendingHeartbeatRef,\n        });\n        this.heartbeatCallback('sent');\n        await this.setAuth();\n    }\n    onHeartbeat(callback) {\n        this.heartbeatCallback = callback;\n    }\n    /**\n     * Flushes send buffer\n     */\n    flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback) => callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */\n    _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        }\n        else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */\n    _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c) => c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log('transport', `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */\n    _remove(channel) {\n        this.channels = this.channels.filter((c) => c.topic !== channel.topic);\n    }\n    /**\n     * Sets up connection handlers.\n     *\n     * @internal\n     */\n    setupConnection() {\n        if (this.conn) {\n            this.conn.binaryType = 'arraybuffer';\n            this.conn.onopen = () => this._onConnOpen();\n            this.conn.onerror = (error) => this._onConnError(error);\n            this.conn.onmessage = (event) => this._onConnMessage(event);\n            this.conn.onclose = (event) => this._onConnClose(event);\n        }\n    }\n    /** @internal */\n    _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg) => {\n            let { topic, event, payload, ref } = msg;\n            if (topic === 'phoenix' && event === 'phx_reply') {\n                this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error');\n            }\n            if (ref && ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            this.log('receive', `${payload.status || ''} ${topic} ${event} ${(ref && '(' + ref + ')') || ''}`, payload);\n            Array.from(this.channels)\n                .filter((channel) => channel._isMember(topic))\n                .forEach((channel) => channel._trigger(event, payload, ref));\n            this.stateChangeCallbacks.message.forEach((callback) => callback(msg));\n        });\n    }\n    /** @internal */\n    _onConnOpen() {\n        this.log('transport', `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this.reconnectTimer.reset();\n        if (!this.worker) {\n            this._startHeartbeat();\n        }\n        else {\n            if (!this.workerRef) {\n                this._startWorkerHeartbeat();\n            }\n        }\n        this.stateChangeCallbacks.open.forEach((callback) => callback());\n    }\n    /** @internal */\n    _startHeartbeat() {\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n    }\n    /** @internal */\n    _startWorkerHeartbeat() {\n        if (this.workerUrl) {\n            this.log('worker', `starting worker for from ${this.workerUrl}`);\n        }\n        else {\n            this.log('worker', `starting default worker`);\n        }\n        const objectUrl = this._workerObjectUrl(this.workerUrl);\n        this.workerRef = new Worker(objectUrl);\n        this.workerRef.onerror = (error) => {\n            this.log('worker', 'worker error', error.message);\n            this.workerRef.terminate();\n        };\n        this.workerRef.onmessage = (event) => {\n            if (event.data.event === 'keepAlive') {\n                this.sendHeartbeat();\n            }\n        };\n        this.workerRef.postMessage({\n            event: 'start',\n            interval: this.heartbeatIntervalMs,\n        });\n    }\n    /** @internal */\n    _onConnClose(event) {\n        this.log('transport', 'close', event);\n        this._triggerChanError();\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.reconnectTimer.scheduleTimeout();\n        this.stateChangeCallbacks.close.forEach((callback) => callback(event));\n    }\n    /** @internal */\n    _onConnError(error) {\n        this.log('transport', `${error}`);\n        this._triggerChanError();\n        this.stateChangeCallbacks.error.forEach((callback) => callback(error));\n    }\n    /** @internal */\n    _triggerChanError() {\n        this.channels.forEach((channel) => channel._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error));\n    }\n    /** @internal */\n    _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? '&' : '?';\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        }\n        else {\n            const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n}\n//# sourceMappingURL=RealtimeClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* binding */ REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   \"default\": () => (/* binding */ RealtimePresence)\n/* harmony export */ });\n/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nvar REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nclass RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */\n    constructor(channel, opts) {\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.caller = {\n            onJoin: () => { },\n            onLeave: () => { },\n            onSync: () => { },\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: 'presence_state',\n            diff: 'presence_diff',\n        };\n        this.channel._on(events.state, {}, (newState) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff) => {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            }\n            else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences) => {\n            this.channel._trigger('presence', {\n                event: 'join',\n                key,\n                currentPresences,\n                newPresences,\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences) => {\n            this.channel._trigger('presence', {\n                event: 'leave',\n                key,\n                currentPresences,\n                leftPresences,\n            });\n        });\n        this.onSync(() => {\n            this.channel._trigger('presence', { event: 'sync' });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */\n    static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences) => {\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences) => {\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m) => m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m) => m.presence_ref);\n                const joinedPresences = newPresences.filter((m) => curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m) => newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            }\n            else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, { joins, leaves }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */\n    static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves),\n        };\n        if (!onJoin) {\n            onJoin = () => { };\n        }\n        if (!onLeave) {\n            onLeave = () => { };\n        }\n        this.map(joins, (key, newPresences) => {\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m) => m.presence_ref);\n                const curPresences = currentPresences.filter((m) => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences) => {\n            let currentPresences = state[key];\n            if (!currentPresences)\n                return;\n            const presenceRefsToRemove = leftPresences.map((m) => m.presence_ref);\n            currentPresences = currentPresences.filter((m) => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0)\n                delete state[key];\n        });\n        return state;\n    }\n    /** @internal */\n    static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */\n    static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n            const presences = state[key];\n            if ('metas' in presences) {\n                newState[key] = presences.metas.map((presence) => {\n                    presence['presence_ref'] = presence['phx_ref'];\n                    delete presence['phx_ref'];\n                    delete presence['phx_ref_prev'];\n                    return presence;\n                });\n            }\n            else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */\n    static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */\n    onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */\n    onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */\n    onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */\n    inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n}\n//# sourceMappingURL=RealtimePresence.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RealtimeClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjE1L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcmVhbHRpbWUtanMvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBOEM7QUFDMEg7QUFDaEY7QUFDMEg7QUFDbE4iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcmVhbHRpbWUtanNAMi4xMS4xNVxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHJlYWx0aW1lLWpzXFxkaXN0XFxtb2R1bGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFsdGltZUNsaWVudCBmcm9tICcuL1JlYWx0aW1lQ2xpZW50JztcbmltcG9ydCBSZWFsdGltZUNoYW5uZWwsIHsgUkVBTFRJTUVfTElTVEVOX1RZUEVTLCBSRUFMVElNRV9QT1NUR1JFU19DSEFOR0VTX0xJU1RFTl9FVkVOVCwgUkVBTFRJTUVfU1VCU0NSSUJFX1NUQVRFUywgUkVBTFRJTUVfQ0hBTk5FTF9TVEFURVMsIH0gZnJvbSAnLi9SZWFsdGltZUNoYW5uZWwnO1xuaW1wb3J0IFJlYWx0aW1lUHJlc2VuY2UsIHsgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUywgfSBmcm9tICcuL1JlYWx0aW1lUHJlc2VuY2UnO1xuZXhwb3J0IHsgUmVhbHRpbWVQcmVzZW5jZSwgUmVhbHRpbWVDaGFubmVsLCBSZWFsdGltZUNsaWVudCwgUkVBTFRJTUVfTElTVEVOX1RZUEVTLCBSRUFMVElNRV9QT1NUR1JFU19DSEFOR0VTX0xJU1RFTl9FVkVOVCwgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUywgUkVBTFRJTUVfU1VCU0NSSUJFX1NUQVRFUywgUkVBTFRJTUVfQ0hBTk5FTF9TVEFURVMsIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js":
/*!******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHANNEL_EVENTS: () => (/* binding */ CHANNEL_EVENTS),\n/* harmony export */   CHANNEL_STATES: () => (/* binding */ CHANNEL_STATES),\n/* harmony export */   CONNECTION_STATE: () => (/* binding */ CONNECTION_STATE),\n/* harmony export */   DEFAULT_TIMEOUT: () => (/* binding */ DEFAULT_TIMEOUT),\n/* harmony export */   DEFAULT_VERSION: () => (/* binding */ DEFAULT_VERSION),\n/* harmony export */   SOCKET_STATES: () => (/* binding */ SOCKET_STATES),\n/* harmony export */   TRANSPORTS: () => (/* binding */ TRANSPORTS),\n/* harmony export */   VERSION: () => (/* binding */ VERSION),\n/* harmony export */   VSN: () => (/* binding */ VSN),\n/* harmony export */   WS_CLOSE_NORMAL: () => (/* binding */ WS_CLOSE_NORMAL)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js\");\n\nconst DEFAULT_VERSION = `realtime-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`;\nconst VSN = '1.0.0';\nconst VERSION = _version__WEBPACK_IMPORTED_MODULE_0__.version;\nconst DEFAULT_TIMEOUT = 10000;\nconst WS_CLOSE_NORMAL = 1000;\nvar SOCKET_STATES;\n(function (SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nvar CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nvar CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nvar TRANSPORTS;\n(function (TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nvar CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Push)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n\nclass Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */\n    constructor(channel, event, payload = {}, timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT) {\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = '';\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = '';\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived('timeout')) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef(),\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({ status, callback });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload) => {\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(() => {\n            this.trigger('timeout', {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent)\n            this.channel._trigger(this.refEvent, { status, response });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response, }) {\n        this.recHooks\n            .filter((h) => h.status === status)\n            .forEach((h) => h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n}\n//# sourceMappingURL=push.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Serializer)\n/* harmony export */ });\n// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nclass Serializer {\n    constructor() {\n        this.HEADER_LENGTH = 1;\n    }\n    decode(rawPayload, callback) {\n        if (rawPayload.constructor === ArrayBuffer) {\n            return callback(this._binaryDecode(rawPayload));\n        }\n        if (typeof rawPayload === 'string') {\n            return callback(JSON.parse(rawPayload));\n        }\n        return callback({});\n    }\n    _binaryDecode(buffer) {\n        const view = new DataView(buffer);\n        const decoder = new TextDecoder();\n        return this._decodeBroadcast(buffer, view, decoder);\n    }\n    _decodeBroadcast(buffer, view, decoder) {\n        const topicSize = view.getUint8(1);\n        const eventSize = view.getUint8(2);\n        let offset = this.HEADER_LENGTH + 2;\n        const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n        offset = offset + topicSize;\n        const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n        offset = offset + eventSize;\n        const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n        return { ref: null, topic: topic, event: event, payload: data };\n    }\n}\n//# sourceMappingURL=serializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js":
/*!**************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Timer)\n/* harmony export */ });\n/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nclass Timer {\n    constructor(callback, timerCalc) {\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n        this.timer = undefined;\n        this.tries = 0;\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n    }\n    reset() {\n        this.tries = 0;\n        clearTimeout(this.timer);\n    }\n    // Cancels any previous scheduleTimeout and schedules callback\n    scheduleTimeout() {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(() => {\n            this.tries = this.tries + 1;\n            this.callback();\n        }, this.timerCalc(this.tries + 1));\n    }\n}\n//# sourceMappingURL=timer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgresTypes: () => (/* binding */ PostgresTypes),\n/* harmony export */   convertCell: () => (/* binding */ convertCell),\n/* harmony export */   convertChangeData: () => (/* binding */ convertChangeData),\n/* harmony export */   convertColumn: () => (/* binding */ convertColumn),\n/* harmony export */   httpEndpointURL: () => (/* binding */ httpEndpointURL),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toJson: () => (/* binding */ toJson),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toTimestampString: () => (/* binding */ toTimestampString)\n/* harmony export */ });\n/**\n * Helpers to convert the change Payload into native JS types.\n */\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nvar PostgresTypes;\n(function (PostgresTypes) {\n    PostgresTypes[\"abstime\"] = \"abstime\";\n    PostgresTypes[\"bool\"] = \"bool\";\n    PostgresTypes[\"date\"] = \"date\";\n    PostgresTypes[\"daterange\"] = \"daterange\";\n    PostgresTypes[\"float4\"] = \"float4\";\n    PostgresTypes[\"float8\"] = \"float8\";\n    PostgresTypes[\"int2\"] = \"int2\";\n    PostgresTypes[\"int4\"] = \"int4\";\n    PostgresTypes[\"int4range\"] = \"int4range\";\n    PostgresTypes[\"int8\"] = \"int8\";\n    PostgresTypes[\"int8range\"] = \"int8range\";\n    PostgresTypes[\"json\"] = \"json\";\n    PostgresTypes[\"jsonb\"] = \"jsonb\";\n    PostgresTypes[\"money\"] = \"money\";\n    PostgresTypes[\"numeric\"] = \"numeric\";\n    PostgresTypes[\"oid\"] = \"oid\";\n    PostgresTypes[\"reltime\"] = \"reltime\";\n    PostgresTypes[\"text\"] = \"text\";\n    PostgresTypes[\"time\"] = \"time\";\n    PostgresTypes[\"timestamp\"] = \"timestamp\";\n    PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n    PostgresTypes[\"timetz\"] = \"timetz\";\n    PostgresTypes[\"tsrange\"] = \"tsrange\";\n    PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nconst convertChangeData = (columns, record, options = {}) => {\n    var _a;\n    const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n    return Object.keys(record).reduce((acc, rec_key) => {\n        acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n        return acc;\n    }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nconst convertColumn = (columnName, columns, record, skipTypes) => {\n    const column = columns.find((x) => x.name === columnName);\n    const colType = column === null || column === void 0 ? void 0 : column.type;\n    const value = record[columnName];\n    if (colType && !skipTypes.includes(colType)) {\n        return convertCell(colType, value);\n    }\n    return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nconst convertCell = (type, value) => {\n    // if data type is an array\n    if (type.charAt(0) === '_') {\n        const dataType = type.slice(1, type.length);\n        return toArray(value, dataType);\n    }\n    // If not null, convert to correct type.\n    switch (type) {\n        case PostgresTypes.bool:\n            return toBoolean(value);\n        case PostgresTypes.float4:\n        case PostgresTypes.float8:\n        case PostgresTypes.int2:\n        case PostgresTypes.int4:\n        case PostgresTypes.int8:\n        case PostgresTypes.numeric:\n        case PostgresTypes.oid:\n            return toNumber(value);\n        case PostgresTypes.json:\n        case PostgresTypes.jsonb:\n            return toJson(value);\n        case PostgresTypes.timestamp:\n            return toTimestampString(value); // Format to be consistent with PostgREST\n        case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n        case PostgresTypes.date: // To allow users to cast it based on Timezone\n        case PostgresTypes.daterange:\n        case PostgresTypes.int4range:\n        case PostgresTypes.int8range:\n        case PostgresTypes.money:\n        case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n        case PostgresTypes.text:\n        case PostgresTypes.time: // To allow users to cast it based on Timezone\n        case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n        case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n        case PostgresTypes.tsrange:\n        case PostgresTypes.tstzrange:\n            return noop(value);\n        default:\n            // Return the value for remaining types\n            return noop(value);\n    }\n};\nconst noop = (value) => {\n    return value;\n};\nconst toBoolean = (value) => {\n    switch (value) {\n        case 't':\n            return true;\n        case 'f':\n            return false;\n        default:\n            return value;\n    }\n};\nconst toNumber = (value) => {\n    if (typeof value === 'string') {\n        const parsedValue = parseFloat(value);\n        if (!Number.isNaN(parsedValue)) {\n            return parsedValue;\n        }\n    }\n    return value;\n};\nconst toJson = (value) => {\n    if (typeof value === 'string') {\n        try {\n            return JSON.parse(value);\n        }\n        catch (error) {\n            console.log(`JSON parse error: ${error}`);\n            return value;\n        }\n    }\n    return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nconst toArray = (value, type) => {\n    if (typeof value !== 'string') {\n        return value;\n    }\n    const lastIdx = value.length - 1;\n    const closeBrace = value[lastIdx];\n    const openBrace = value[0];\n    // Confirm value is a Postgres array by checking curly brackets\n    if (openBrace === '{' && closeBrace === '}') {\n        let arr;\n        const valTrim = value.slice(1, lastIdx);\n        // TODO: find a better solution to separate Postgres array data\n        try {\n            arr = JSON.parse('[' + valTrim + ']');\n        }\n        catch (_) {\n            // WARNING: splitting on comma does not cover all edge cases\n            arr = valTrim ? valTrim.split(',') : [];\n        }\n        return arr.map((val) => convertCell(type, val));\n    }\n    return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nconst toTimestampString = (value) => {\n    if (typeof value === 'string') {\n        return value.replace(' ', 'T');\n    }\n    return value;\n};\nconst httpEndpointURL = (socketUrl) => {\n    let url = socketUrl;\n    url = url.replace(/^ws/i, 'http');\n    url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n    return url.replace(/\\/+$/, '');\n};\n//# sourceMappingURL=transformers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js":
/*!****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.11.15';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjE1L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcmVhbHRpbWUtanMvZGlzdC9tb2R1bGUvbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcmVhbHRpbWUtanNAMi4xMS4xNVxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHJlYWx0aW1lLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjExLjE1Jztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js\n");

/***/ })

};
;