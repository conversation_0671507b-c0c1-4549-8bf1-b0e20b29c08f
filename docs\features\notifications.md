# Notifications

This document outlines potential notification events based on the database schema. These are suggestions that can be implemented to improve user engagement and communication within the E-Senpai platform.

## Types

### Account Management (`app_account`)

- **Welcome Notification**: When a new user signs up.
- **KYC Status Change**: When a user's KYC status changes to `pending`, `approved`, or `rejected`.
- **User Banned**: When a user is banned from the platform.
- **User Unbanned**: When a user's ban is lifted.

### Access Control (`app_access`)

- **Role Change**: When a user's role is changed (e.g., promoted to `provider` or `admin`).

### Provider & Order Management (`app_provider`)

- **Provider Application Submitted**: When a user submits a provider application.
- **Provider Application Status Change**: When a provider's application status changes to `approved` or `rejected`.
- **New Order**: When a provider receives a new order.
- **Order Status Update**: When the status of an order changes (e.g., `accepted`, `completed`, `cancelled`, `in_dispute`).
- **Review Approved**: When a submitted review is approved. Notify both the reviewer and the reviewed.
- **New Question**: When a provider receives a new question from a user.
- **Question Answered**: When a provider answers a question.

### Financial Transactions (`app_transaction`)

- **Successful Deposit**: When a user's deposit is successfully processed.
- **Token Transfer Received**: When a user receives a token transfer from another user.
- **Withdrawal Request Submitted**: When a user submits a withdrawal request inform the user about the processing time.
- **Withdrawal Request Status Change**: When a withdrawal request's status changes to `processing` or `completed`.
- **Escrow Funds Released**: When funds held in escrow are released to the provider.
- **Escrow Funds Refunded**: When funds held in escrow are refunded to the customer.

### Communication (`app_chat`)

- **New Conversation**: When a user is added to a new conversation.
- **New Chat Message**: When a user receives a new message in a conversation.

### Support (`app_support`)

- **New Support Ticket**: When a user's support ticket is successfully created.
- **Support Ticket Status Change**: When the status of a support ticket changes (e.g., `closed`).
- **New Comment on Support Ticket**: When a new comment is added to a user's support ticket.
- **Content Flagged**: When a user creates a flag, inform the sender that their flag has been received.

### Platform Updates (`app_wiki`)

- **New Announcement/News**: When a new article is published in the news section.
- **New Event**: When a new platform event is announced.
- **Roadmap Update**: When the status of a roadmap item changes (e.g., `in_progress`, `live`).

### Custom

- **Custom Notification**: Database function for sending custom notification to users to inform them of important events (deletion of gallery image etc.) related to their accounts.

## Delivery Channels

Notifications will be delivered through the following channels:

- **In-App**: Displayed within the E-Senpai web application.
- **Email**: Sent to the user's registered email address.

## User Preferences

To provide users with control over the notifications they receive, the following preferences should be available in their account settings:

- **Channel Control**: Users can enable or disable notifications for each delivery channel (`In-App`, `Email`) independently.
- **Category-based Subscriptions**: Users can subscribe to or unsubscribe from specific categories of notifications (e.g., Account Management, Order Management, Platform Updates). This allows for granular control over what updates they receive.

## Notification Payload

All notifications, regardless of the delivery channel, will share a standardized JSON payload. This ensures consistency and simplifies processing on the client-side and in our email templating engine.

```json
{
  "id": "uuid",
  "type": "string",
  "recipient_id": "uuid",
  "created_at": "timestamp",
  "read_at": "timestamp | null",
  "title_key": "string",
  "message_key": "string",
  "data": {
    "entity_id": "uuid",
    "actor_name": "string",
    "details": "object"
  },
  "action_url": "string"
}
```

- **`id`**: A unique identifier for the notification.
- **`type`**: The notification type, corresponding to the events listed in the "Types" section (e.g., `order.new`, `account.banned`).
- **`recipient_id`**: The ID of the user who will receive the notification.
- **`created_at`**: The timestamp when the notification was generated.
- **`read_at`**: The timestamp when the user marked the notification as read (for in-app notifications).
- **`title_key`**: The localization key for the notification title.
- **`message_key`**: The localization key for the notification message body.
- **`data`**: An object containing contextual information needed to construct the final message (e.g., an order ID, a username).
  - **`entity_id`**: The ID of the primary entity related to the notification (e.g., `order_id`, `ticket_id`).
  - **`actor_name`**: The name of the user or system that triggered the event.
  - **`details`**: Any other relevant data.
- **`action_url`**: A URL that the user can be redirected to upon clicking the notification.

## Content and Templates

All notification text will be managed using a localization framework to support multiple languages. The `title_key` and `message_key` from the payload will be used to look up the appropriate text strings.

- **Localization Keys**: Keys will follow a consistent, nested format (e.g., `notifications.orders.new.title`).
- **Dynamic Content**: The `data` object in the payload will provide the necessary variables (like `actor_name` or `entity_id`) to be interpolated into the message templates.
- **Email Templates**: For email notifications, dedicated HTML templates will be created. These templates will be designed to be responsive and consistent with the E-Senpai brand identity.

### Example

**Notification Type**: `order.new`

- **`title_key`**: `notifications.orders.new.title`
  - **EN**: "You Have a New Order!"
- **`message_key`**: `notifications.orders.new.message`
  - **EN**: "{actor_name} has placed a new order with you. Order ID: {entity_id}"
