"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+storage-js@2.10.4";
exports.ids = ["vendor-chunks/@supabase+storage-js@2.10.4"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/StorageClient.js":
/*!***************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/StorageClient.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageClient: () => (/* binding */ StorageClient)\n/* harmony export */ });\n/* harmony import */ var _packages_StorageFileApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./packages/StorageFileApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js\");\n/* harmony import */ var _packages_StorageBucketApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./packages/StorageBucketApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js\");\n\n\nclass StorageClient extends _packages_StorageBucketApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n    constructor(url, headers = {}, fetch, opts) {\n        super(url, headers, fetch, opts);\n    }\n    /**\n     * Perform file operation in a bucket.\n     *\n     * @param id The bucket id to operate on.\n     */\n    from(id) {\n        return new _packages_StorageFileApi__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this.url, this.headers, id, this.fetch);\n    }\n}\n//# sourceMappingURL=StorageClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N0b3JhZ2UtanMvZGlzdC9tb2R1bGUvU3RvcmFnZUNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUQ7QUFDSTtBQUNwRCw0QkFBNEIsa0VBQWdCO0FBQ25ELGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdFQUFjO0FBQ2pDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN0b3JhZ2UtanNcXGRpc3RcXG1vZHVsZVxcU3RvcmFnZUNsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU3RvcmFnZUZpbGVBcGkgZnJvbSAnLi9wYWNrYWdlcy9TdG9yYWdlRmlsZUFwaSc7XG5pbXBvcnQgU3RvcmFnZUJ1Y2tldEFwaSBmcm9tICcuL3BhY2thZ2VzL1N0b3JhZ2VCdWNrZXRBcGknO1xuZXhwb3J0IGNsYXNzIFN0b3JhZ2VDbGllbnQgZXh0ZW5kcyBTdG9yYWdlQnVja2V0QXBpIHtcbiAgICBjb25zdHJ1Y3Rvcih1cmwsIGhlYWRlcnMgPSB7fSwgZmV0Y2gsIG9wdHMpIHtcbiAgICAgICAgc3VwZXIodXJsLCBoZWFkZXJzLCBmZXRjaCwgb3B0cyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFBlcmZvcm0gZmlsZSBvcGVyYXRpb24gaW4gYSBidWNrZXQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gaWQgVGhlIGJ1Y2tldCBpZCB0byBvcGVyYXRlIG9uLlxuICAgICAqL1xuICAgIGZyb20oaWQpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBTdG9yYWdlRmlsZUFwaSh0aGlzLnVybCwgdGhpcy5oZWFkZXJzLCBpZCwgdGhpcy5mZXRjaCk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9U3RvcmFnZUNsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/StorageClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/constants.js":
/*!***************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/constants.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/version.js\");\n\nconst DEFAULT_HEADERS = { 'X-Client-Info': `storage-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N0b3JhZ2UtanMvZGlzdC9tb2R1bGUvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQztBQUM3QiwwQkFBMEIsK0JBQStCLDZDQUFPLENBQUM7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3RvcmFnZS1qc0AyLjEwLjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdG9yYWdlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlcnNpb24gfSBmcm9tICcuL3ZlcnNpb24nO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfSEVBREVSUyA9IHsgJ1gtQ2xpZW50LUluZm8nOiBgc3RvcmFnZS1qcy8ke3ZlcnNpb259YCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/errors.js":
/*!************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/errors.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageApiError: () => (/* binding */ StorageApiError),\n/* harmony export */   StorageError: () => (/* binding */ StorageError),\n/* harmony export */   StorageUnknownError: () => (/* binding */ StorageUnknownError),\n/* harmony export */   isStorageError: () => (/* binding */ isStorageError)\n/* harmony export */ });\nclass StorageError extends Error {\n    constructor(message) {\n        super(message);\n        this.__isStorageError = true;\n        this.name = 'StorageError';\n    }\n}\nfunction isStorageError(error) {\n    return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nclass StorageApiError extends StorageError {\n    constructor(message, status, statusCode) {\n        super(message);\n        this.name = 'StorageApiError';\n        this.status = status;\n        this.statusCode = statusCode;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            statusCode: this.statusCode,\n        };\n    }\n}\nclass StorageUnknownError extends StorageError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'StorageUnknownError';\n        this.originalError = originalError;\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N0b3JhZ2UtanMvZGlzdC9tb2R1bGUvbGliL2Vycm9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3RvcmFnZS1qc0AyLjEwLjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdG9yYWdlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcZXJyb3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBTdG9yYWdlRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5fX2lzU3RvcmFnZUVycm9yID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5uYW1lID0gJ1N0b3JhZ2VFcnJvcic7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIGlzU3RvcmFnZUVycm9yKGVycm9yKSB7XG4gICAgcmV0dXJuIHR5cGVvZiBlcnJvciA9PT0gJ29iamVjdCcgJiYgZXJyb3IgIT09IG51bGwgJiYgJ19faXNTdG9yYWdlRXJyb3InIGluIGVycm9yO1xufVxuZXhwb3J0IGNsYXNzIFN0b3JhZ2VBcGlFcnJvciBleHRlbmRzIFN0b3JhZ2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgc3RhdHVzLCBzdGF0dXNDb2RlKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLm5hbWUgPSAnU3RvcmFnZUFwaUVycm9yJztcbiAgICAgICAgdGhpcy5zdGF0dXMgPSBzdGF0dXM7XG4gICAgICAgIHRoaXMuc3RhdHVzQ29kZSA9IHN0YXR1c0NvZGU7XG4gICAgfVxuICAgIHRvSlNPTigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSxcbiAgICAgICAgICAgIG1lc3NhZ2U6IHRoaXMubWVzc2FnZSxcbiAgICAgICAgICAgIHN0YXR1czogdGhpcy5zdGF0dXMsXG4gICAgICAgICAgICBzdGF0dXNDb2RlOiB0aGlzLnN0YXR1c0NvZGUsXG4gICAgICAgIH07XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIFN0b3JhZ2VVbmtub3duRXJyb3IgZXh0ZW5kcyBTdG9yYWdlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIG9yaWdpbmFsRXJyb3IpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdTdG9yYWdlVW5rbm93bkVycm9yJztcbiAgICAgICAgdGhpcy5vcmlnaW5hbEVycm9yID0gb3JpZ2luYWxFcnJvcjtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lcnJvcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/fetch.js":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/fetch.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   head: () => (/* binding */ head),\n/* harmony export */   post: () => (/* binding */ post),\n/* harmony export */   put: () => (/* binding */ put),\n/* harmony export */   remove: () => (/* binding */ remove)\n/* harmony export */ });\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options) => __awaiter(void 0, void 0, void 0, function* () {\n    const Res = yield (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.resolveResponse)();\n    if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n        error\n            .json()\n            .then((err) => {\n            const status = error.status || 500;\n            const statusCode = (err === null || err === void 0 ? void 0 : err.statusCode) || status + '';\n            reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageApiError(_getErrorMessage(err), status, statusCode));\n        })\n            .catch((err) => {\n            reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError(_getErrorMessage(err), err));\n        });\n    }\n    else {\n        reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError(_getErrorMessage(error), error));\n    }\n});\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET' || !body) {\n        return params;\n    }\n    if ((0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(body)) {\n        params.headers = Object.assign({ 'Content-Type': 'application/json' }, options === null || options === void 0 ? void 0 : options.headers);\n        params.body = JSON.stringify(body);\n    }\n    else {\n        params.body = body;\n    }\n    return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            fetcher(url, _getRequestParams(method, options, parameters, body))\n                .then((result) => {\n                if (!result.ok)\n                    throw result;\n                if (options === null || options === void 0 ? void 0 : options.noResolveJson)\n                    return result;\n                return result.json();\n            })\n                .then((data) => resolve(data))\n                .catch((error) => handleError(error, reject, options));\n        });\n    });\n}\nfunction get(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'GET', url, options, parameters);\n    });\n}\nfunction post(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'POST', url, options, parameters, body);\n    });\n}\nfunction put(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'PUT', url, options, parameters, body);\n    });\n}\nfunction head(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'HEAD', url, Object.assign(Object.assign({}, options), { noResolveJson: true }), parameters);\n    });\n}\nfunction remove(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'DELETE', url, options, parameters, body);\n    });\n}\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N0b3JhZ2UtanMvZGlzdC9tb2R1bGUvbGliL2ZldGNoLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSxpQkFBaUIsU0FBSSxJQUFJLFNBQUk7QUFDN0IsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDZ0U7QUFDTDtBQUMzRDtBQUNBO0FBQ0Esc0JBQXNCLHlEQUFlO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixvREFBZTtBQUN0QyxTQUFTO0FBQ1Q7QUFDQSx1QkFBdUIsd0RBQW1CO0FBQzFDLFNBQVM7QUFDVDtBQUNBO0FBQ0EsbUJBQW1CLHdEQUFtQjtBQUN0QztBQUNBLENBQUM7QUFDRDtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxRQUFRLHVEQUFhO0FBQ3JCLHlDQUF5QyxvQ0FBb0M7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ087QUFDUDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ087QUFDUDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ087QUFDUDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ087QUFDUDtBQUNBLGtGQUFrRixjQUFjLHFCQUFxQjtBQUNySCxLQUFLO0FBQ0w7QUFDTztBQUNQO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN0b3JhZ2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFxmZXRjaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19hd2FpdGVyID0gKHRoaXMgJiYgdGhpcy5fX2F3YWl0ZXIpIHx8IGZ1bmN0aW9uICh0aGlzQXJnLCBfYXJndW1lbnRzLCBQLCBnZW5lcmF0b3IpIHtcbiAgICBmdW5jdGlvbiBhZG9wdCh2YWx1ZSkgeyByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBQID8gdmFsdWUgOiBuZXcgUChmdW5jdGlvbiAocmVzb2x2ZSkgeyByZXNvbHZlKHZhbHVlKTsgfSk7IH1cbiAgICByZXR1cm4gbmV3IChQIHx8IChQID0gUHJvbWlzZSkpKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcbiAgICAgICAgZnVuY3Rpb24gZnVsZmlsbGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yLm5leHQodmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiByZWplY3RlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvcltcInRocm93XCJdKHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgICAgZnVuY3Rpb24gc3RlcChyZXN1bHQpIHsgcmVzdWx0LmRvbmUgPyByZXNvbHZlKHJlc3VsdC52YWx1ZSkgOiBhZG9wdChyZXN1bHQudmFsdWUpLnRoZW4oZnVsZmlsbGVkLCByZWplY3RlZCk7IH1cbiAgICAgICAgc3RlcCgoZ2VuZXJhdG9yID0gZ2VuZXJhdG9yLmFwcGx5KHRoaXNBcmcsIF9hcmd1bWVudHMgfHwgW10pKS5uZXh0KCkpO1xuICAgIH0pO1xufTtcbmltcG9ydCB7IFN0b3JhZ2VBcGlFcnJvciwgU3RvcmFnZVVua25vd25FcnJvciB9IGZyb20gJy4vZXJyb3JzJztcbmltcG9ydCB7IGlzUGxhaW5PYmplY3QsIHJlc29sdmVSZXNwb25zZSB9IGZyb20gJy4vaGVscGVycyc7XG5jb25zdCBfZ2V0RXJyb3JNZXNzYWdlID0gKGVycikgPT4gZXJyLm1zZyB8fCBlcnIubWVzc2FnZSB8fCBlcnIuZXJyb3JfZGVzY3JpcHRpb24gfHwgZXJyLmVycm9yIHx8IEpTT04uc3RyaW5naWZ5KGVycik7XG5jb25zdCBoYW5kbGVFcnJvciA9IChlcnJvciwgcmVqZWN0LCBvcHRpb25zKSA9PiBfX2F3YWl0ZXIodm9pZCAwLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICBjb25zdCBSZXMgPSB5aWVsZCByZXNvbHZlUmVzcG9uc2UoKTtcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBSZXMgJiYgIShvcHRpb25zID09PSBudWxsIHx8IG9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdGlvbnMubm9SZXNvbHZlSnNvbikpIHtcbiAgICAgICAgZXJyb3JcbiAgICAgICAgICAgIC5qc29uKClcbiAgICAgICAgICAgIC50aGVuKChlcnIpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHN0YXR1cyA9IGVycm9yLnN0YXR1cyB8fCA1MDA7XG4gICAgICAgICAgICBjb25zdCBzdGF0dXNDb2RlID0gKGVyciA9PT0gbnVsbCB8fCBlcnIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGVyci5zdGF0dXNDb2RlKSB8fCBzdGF0dXMgKyAnJztcbiAgICAgICAgICAgIHJlamVjdChuZXcgU3RvcmFnZUFwaUVycm9yKF9nZXRFcnJvck1lc3NhZ2UoZXJyKSwgc3RhdHVzLCBzdGF0dXNDb2RlKSk7XG4gICAgICAgIH0pXG4gICAgICAgICAgICAuY2F0Y2goKGVycikgPT4ge1xuICAgICAgICAgICAgcmVqZWN0KG5ldyBTdG9yYWdlVW5rbm93bkVycm9yKF9nZXRFcnJvck1lc3NhZ2UoZXJyKSwgZXJyKSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmVqZWN0KG5ldyBTdG9yYWdlVW5rbm93bkVycm9yKF9nZXRFcnJvck1lc3NhZ2UoZXJyb3IpLCBlcnJvcikpO1xuICAgIH1cbn0pO1xuY29uc3QgX2dldFJlcXVlc3RQYXJhbXMgPSAobWV0aG9kLCBvcHRpb25zLCBwYXJhbWV0ZXJzLCBib2R5KSA9PiB7XG4gICAgY29uc3QgcGFyYW1zID0geyBtZXRob2QsIGhlYWRlcnM6IChvcHRpb25zID09PSBudWxsIHx8IG9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdGlvbnMuaGVhZGVycykgfHwge30gfTtcbiAgICBpZiAobWV0aG9kID09PSAnR0VUJyB8fCAhYm9keSkge1xuICAgICAgICByZXR1cm4gcGFyYW1zO1xuICAgIH1cbiAgICBpZiAoaXNQbGFpbk9iamVjdChib2R5KSkge1xuICAgICAgICBwYXJhbXMuaGVhZGVycyA9IE9iamVjdC5hc3NpZ24oeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sIG9wdGlvbnMgPT09IG51bGwgfHwgb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0aW9ucy5oZWFkZXJzKTtcbiAgICAgICAgcGFyYW1zLmJvZHkgPSBKU09OLnN0cmluZ2lmeShib2R5KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHBhcmFtcy5ib2R5ID0gYm9keTtcbiAgICB9XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgcGFyYW1zKSwgcGFyYW1ldGVycyk7XG59O1xuZnVuY3Rpb24gX2hhbmRsZVJlcXVlc3QoZmV0Y2hlciwgbWV0aG9kLCB1cmwsIG9wdGlvbnMsIHBhcmFtZXRlcnMsIGJvZHkpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgZmV0Y2hlcih1cmwsIF9nZXRSZXF1ZXN0UGFyYW1zKG1ldGhvZCwgb3B0aW9ucywgcGFyYW1ldGVycywgYm9keSkpXG4gICAgICAgICAgICAgICAgLnRoZW4oKHJlc3VsdCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmICghcmVzdWx0Lm9rKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyByZXN1bHQ7XG4gICAgICAgICAgICAgICAgaWYgKG9wdGlvbnMgPT09IG51bGwgfHwgb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0aW9ucy5ub1Jlc29sdmVKc29uKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQuanNvbigpO1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAudGhlbigoZGF0YSkgPT4gcmVzb2x2ZShkYXRhKSlcbiAgICAgICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiBoYW5kbGVFcnJvcihlcnJvciwgcmVqZWN0LCBvcHRpb25zKSk7XG4gICAgICAgIH0pO1xuICAgIH0pO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldChmZXRjaGVyLCB1cmwsIG9wdGlvbnMsIHBhcmFtZXRlcnMpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICByZXR1cm4gX2hhbmRsZVJlcXVlc3QoZmV0Y2hlciwgJ0dFVCcsIHVybCwgb3B0aW9ucywgcGFyYW1ldGVycyk7XG4gICAgfSk7XG59XG5leHBvcnQgZnVuY3Rpb24gcG9zdChmZXRjaGVyLCB1cmwsIGJvZHksIG9wdGlvbnMsIHBhcmFtZXRlcnMpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICByZXR1cm4gX2hhbmRsZVJlcXVlc3QoZmV0Y2hlciwgJ1BPU1QnLCB1cmwsIG9wdGlvbnMsIHBhcmFtZXRlcnMsIGJvZHkpO1xuICAgIH0pO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHB1dChmZXRjaGVyLCB1cmwsIGJvZHksIG9wdGlvbnMsIHBhcmFtZXRlcnMpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICByZXR1cm4gX2hhbmRsZVJlcXVlc3QoZmV0Y2hlciwgJ1BVVCcsIHVybCwgb3B0aW9ucywgcGFyYW1ldGVycywgYm9keSk7XG4gICAgfSk7XG59XG5leHBvcnQgZnVuY3Rpb24gaGVhZChmZXRjaGVyLCB1cmwsIG9wdGlvbnMsIHBhcmFtZXRlcnMpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICByZXR1cm4gX2hhbmRsZVJlcXVlc3QoZmV0Y2hlciwgJ0hFQUQnLCB1cmwsIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgb3B0aW9ucyksIHsgbm9SZXNvbHZlSnNvbjogdHJ1ZSB9KSwgcGFyYW1ldGVycyk7XG4gICAgfSk7XG59XG5leHBvcnQgZnVuY3Rpb24gcmVtb3ZlKGZldGNoZXIsIHVybCwgYm9keSwgb3B0aW9ucywgcGFyYW1ldGVycykge1xuICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgIHJldHVybiBfaGFuZGxlUmVxdWVzdChmZXRjaGVyLCAnREVMRVRFJywgdXJsLCBvcHRpb25zLCBwYXJhbWV0ZXJzLCBib2R5KTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZldGNoLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/helpers.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/helpers.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   recursiveToCamel: () => (/* binding */ recursiveToCamel),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveResponse: () => (/* binding */ resolveResponse)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n    if (typeof Response === 'undefined') {\n        // @ts-ignore\n        return (yield Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23))).Response;\n    }\n    return Response;\n});\nconst recursiveToCamel = (item) => {\n    if (Array.isArray(item)) {\n        return item.map((el) => recursiveToCamel(el));\n    }\n    else if (typeof item === 'function' || item !== Object(item)) {\n        return item;\n    }\n    const result = {};\n    Object.entries(item).forEach(([key, value]) => {\n        const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''));\n        result[newKey] = recursiveToCamel(value);\n    });\n    return result;\n};\n/**\n * Determine if input is a plain object\n * An object is plain if it's created by either {}, new Object(), or Object.create(null)\n * source: https://github.com/sindresorhus/is-plain-obj\n */\nconst isPlainObject = (value) => {\n    if (typeof value !== 'object' || value === null) {\n        return false;\n    }\n    const prototype = Object.getPrototypeOf(value);\n    return ((prototype === null ||\n        prototype === Object.prototype ||\n        Object.getPrototypeOf(prototype) === null) &&\n        !(Symbol.toStringTag in value) &&\n        !(Symbol.iterator in value));\n};\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/version.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/version.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n// generated by genversion\nconst version = '2.10.4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N0b3JhZ2UtanMvZGlzdC9tb2R1bGUvbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdG9yYWdlLWpzQDIuMTAuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN0b3JhZ2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGdlbmVyYXRlZCBieSBnZW52ZXJzaW9uXG5leHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjEwLjQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StorageBucketApi)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\nclass StorageBucketApi {\n    constructor(url, headers = {}, fetch, opts) {\n        const baseUrl = new URL(url);\n        // if legacy uri is used, replace with new storage host (disables request buffering to allow > 50GB uploads)\n        // \"project-ref.supabase.co\" becomes \"project-ref.storage.supabase.co\"\n        if (opts === null || opts === void 0 ? void 0 : opts.useNewHostname) {\n            const isSupabaseHost = /supabase\\.(co|in|red)$/.test(baseUrl.hostname);\n            if (isSupabaseHost && !baseUrl.hostname.includes('storage.supabase.')) {\n                baseUrl.hostname = baseUrl.hostname.replace('supabase.', 'storage.supabase.');\n            }\n        }\n        this.url = baseUrl.href;\n        this.headers = Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_HEADERS), headers);\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(fetch);\n    }\n    /**\n     * Retrieves the details of all Storage buckets within an existing project.\n     */\n    listBuckets() {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/bucket`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing Storage bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to retrieve.\n     */\n    getBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/bucket/${id}`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a new Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are creating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     * @returns newly created bucket id\n     * @param options.type (private-beta) specifies the bucket type. see `BucketType` for more details.\n     *   - default bucket type is `STANDARD`\n     */\n    createBucket(id, options = {\n        public: false,\n    }) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/bucket`, {\n                    id,\n                    name: id,\n                    type: options.type,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Updates a Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are updating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     */\n    updateBucket(id, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.put)(this.fetch, `${this.url}/bucket/${id}`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Removes all objects inside a single bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to empty.\n     */\n    emptyBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/bucket/${id}/empty`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n     * You must first `empty()` the bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to delete.\n     */\n    deleteBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.remove)(this.fetch, `${this.url}/bucket/${id}`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n}\n//# sourceMappingURL=StorageBucketApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js":
/*!*************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StorageFileApi)\n/* harmony export */ });\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nconst DEFAULT_SEARCH_OPTIONS = {\n    limit: 100,\n    offset: 0,\n    sortBy: {\n        column: 'name',\n        order: 'asc',\n    },\n};\nconst DEFAULT_FILE_OPTIONS = {\n    cacheControl: '3600',\n    contentType: 'text/plain;charset=UTF-8',\n    upsert: false,\n};\nclass StorageFileApi {\n    constructor(url, headers = {}, bucketId, fetch) {\n        this.url = url;\n        this.headers = headers;\n        this.bucketId = bucketId;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_0__.resolveFetch)(fetch);\n    }\n    /**\n     * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n     *\n     * @param method HTTP method.\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadOrUpdate(method, path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let body;\n                const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n                let headers = Object.assign(Object.assign({}, this.headers), (method === 'POST' && { 'x-upsert': String(options.upsert) }));\n                const metadata = options.metadata;\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                    if (metadata) {\n                        headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n                    }\n                }\n                if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n                    headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n                }\n                const cleanPath = this._removeEmptyFolders(path);\n                const _path = this._getFinalPath(cleanPath);\n                const data = yield (method == 'PUT' ? _lib_fetch__WEBPACK_IMPORTED_MODULE_1__.put : _lib_fetch__WEBPACK_IMPORTED_MODULE_1__.post)(this.fetch, `${this.url}/object/${_path}`, body, Object.assign({ headers }, ((options === null || options === void 0 ? void 0 : options.duplex) ? { duplex: options.duplex } : {})));\n                return {\n                    data: { path: cleanPath, id: data.Id, fullPath: data.Key },\n                    error: null,\n                };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Uploads a file to an existing bucket.\n     *\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    upload(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Upload a file with a token generated from `createSignedUploadUrl`.\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param token The token generated from `createSignedUploadUrl`\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadToSignedUrl(path, token, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const cleanPath = this._removeEmptyFolders(path);\n            const _path = this._getFinalPath(cleanPath);\n            const url = new URL(this.url + `/object/upload/sign/${_path}`);\n            url.searchParams.set('token', token);\n            try {\n                let body;\n                const options = Object.assign({ upsert: DEFAULT_FILE_OPTIONS.upsert }, fileOptions);\n                const headers = Object.assign(Object.assign({}, this.headers), { 'x-upsert': String(options.upsert) });\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                }\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.put)(this.fetch, url.toString(), body, { headers });\n                return {\n                    data: { path: cleanPath, fullPath: data.Key },\n                    error: null,\n                };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed upload URL.\n     * Signed upload URLs can be used to upload files to the bucket without further authentication.\n     * They are valid for 2 hours.\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n     */\n    createSignedUploadUrl(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                const headers = Object.assign({}, this.headers);\n                if (options === null || options === void 0 ? void 0 : options.upsert) {\n                    headers['x-upsert'] = 'true';\n                }\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.post)(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, { headers });\n                const url = new URL(this.url + data.url);\n                const token = url.searchParams.get('token');\n                if (!token) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.StorageError('No token returned by API');\n                }\n                return { data: { signedUrl: url.toString(), path, token }, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Replaces an existing file at the specified path with a new one.\n     *\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    update(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Moves an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n     * @param options The destination options.\n     */\n    move(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.post)(this.fetch, `${this.url}/object/move`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Copies an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n     * @param options The destination options.\n     */\n    copy(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.post)(this.fetch, `${this.url}/object/copy`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data: { path: data.Key }, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    createSignedUrl(path, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                let data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.post)(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({ expiresIn }, ((options === null || options === void 0 ? void 0 : options.transform) ? { transform: options.transform } : {})), { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n                data = { signedUrl };\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n     * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     */\n    createSignedUrls(paths, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.post)(this.fetch, `${this.url}/object/sign/${this.bucketId}`, { expiresIn, paths }, { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                return {\n                    data: data.map((datum) => (Object.assign(Object.assign({}, datum), { signedUrl: datum.signedURL\n                            ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`)\n                            : null }))),\n                    error: null,\n                };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n     *\n     * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    download(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n            const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n            const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n            const queryString = transformationQuery ? `?${transformationQuery}` : '';\n            try {\n                const _path = this._getFinalPath(path);\n                const res = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.get)(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n                    headers: this.headers,\n                    noResolveJson: true,\n                });\n                const data = yield res.blob();\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing file.\n     * @param path\n     */\n    info(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.get)(this.fetch, `${this.url}/object/info/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_0__.recursiveToCamel)(data), error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Checks the existence of a file.\n     * @param path\n     */\n    exists(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.head)(this.fetch, `${this.url}/object/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: true, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error) && error instanceof _lib_errors__WEBPACK_IMPORTED_MODULE_2__.StorageUnknownError) {\n                    const originalError = error.originalError;\n                    if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n                        return { data: false, error };\n                    }\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n     * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n     *\n     * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n     * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    getPublicUrl(path, options) {\n        const _path = this._getFinalPath(path);\n        const _queryString = [];\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n            ? `download=${options.download === true ? '' : options.download}`\n            : '';\n        if (downloadQueryParam !== '') {\n            _queryString.push(downloadQueryParam);\n        }\n        const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n        const renderPath = wantsTransformation ? 'render/image' : 'object';\n        const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n        if (transformationQuery !== '') {\n            _queryString.push(transformationQuery);\n        }\n        let queryString = _queryString.join('&');\n        if (queryString !== '') {\n            queryString = `?${queryString}`;\n        }\n        return {\n            data: { publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`) },\n        };\n    }\n    /**\n     * Deletes files within the same bucket\n     *\n     * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n     */\n    remove(paths) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.remove)(this.fetch, `${this.url}/object/${this.bucketId}`, { prefixes: paths }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Get file metadata\n     * @param id the file id to retrieve metadata\n     */\n    // async getMetadata(\n    //   id: string\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Update file metadata\n     * @param id the file id to update metadata\n     * @param meta the new file metadata\n     */\n    // async updateMetadata(\n    //   id: string,\n    //   meta: Metadata\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await post(\n    //       this.fetch,\n    //       `${this.url}/metadata/${id}`,\n    //       { ...meta },\n    //       { headers: this.headers }\n    //     )\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Lists all the files within a bucket.\n     * @param path The folder path.\n     * @param options Search options including limit (defaults to 100), offset, sortBy, and search\n     */\n    list(path, options, parameters) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), { prefix: path || '' });\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_1__.post)(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, { headers: this.headers }, parameters);\n                return { data, error: null };\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isStorageError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    encodeMetadata(metadata) {\n        return JSON.stringify(metadata);\n    }\n    toBase64(data) {\n        if (typeof Buffer !== 'undefined') {\n            return Buffer.from(data).toString('base64');\n        }\n        return btoa(data);\n    }\n    _getFinalPath(path) {\n        return `${this.bucketId}/${path.replace(/^\\/+/, '')}`;\n    }\n    _removeEmptyFolders(path) {\n        return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n    }\n    transformOptsToQueryString(transform) {\n        const params = [];\n        if (transform.width) {\n            params.push(`width=${transform.width}`);\n        }\n        if (transform.height) {\n            params.push(`height=${transform.height}`);\n        }\n        if (transform.resize) {\n            params.push(`resize=${transform.resize}`);\n        }\n        if (transform.format) {\n            params.push(`format=${transform.format}`);\n        }\n        if (transform.quality) {\n            params.push(`quality=${transform.quality}`);\n        }\n        return params.join('&');\n    }\n}\n//# sourceMappingURL=StorageFileApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.10.4/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js\n");

/***/ })

};
;