-- section <PERSON><PERSON>EMA
DROP SCHEMA IF EXISTS app_core CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_core
;

GRANT USAGE ON SCHEMA app_core TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_core TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_core TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_core TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_core
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_core
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_core
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor POST_STATUS
CREATE TYPE app_core.POST_STATUS AS ENUM('draft', 'published')
;

-- anchor <PERSON>NDER
CREATE TYPE app_core.GENDER AS ENUM('male', 'female')
;

-- anchor LOCALE
CREATE TYPE app_core.LOCALE AS ENUM('en', 'ko', 'ja', 'tr')
;

-- anchor DAY_OF_WEEK
CREATE TYPE app_core.DAY_OF_WEEK AS ENUM(
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday'
)
;

-- !section
-- section DOMAIN
-- anchor SLUG
CREATE DOMAIN app_core.SLUG AS TEXT CHECK (
  VALUE ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$'
)
;

-- !section
-- section TABLES
-- anchor rate_limit
CREATE TABLE app_core.rate_limit (ip inet, request_at TIMESTAMP)
;

-- anchor config
CREATE TABLE app_core.config (
  id BOOLEAN PRIMARY KEY DEFAULT TRUE CHECK (id), -- Always one row
  max_requests_per_minute INTEGER NOT NULL DEFAULT 100
)
;

-- !section
-- section FUNCTIONS
-- anchor update_exchange_rates
CREATE OR REPLACE FUNCTION app_core.update_exchange_rates () RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_api_url TEXT;
  v_api_call_secret TEXT;
BEGIN
  -- Get the API_URL secret from the vault
  SELECT decrypted_secret INTO v_api_url
  FROM vault.decrypted_secrets
  WHERE name = 'API_URL';

  -- Get the API_CALL_SECRET secret from the vault
  SELECT decrypted_secret INTO v_api_call_secret
  FROM vault.decrypted_secrets
  WHERE name = 'API_CALL_SECRET';

  -- Make an HTTP GET request to update exchange rates
  PERFORM net.http_get(
    url := v_api_url || '/update-exchange-rates',
    headers := jsonb_build_object(
      'X-API-CALL-SECRET', v_api_call_secret
    ),
    timeout_milliseconds := 30000
  );

  RETURN;
END;
$$
;

-- anchor get_first_jsonb_value
CREATE OR REPLACE FUNCTION app_core.get_first_jsonb_value (JSONB_OBJ JSONB) RETURNS TEXT LANGUAGE SQL IMMUTABLE SECURITY INVOKER AS $$
    SELECT
        SUBSTRING(VALUE::TEXT, 2, LENGTH(VALUE::TEXT) - 2)
    FROM
        JSONB_EACH(JSONB_OBJ)
    LIMIT 1
;
$$
;

-- anchor is_valid_color_jsonb
CREATE OR REPLACE FUNCTION app_core.is_valid_color_jsonb (jsonb_obj JSONB) RETURNS BOOLEAN LANGUAGE SQL AS $$
  SELECT
    CASE
      WHEN JSONB_TYPEOF(jsonb_obj) = 'string' AND jsonb_obj::text ~ '^"#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?"$' THEN TRUE
      WHEN JSONB_TYPEOF(jsonb_obj) = 'object' AND jsonb_obj ? 'light' AND jsonb_obj ? 'dark'
           AND (jsonb_obj ->> 'light') ~ '^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$'
           AND (jsonb_obj ->> 'dark') ~ '^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$' THEN TRUE
      ELSE FALSE
    END;
$$
;

-- anchor check_request
CREATE OR REPLACE FUNCTION app_core.check_request () RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  req_method TEXT := current_setting('request.method', TRUE);
  req_ip INET := split_part(
    current_setting('request.headers', TRUE)::json->>'x-forwarded-for',
    ',', 1
  )::INET;
  count_in_five_mins INTEGER;
  v_max_requests_per_minute INTEGER;
BEGIN
  IF req_method = 'GET'
  OR req_method = 'HEAD'
  OR req_method IS NULL THEN
    -- rate limiting can't be done on GET and HEAD requests
    RETURN;
  END IF;

  SELECT max_requests_per_minute INTO v_max_requests_per_minute FROM app_core.config WHERE id = TRUE;

  SELECT
    count(*) INTO count_in_five_mins
  FROM app_core.rate_limit
  WHERE
    ip = req_ip
    AND request_at BETWEEN NOW() - INTERVAL '1 minutes' AND NOW();

  IF count_in_five_mins > v_max_requests_per_minute THEN
    RAISE sqlstate 'PGRST'
    USING
      MESSAGE = json_build_object(
        'code', '429',
        'message', 'Rate limit exceeded, try again after a while'
      )::TEXT,
      detail = json_build_object('status', 420, 'headers', json_build_object(), 'status_text', 'Enhance Your Calm')::TEXT;
  END IF;

  INSERT INTO app_core.rate_limit (ip, request_at)
    VALUES (req_ip, NOW());
END;
$$
;

-- !section
-- section TRIGGER FUNCTIONS
-- anchor set_updated_at
CREATE OR REPLACE FUNCTION app_core.set_updated_at () RETURNS TRIGGER LANGUAGE plpgsql AS $$
  BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
  END;
  $$
;

-- anchor delete_related_row
CREATE OR REPLACE FUNCTION app_core.delete_related_row () RETURNS TRIGGER AS $$
DECLARE
    v_schema TEXT := TG_ARGV[0];
    v_target_table TEXT := TG_ARGV[1];
    v_source_column TEXT := TG_ARGV[2];
    v_target_column TEXT := TG_ARGV[3];
BEGIN
    IF TG_OP = 'DELETE' THEN
        EXECUTE format('DELETE FROM %I.%I WHERE %I = ($1).%I', v_schema, v_target_table, v_target_column, v_source_column) USING OLD;
    ELSIF TG_OP = 'INSERT' THEN
        EXECUTE format('DELETE FROM %I.%I WHERE %I = ($1).%I', v_schema, v_target_table, v_target_column, v_source_column) USING NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        EXECUTE format('DELETE FROM %I.%I WHERE %I = ($1).%I', v_schema, v_target_table, v_target_column, v_source_column) USING NEW;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
;

-- anchor validate_locale_columns
CREATE OR REPLACE FUNCTION app_core.validate_locale_columns () RETURNS TRIGGER AS $$
DECLARE
  col TEXT;
  val JSONB;
  clean_val JSONB;
  key TEXT;
  valid_key BOOLEAN;
BEGIN
  -- Process each column specified in the trigger arguments
  FOR i IN 0..TG_NARGS-1 LOOP
    col := TG_ARGV[i];

    -- Get the current value of the column
    EXECUTE format('SELECT ($1).%I', col)
      INTO val
      USING NEW;

    -- Allow NULL values to pass through without modification
    IF val IS NULL THEN
      -- Do nothing, keep the NULL value
      NULL;
    ELSIF JSONB_TYPEOF(val) = 'object' THEN
      -- Initialize clean_val as an empty object
      clean_val := '{}'::JSONB;

      -- Iterate through each key in the JSONB object
      FOR key IN SELECT * FROM JSONB_OBJECT_KEYS(val) LOOP
        -- Check if the key is a valid locale by checking if it exists in the LOCALE enum
        IF key = ANY (enum_range(NULL::app_core.LOCALE)::TEXT[]) THEN
          clean_val := clean_val || JSONB_BUILD_OBJECT(key, val->key);
        END IF;
      END LOOP;

      -- Update the column in NEW dynamically using jsonb_populate_record
      BEGIN
        -- Create a JSON object with the column name and cleaned value
        DECLARE
          update_json JSONB;
        BEGIN
          -- Build a JSON object like {"column_name": cleaned_value}
          update_json := jsonb_build_object(col, clean_val);

          -- Update NEW with this JSON object
          NEW := jsonb_populate_record(NEW, update_json);
        END;
      EXCEPTION WHEN OTHERS THEN
        -- If the dynamic update fails, log the error but continue processing
        RAISE NOTICE 'Could not update column %', col;
      END;
    END IF;
  END LOOP;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql
;

-- anchor check_circular_dependency
CREATE OR REPLACE FUNCTION app_core.check_circular_dependency () RETURNS TRIGGER AS $$
DECLARE
    path UUID[] := ARRAY[NEW.id];
    current_id UUID;
    p_schema TEXT := TG_ARGV[0];
    p_table TEXT := TG_ARGV[1];
    p_id_column TEXT := TG_ARGV[2];
    p_parent_id_column TEXT := TG_ARGV[3];
    p_message TEXT := TG_ARGV[4];
    query TEXT;
BEGIN
    EXECUTE format('SELECT $1.%I', p_parent_id_column) USING NEW INTO current_id;

    IF current_id IS NULL THEN
        RETURN NEW;
    END IF;

    WHILE current_id IS NOT NULL LOOP
        IF path @> ARRAY[current_id] THEN
            RAISE EXCEPTION '%', p_message;
        END IF;

        path := array_append(path, current_id);

        query := format('SELECT %I FROM %I.%I WHERE %I = $1',
            p_parent_id_column, p_schema, p_table, p_id_column);

        EXECUTE query USING current_id INTO current_id;
    END LOOP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql
;

-- anchor prevent_delete
CREATE OR REPLACE FUNCTION app_core.prevent_delete_operation () RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  RAISE EXCEPTION 'Deletes are not allowed on %.%', TG_TABLE_SCHEMA, TG_TABLE_NAME;
END;
$$
;

-- !section
-- section RLS POLICIES
-- anchor rate_limit
ALTER TABLE app_core.rate_limit ENABLE ROW LEVEL SECURITY
;

-- anchor config
ALTER TABLE app_core.config ENABLE ROW LEVEL SECURITY
;

-- !section
-- section CRONJOBS
-- anchor Clean Old Rate Limits
SELECT
  cron.schedule (
    'Clean Old Rate Limits',
    '0 */1 * * *', -- Run once every hour
    'DELETE FROM app_core.rate_limit WHERE request_at < NOW() - INTERVAL ''1 minutes'''
  )
;

-- !section
-- section POSTGREST CONFIGURATION
-- anchor authenticator role pgrst.db_pre_request setting
ALTER ROLE authenticator
SET
  pgrst.db_pre_request = 'app_core.check_request'
;

-- anchor pgrst reload config notification
NOTIFY pgrst,
'reload config'
;

-- !section
-- !section
-- section INDEXES
-- anchor rate_limit
CREATE INDEX rate_limit_ip_request_at_idx ON app_core.rate_limit (ip, request_at DESC)
;

-- !section