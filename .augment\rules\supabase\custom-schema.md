---
type: "agent_requested"
description: "Custom schema creation guide"
---

# Custom Schema Creation

Include the following for new custom schemas to grant privileges. When creating a new schema, remember to update `supabase/config.toml`.

```toml
[api]
schemas = [
    "myschema",
    "other_schemas"
]
```

```sql
-- section SCHEMA
GRANT USAGE ON SCHEMA myschema TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA myschema TO anon, authenticated, service_role;
GRANT ALL ON ALL ROUTINES IN SCHEMA myschema TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA myschema TO anon, authenticated, service_role;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA myschema GRANT ALL ON TABLES TO anon, authenticated, service_role;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA myschema GRANT ALL ON ROUTINES TO anon, authenticated, service_role;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA myschema GRANT ALL ON SEQUENCES TO anon, authenticated, service_role;
-- !section
```

After creating the schema file stop Supabase with `supabase stop --no-backup` function and restart with `supabase start` function.
