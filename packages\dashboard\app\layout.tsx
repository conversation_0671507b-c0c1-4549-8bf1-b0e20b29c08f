import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "shared/globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "E-Senpai Admin Dashboard",
  description: "Administrative dashboard for E-Senpai platform",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}
