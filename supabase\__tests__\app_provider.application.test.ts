import { test, expect, describe, beforeAll, afterAll } from "vitest";
import {
  mockApplication,
  mockFilledOutApplication
} from "./mocks/app_provider.application";
import { mockAdmin, mockCustomer, MockUser } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { readFileSync } from "fs";
import { join } from "path";
import { fileTypeFromBuffer } from "file-type";
const admin = mockAdmin();

// Track created storage objects for cleanup across all tests
const createdStorageObjects: Array<{ bucket: string; filename: string }> = [];

// Helper function to upload media files for testing
async function uploadMediaFiles(
  customer: MockUser,
  options: {
    avatar?: boolean;
    voice?: boolean;
    galleryCount?: number;
  } = {}
) {
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!customer.data) throw new Error("Customer data is undefined");

  type Result =
    | {
        data: {
          id: string;
          path: string;
          fullPath: string;
        };
        error: null;
      }
    | {
        data: null;
        error: unknown;
      };

  const results: {
    avatar?: Result;
    voice?: Result;
    gallery?: Result[];
    createdObjects?: Array<{ bucket: string; filename: string }>;
  } = {
    createdObjects: []
  };

  // Upload avatar if requested
  if (options.avatar) {
    const avatarPath = join(__dirname, "objects", "test-avatar.jpg");
    const avatarBuffer = readFileSync(avatarPath);
    const avatarType = await fileTypeFromBuffer(avatarBuffer);

    const avatarUpload = await customer.client.storage
      .from("account_avatar")
      .upload(customer.data.id, avatarBuffer, {
        upsert: true,
        contentType: avatarType?.mime
      });

    results.avatar = avatarUpload;

    // Track successful upload for cleanup
    if (!avatarUpload.error) {
      const objectInfo = {
        bucket: "account_avatar",
        filename: customer.data.id
      };
      results.createdObjects!.push(objectInfo);
      createdStorageObjects.push(objectInfo);
    }
  }

  // Upload voice if requested
  if (options.voice) {
    const voicePath = join(__dirname, "objects", "test-voice.mp3");
    const voiceBuffer = readFileSync(voicePath);
    const voiceType = await fileTypeFromBuffer(voiceBuffer);

    const voiceUpload = await customer.client.storage
      .from("provider_voice")
      .upload(customer.data.id, voiceBuffer, {
        upsert: true,
        contentType: voiceType?.mime
      });

    results.voice = voiceUpload;

    // Track successful upload for cleanup
    if (!voiceUpload.error) {
      const objectInfo = {
        bucket: "provider_voice",
        filename: customer.data.id
      };
      results.createdObjects!.push(objectInfo);
      createdStorageObjects.push(objectInfo);
    }
  }

  // Upload gallery images if requested
  if (options.galleryCount && options.galleryCount > 0) {
    results.gallery = [];
    const galleryFiles = [
      "test-gallery-1.webp",
      "test-gallery-2.webp",
      "test-gallery-3.webp"
    ];

    for (
      let i = 0;
      i < Math.min(options.galleryCount, galleryFiles.length);
      i++
    ) {
      const galleryPath = join(__dirname, "objects", galleryFiles[i]);
      const galleryBuffer = readFileSync(galleryPath);
      const galleryType = await fileTypeFromBuffer(galleryBuffer);

      const filename = `${customer.data.id}_${i + 1}`;
      const galleryUpload = await customer.client.storage
        .from("provider_gallery")
        .upload(filename, galleryBuffer, {
          upsert: true,
          contentType: galleryType?.mime
        });

      results.gallery.push(galleryUpload);

      // Track successful upload for cleanup
      if (!galleryUpload.error) {
        const objectInfo = { bucket: "provider_gallery", filename };
        results.createdObjects!.push(objectInfo);
        createdStorageObjects.push(objectInfo);
      }
    }
  }

  return results;
}

// Add cleanup function for all storage objects created by uploadMediaFiles
afterAll(async () => {
  if (createdStorageObjects.length === 0) return;

  // Group filenames by bucket
  const bucketGroups = new Map<string, string[]>();
  for (const { bucket, filename } of createdStorageObjects) {
    if (!bucketGroups.has(bucket)) {
      bucketGroups.set(bucket, []);
    }
    bucketGroups.get(bucket)!.push(filename);
  }

  // Clean up each bucket using service client
  for (const [bucket, filenames] of bucketGroups) {
    try {
      if (filenames.length > 0) {
        await serviceClient.storage.from(bucket).remove(filenames);
      }
    } catch (error) {
      console.warn(`Failed to clean up ${bucket}:`, error);
    }
  }
});

describe("role", () => {
  describe("customer", () => {
    const customer = mockCustomer();
    mockApplication(customer);

    test("cannot submit application twice", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .insert({
          user_id: customer.data.id,
          application_status: "draft"
        });

      expect(submitApplication.error).not.toBeNull();
    });

    test("still has customer role", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");

      const { data: hasRole } = await customer.client
        .schema("app_access")
        .rpc("has_role", {
          role_name: "customer"
        });

      expect(hasRole).toBe(true);
    });

    test("acquires `provider_applicant` role on application creation", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");

      const { data: hasRole } = await customer.client
        .schema("app_access")
        .rpc("has_role", {
          role_name: "provider_applicant"
        });

      expect(hasRole).toBe(true);
    });

    test("cannot submit application if no profile and service created", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      // submit application
      const applicationUpdate = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).not.toBeNull();
    });

    test("cannot submit application without avatar", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      // Create profile and service first
      await customer.client
        .schema("app_provider")
        .from("profile")
        .insert({
          user_id: customer.data.id,
          slug: `test-provider-${customer.data.id.slice(0, 8)}`
        });

      await customer.client
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: customer.data.id,
          name: { en: "Test Service" },
          soda_amount: 100
        });

      // Upload voice and gallery but not avatar
      await uploadMediaFiles(customer, { voice: true, galleryCount: 3 });

      // Try to submit application
      const applicationUpdate = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).not.toBeNull();
      expect(applicationUpdate.error?.message).toContain("must have an avatar");
    });
  });

  describe("voice validation", () => {
    const customer = mockCustomer();
    mockApplication(customer);

    test("cannot submit application without voice", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      // Create profile and service first
      await customer.client
        .schema("app_provider")
        .from("profile")
        .insert({
          user_id: customer.data.id,
          slug: `voice-test-provider-${customer.data.id.slice(0, 8)}`
        });

      await customer.client
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: customer.data.id,
          name: { en: "Voice Test Service" },
          soda_amount: 100
        });

      // Upload avatar and gallery but not voice
      await uploadMediaFiles(customer, { avatar: true, galleryCount: 3 });

      // Try to submit application
      const applicationUpdate = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).not.toBeNull();
      expect(applicationUpdate.error?.message).toContain(
        "must have a voice file"
      );
    });
  });

  describe("gallery validation", () => {
    const customer = mockCustomer();
    mockApplication(customer);

    test("cannot submit application with less than 3 gallery images", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      // Create profile and service first
      await customer.client
        .schema("app_provider")
        .from("profile")
        .insert({
          user_id: customer.data.id,
          slug: `gallery-test-provider-${customer.data.id.slice(0, 8)}`
        });

      await customer.client
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: customer.data.id,
          name: { en: "Gallery Test Service" },
          soda_amount: 100
        });

      // Upload avatar and voice but only 2 gallery images
      await uploadMediaFiles(customer, {
        avatar: true,
        voice: true,
        galleryCount: 2
      });

      // Try to submit application
      const applicationUpdate = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).not.toBeNull();
      expect(applicationUpdate.error?.message).toContain(
        "must have at least 3 gallery images"
      );
      expect(applicationUpdate.error?.message).toContain("Currently has 2");
    });
  });

  describe("provider_applicant", () => {
    const customer = mockCustomer();
    const filledOutApplication = mockFilledOutApplication(customer);

    test("can update profile, service and service modifier", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!filledOutApplication.service.providerServiceId) {
        throw new Error("Service ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceModifierId) {
        throw new Error("Service Modifier ID is undefined");
      }

      const updateProfile = await customer.client
        .schema("app_provider")
        .from("profile")
        .update({ bio: { en: "Updated bio" } })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(updateProfile.data?.bio).toEqual({ en: "Updated bio" });

      const updateService = await customer.client
        .schema("app_provider")
        .from("service")
        .update({ name: { en: "Updated Service" } })
        .eq("id", filledOutApplication.service.providerServiceId)
        .select("*")
        .single();

      expect(updateService.data?.name).toEqual({ en: "Updated Service" });

      const updateServiceModifier = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .update({ name: { en: "Updated Modifier" } })
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .select("*")
        .single();

      expect(updateServiceModifier.data?.name).toEqual({
        en: "Updated Modifier"
      });
    });

    test("can submit application with all required media", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      // Upload all required media files
      const mediaResults = await uploadMediaFiles(customer, {
        avatar: true,
        voice: true,
        galleryCount: 3
      });

      // Verify uploads were successful
      expect(mediaResults.avatar?.error).toBeNull();
      expect(mediaResults.voice?.error).toBeNull();
      expect(mediaResults.gallery?.every((g) => g.error === null)).toBe(true);

      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(submitApplication.error).toBeNull();
      expect(submitApplication.data?.application_status).toBe("submitted");
    });
  });

  describe("provider_applicant_under_review", () => {
    const customer = mockCustomer();
    const filledOutApplication = mockFilledOutApplication(customer);

    beforeAll(async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(submitApplication.data?.application_status).toBe("submitted");
    });

    test("can view application, profile, activity, service and service modifier", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!filledOutApplication.service.providerServiceId) {
        throw new Error("Service ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceModifierId) {
        throw new Error("Service Modifier ID is undefined");
      }
      if (!filledOutApplication.service.selectedActivityId) {
        throw new Error("Activity ID is undefined");
      }

      const viewApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(viewApplication.data?.application_status).toBe("submitted");

      const viewProfile = await customer.client
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(viewProfile.data?.user_id).toBe(customer.data.id);

      const viewActivity = await customer.client
        .schema("app_provider")
        .from("activity")
        .select("*")
        .eq("id", filledOutApplication.service.selectedActivityId)
        .single();

      expect(viewActivity.data?.id).toBe(
        filledOutApplication.service.selectedActivityId
      );

      const viewService = await customer.client
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceId)
        .single();

      expect(viewService.data?.id).toBe(
        filledOutApplication.service.providerServiceId
      );

      const viewServiceModifier = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .single();

      expect(viewServiceModifier.data?.id).toBe(
        filledOutApplication.service.providerServiceModifierId
      );
    });

    test("cannot update or delete, application, profile, activity, service and service modifier", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!filledOutApplication.service.selectedActivityId) {
        throw new Error("Activity ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceId) {
        throw new Error("Service ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceModifierId) {
        throw new Error("Service Modifier ID is undefined");
      }

      await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "draft" })
        .eq("user_id", customer.data.id);

      await customer.client
        .schema("app_provider")
        .from("application")
        .delete()
        .eq("user_id", customer.data.id);

      const checkApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(checkApplication.data?.application_status).toBe("submitted");

      await customer.client
        .schema("app_provider")
        .from("profile")
        .update({ bio: { en: "Updated bio" } })
        .eq("user_id", customer.data.id);

      const checkProfileUpdate = await customer.client
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(checkProfileUpdate.data?.bio).not.toEqual({ en: "Updated bio" });

      // delete profile
      await customer.client
        .schema("app_provider")
        .from("profile")
        .delete()
        .eq("user_id", customer.data.id);

      const checkProfileDelete = await customer.client
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(checkProfileDelete.data).toBeDefined();

      // delete activity
      await customer.client
        .schema("app_provider")
        .from("activity")
        .delete()
        .eq("id", filledOutApplication.service.selectedActivityId);

      const checkActivityDelete = await customer.client
        .schema("app_provider")
        .from("activity")
        .select("*")
        .eq("id", filledOutApplication.service.selectedActivityId)
        .single();

      expect(checkActivityDelete.data).toBeDefined();

      // update service
      await customer.client
        .schema("app_provider")
        .from("service")
        .update({ soda_amount: 66 })
        .eq("id", filledOutApplication.service.providerServiceId);

      const checkServiceUpdate = await customer.client
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceId)
        .single();

      expect(checkServiceUpdate.data?.soda_amount).not.toBe(66);

      // delete service
      await customer.client
        .schema("app_provider")
        .from("service")
        .delete()
        .eq("id", filledOutApplication.service.providerServiceId);

      const checkServiceDelete = await customer.client
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceId)
        .single();

      expect(checkServiceDelete.data).toBeDefined();

      const updateServiceModifier = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .update({ soda_amount: 66 })
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .select()
        .single();

      expect(updateServiceModifier.data?.soda_amount).not.toBe(66);

      // delete service modifier
      await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .delete()
        .eq("id", filledOutApplication.service.providerServiceModifierId);

      const checkServiceModifierDelete = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .single();

      expect(checkServiceModifierDelete.data).toBeDefined();
    });

    test("role switches to `provider` when application is approved", async () => {
      if (!admin.client) throw new Error("Admin client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const applicationUpdate = await admin.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "approved" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).toBeNull();
      expect(applicationUpdate.data?.application_status).toBe("approved");

      const { data: hasProviderRole } = await customer.client
        .schema("app_access")
        .rpc("has_role", {
          role_name: "provider"
        });

      expect(hasProviderRole).toBe(true);
    });
  });
});

describe("application rejection", () => {
  const customer = mockCustomer();
  mockFilledOutApplication(customer);

  // submit application
  beforeAll(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");

    const submitApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .update({ application_status: "submitted" })
      .eq("user_id", customer.data.id)
      .select("*")
      .single();

    expect(submitApplication.data?.application_status).toBe("submitted");

    const rejectApplication = await admin.client
      .schema("app_provider")
      .from("application")
      .update({ application_status: "rejected" })
      .eq("user_id", customer.data.id)
      .select("*")
      .single();

    expect(rejectApplication.error).toBeNull();
    expect(rejectApplication.data?.application_status).toBe("rejected");
  });

  test("application roles are removed when application is rejected", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const { data: hasProviderRole } = await customer.client
      .schema("app_access")
      .rpc("has_role", {
        role_name: "provider"
      });

    expect(hasProviderRole).toBe(false);

    const { data: hasProviderApplicantRole } = await customer.client
      .schema("app_access")
      .rpc("has_role", {
        role_name: "provider_applicant"
      });

    expect(hasProviderApplicantRole).toBe(false);

    const { data: hasProviderApplicantUnderReviewRole } = await customer.client
      .schema("app_access")
      .rpc("has_role", {
        role_name: "provider_applicant_under_review"
      });

    expect(hasProviderApplicantUnderReviewRole).toBe(false);
  });

  test("provider data is deleted when application is rejected", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    const profileSelect = await serviceClient
      .schema("app_provider")
      .from("profile")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(profileSelect.data?.length).toBe(0);

    const activitySelect = await serviceClient
      .schema("app_provider")
      .from("activity")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(activitySelect.data?.length).toBe(0);

    const serviceSelect = await serviceClient
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(serviceSelect.data?.length).toBe(0);

    const serviceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(serviceModifierSelect.data?.length).toBe(0);

    const fieldValueSelect = await serviceClient
      .schema("app_provider")
      .from("field_value")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(fieldValueSelect.data?.length).toBe(0);
  });
});

describe("application approved", () => {
  const customer = mockCustomer();
  const filledOutApplication = mockFilledOutApplication(customer);

  // approve application
  beforeAll(async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const approveApplication = await admin.client
      .schema("app_provider")
      .from("application")
      .update({ application_status: "approved" })
      .eq("user_id", customer.data.id)
      .select("*")
      .single();

    expect(approveApplication.data?.application_status).toBe("approved");
  });

  test("service and service modifier are approved and their status are set to published", async () => {
    if (!filledOutApplication.service.providerServiceId) {
      throw new Error("Service ID is undefined");
    }
    if (!filledOutApplication.service.providerServiceModifierId) {
      throw new Error("Service Modifier ID is undefined");
    }

    const serviceSelect = await serviceClient
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("id", filledOutApplication.service.providerServiceId)
      .single();

    expect(serviceSelect.data?.status).toBe("published");

    const serviceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .select("*")
      .eq("id", filledOutApplication.service.providerServiceModifierId)
      .single();

    expect(serviceModifierSelect.data?.status).toBe("published");

    const approvedServiceSelect = await serviceClient
      .schema("app_provider")
      .from("approved_service")
      .select("*")
      .eq("service_id", filledOutApplication.service.providerServiceId)
      .single();

    expect(approvedServiceSelect.data?.service_id).toBe(
      filledOutApplication.service.providerServiceId
    );

    const approvedServiceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("approved_service_modifier")
      .select("*")
      .eq(
        "service_modifier_id",
        filledOutApplication.service.providerServiceModifierId
      )
      .single();

    expect(approvedServiceModifierSelect.data?.service_modifier_id).toBe(
      filledOutApplication.service.providerServiceModifierId
    );
  });

  test("profile and field values should stay", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!filledOutApplication.service.providerFieldValueId) {
      throw new Error("Field Value ID is undefined");
    }

    const profileSelect = await serviceClient
      .schema("app_provider")
      .from("profile")
      .select("*")
      .eq("user_id", customer.data?.id)
      .single();

    expect(profileSelect.data?.user_id).toBe(customer.data?.id);

    const fieldValueSelect = await serviceClient
      .schema("app_provider")
      .from("field_value")
      .select("*")
      .eq("id", filledOutApplication.service.providerFieldValueId)
      .single();

    expect(fieldValueSelect.data?.id).toBe(
      filledOutApplication.service.providerFieldValueId
    );
  });
});

describe("media validation requirements", () => {
  describe("complete media requirements", () => {
    const customer = mockCustomer();
    mockApplication(customer);

    test("can submit application with all media requirements met", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      // Create profile and service
      await customer.client
        .schema("app_provider")
        .from("profile")
        .insert({
          user_id: customer.data.id,
          slug: `complete-provider-${customer.data.id.slice(0, 8)}`
        });

      await customer.client
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: customer.data.id,
          name: { en: "Complete Service" },
          soda_amount: 100
        });

      // Upload all required media
      const mediaResults = await uploadMediaFiles(customer, {
        avatar: true,
        voice: true,
        galleryCount: 4 // More than minimum required
      });

      // Verify all uploads were successful
      expect(mediaResults.avatar?.error).toBeNull();
      expect(mediaResults.voice?.error).toBeNull();
      expect(mediaResults.gallery?.every((g) => g.error === null)).toBe(true);

      // Should be able to submit application
      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(submitApplication.error).toBeNull();
      expect(submitApplication.data?.application_status).toBe("submitted");
    });
  });

  describe("missing media requirements", () => {
    const customer = mockCustomer();
    mockApplication(customer);

    test("cannot submit without any media files", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      // Create profile and service but no media
      await customer.client
        .schema("app_provider")
        .from("profile")
        .insert({
          user_id: customer.data.id,
          slug: `no-media-provider-${customer.data.id.slice(0, 8)}`
        });

      await customer.client
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: customer.data.id,
          name: { en: "No Media Service" },
          soda_amount: 100
        });

      // Try to submit without any media
      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(submitApplication.error).not.toBeNull();
      expect(submitApplication.error?.message).toContain("must have an avatar");
    });
  });
});

describe("prohibited", () => {
  const customer = mockCustomer();

  test("cannot insert with `submitted` status", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const submitApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .insert({
        user_id: customer.data.id,
        application_status: "submitted"
      });

    expect(submitApplication.error).not.toBeNull();
  });
});
