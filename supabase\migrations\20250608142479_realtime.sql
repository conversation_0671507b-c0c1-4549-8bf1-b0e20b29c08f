DROP POLICY if <PERSON><PERSON><PERSON><PERSON> "realtime_messages_select" ON "realtime"."messages"
;

DROP POLICY if <PERSON><PERSON>ISTS "realtime_messages_insert" ON "realtime"."messages"
;

CREATE POLICY "realtime_messages_select" ON "realtime"."messages" FOR
SELECT
  TO authenticated USING (
    (
      -- Chat messages: user must be a member of the conversation
      realtime.topic () LIKE 'chat:%'
      AND app_chat.is_member (
        REPLACE(
          realtime.topic (),
          'chat:',
          ''
        )::UUID,
        auth.uid ()
      )
      AND realtime.messages.extension IN ('broadcast', 'presence')
    )
    OR (
      -- Notification messages: user must be the recipient
      realtime.topic () LIKE 'notification:%'
      AND REPLACE(
        realtime.topic (),
        'notification:',
        ''
      )::UUID = auth.uid ()
      AND realtime.messages.extension IN ('broadcast', 'presence')
    )
    OR (
      -- Online presence: anyone can access
      realtime.topic () LIKE 'online:%'
      AND realtime.messages.extension IN ('presence')
    )
  )
;

CREATE POLICY "realtime_messages_insert" ON "realtime"."messages" FOR INSERT TO authenticated
WITH
  CHECK (
    (
      -- Chat presence: user must be a member of the conversation
      realtime.topic () LIKE 'chat:%'
      AND app_chat.is_member (
        REPLACE(
          realtime.topic (),
          'chat:',
          ''
        )::UUID,
        auth.uid ()
      )
      AND realtime.messages.extension = 'presence'
    )
    OR (
      -- Notification presence: user must be the recipient
      realtime.topic () LIKE 'notification:%'
      AND REPLACE(
        realtime.topic (),
        'notification:',
        ''
      )::UUID = auth.uid ()
      AND realtime.messages.extension = 'presence'
    )
    OR (
      -- Online presence: anyone can track
      realtime.topic () LIKE 'online:%'
      AND realtime.messages.extension = 'presence'
    )
  )
;
