import { User } from "@supabase/supabase-js";
import { dbClient, serviceClient } from "supabase/__tests__/utils/client";
import {
  getRateLimit,
  resetRateLimits,
  setRateLimit
} from "supabase/__tests__/utils/rate-limit";
import { exec } from "child_process";
import path from "path";

async function playWav(relativePath: string, repeat: number) {
  const fullPath = path.resolve(__dirname, relativePath);

  for (let i = 0; i < repeat; i++) {
    exec(
      `powershell -c (New-Object Media.SoundPlayer '${fullPath}').PlaySync();`
    );

    await new Promise((resolve) => setTimeout(resolve, 200));
  }
}

async function setupRateLimiting(): Promise<number> {
  const originalMaxRequestsPerMinute = await getRateLimit();
  await resetRateLimits();
  await setRateLimit(100000000);
  return originalMaxRequestsPerMinute;
}

async function cleanupRateLimiting(
  originalMaxRequestsPerMinute: number
): Promise<void> {
  await resetRateLimits();
  await setRateLimit(originalMaxRequestsPerMinute);
}

async function getUsersSnapshot() {
  const {
    data: { users }
  } = await serviceClient.auth.admin.listUsers();
  return users;
}

function checkLeftoverUsers(previousUsers: User[], finalUsers: User[]): void {
  const leftOverUsers = finalUsers.length - previousUsers.length;

  if (leftOverUsers > 0) {
    console.error(
      `<<< ATTENTION: ${leftOverUsers} leftover users. Debug whether this is a database or test issue. Please fix the issue to avoid this message. >>>`.toUpperCase()
    );
  }
}

export default async function globalSetup() {
  await dbClient.connect();

  const originalMaxRequestsPerMinute = await setupRateLimiting();

  const previousUsers = await getUsersSnapshot();

  return async () => {
    await cleanupRateLimiting(originalMaxRequestsPerMinute);

    const finalUsers = await getUsersSnapshot();

    checkLeftoverUsers(previousUsers, finalUsers);

    await dbClient.end();

    playWav("./notify.wav", 5);
  };
}
