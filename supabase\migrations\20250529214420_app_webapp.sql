-- section <PERSON><PERSON>EM<PERSON>
DROP SCHEMA IF EXISTS app_webapp CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_webapp
;

GRANT USAGE ON SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section FUNCTIONS
-- anchor match_provider_for_user
CREATE OR REPLACE FUNCTION app_webapp.match_provider_for_user (
  p_provider_ids UUID[] DEFAULT NULL
) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
  v_matched_provider_id UUID;
BEGIN
  -- Check if user has the capability to match with providers
  IF NOT app_access.has_capability('app_webapp.match_provider') THEN
    RAISE EXCEPTION 'You do not have permission to match with providers.';
  END IF;

  -- Find the best matching provider based on user's favorite activities
  SELECT p.user_id INTO v_matched_provider_id
  FROM app_provider.approved_user p
  INNER JOIN app_provider.status ps ON ps.user_id = p.user_id
  INNER JOIN app_provider.activity pa ON pa.user_id = p.user_id
  INNER JOIN app_account.favorite_activity fa ON fa.activity_id = pa.activity_id
  INNER JOIN app_provider.service s ON s.activity_id = pa.id
  INNER JOIN app_provider.approved_service aps ON aps.service_id = s.id
  LEFT JOIN app_provider.performance perf ON perf.user_id = p.user_id
  WHERE
    -- If provider IDs are provided, restrict to those providers; otherwise match all
    (p_provider_ids IS NULL OR array_length(p_provider_ids, 1) IS NULL OR p.user_id = ANY(p_provider_ids))
    AND fa.user_id = v_current_user_id
    AND ps.is_open_for_orders = TRUE
    AND s.status = 'published'
  ORDER BY
    COALESCE(perf.rating, 0) DESC,
    COALESCE(perf.completed_orders, 0) DESC,
    p.created_at ASC
  LIMIT 1;

  RETURN v_matched_provider_id;
END;
$$
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY['app_webapp.match_provider']
  )
;

-- anchor customer
SELECT
  app_access.define_role_capability (
    'customer',
    ARRAY['app_webapp.match_provider']
  )
;

-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY['app_webapp.match_provider']
  )
;

-- !section
-- section MATERIALIZED VIEWS
-- anchor caps_leaderboard
CREATE MATERIALIZED VIEW app_webapp.caps_leaderboard AS
SELECT
  ROW_NUMBER() OVER (
    ORDER BY
      w.cap_balance DESC,
      p.join_date ASC
  ) AS rank,
  w.user_id,
  w.cap_balance,
  p.username,
  p.nickname,
  p.join_date
FROM
  app_transaction.wallet w
  INNER JOIN app_account.profile p ON w.user_id = p.user_id
  LEFT JOIN app_account.privacy pr ON w.user_id = pr.user_id
WHERE
  w.cap_balance > 0
  AND COALESCE(pr.show_in_leaderboard, TRUE) = TRUE
ORDER BY
  w.cap_balance DESC,
  p.join_date ASC
LIMIT
  20
;

-- Create index for better performance
CREATE UNIQUE INDEX caps_leaderboard_user_id_idx ON app_webapp.caps_leaderboard (user_id)
;

-- !section
-- section FUNCTIONS
-- anchor refresh_caps_leaderboard
CREATE OR REPLACE FUNCTION app_webapp.refresh_caps_leaderboard () RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY app_webapp.caps_leaderboard;
END;
$$
;

-- anchor get_user_caps_rank
CREATE OR REPLACE FUNCTION app_webapp.get_user_caps_rank (
  p_user_id UUID DEFAULT auth.uid ()
) RETURNS TABLE (
  rank BIGINT,
  cap_balance app_transaction.TOKEN_UNIT,
  total_users BIGINT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    cl.rank,
    cl.cap_balance,
    (SELECT COUNT(*) FROM app_webapp.caps_leaderboard) as total_users
  FROM app_webapp.caps_leaderboard cl
  WHERE cl.user_id = p_user_id;
END;
$$
;

-- !section
-- section RLS POLICIES
-- anchor caps_leaderboard
ALTER MATERIALIZED VIEW app_webapp.caps_leaderboard OWNER TO postgres
;

-- Grant select permissions
GRANT
SELECT
  ON app_webapp.caps_leaderboard TO anon,
  authenticated,
  service_role
;

-- !section
-- section CRONJOBS
-- anchor Refresh Caps Leaderboard
SELECT
  cron.schedule (
    'Refresh Caps Leaderboard',
    '0 */1 * * *', -- Run once every hour
    'SELECT app_webapp.refresh_caps_leaderboard();'
  )
;

-- !section