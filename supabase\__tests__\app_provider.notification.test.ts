import { afterAll, describe, expect, test } from "vitest";
import { mockCustomer, mockProvider } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { findValidatedNotification } from "./utils/validation";

describe("Provider Notification System", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const createdNotificationIds: string[] = [];
  const createdApplicationIds: string[] = [];
  const createdServiceIds: string[] = [];
  const createdOrderIds: string[] = [];

  describe("Provider Application Notifications", () => {
    test("application status change notification is sent", async () => {
      if (!provider.data) throw new Error("Provider not defined");

      // Create a provider application
      const { data: application, error: applicationError } = await serviceClient
        .schema("app_provider")
        .from("application")
        .upsert({
          user_id: provider.data.id,
          application_status: "submitted"
        })
        .select()
        .single();

      if (!application) {
        throw new Error(
          `Application creation failed: ${applicationError?.message || "Unknown error"}`
        );
      }

      createdApplicationIds.push(application.user_id);

      // Admin updates application status
      await serviceClient
        .schema("app_provider")
        .from("application")
        .update({ application_status: "approved" })
        .eq("user_id", provider.data.id);

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "provider.application.status_change");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "provider.application.status_change",
        (data) =>
          data.old_status === "submitted" && data.new_status === "approved"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.old_status).toBe("submitted");
      expect(validatedNotification!.data.new_status).toBe("approved");
      expect(validatedNotification!.data.application_id).toBe(provider.data.id);

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });
  });

  describe("Order Notifications", () => {
    test("new order notification is sent to provider", async () => {
      if (!customer.data || !provider.data)
        throw new Error("Customer or provider not defined");

      // First create necessary dependencies
      // Create catalog activity
      const { data: catalogActivity } = await serviceClient
        .schema("app_catalog")
        .from("activity")
        .insert({
          name: { en: "Test Activity" },
          description: { en: "Test activity description" }
        })
        .select()
        .single();

      if (!catalogActivity) {
        throw new Error("Catalog activity creation failed");
      }

      // Create provider activity
      const { data: activity } = await serviceClient
        .schema("app_provider")
        .from("activity")
        .insert({
          user_id: provider.data.id,
          activity_id: catalogActivity.id
        })
        .select()
        .single();

      if (!activity) {
        throw new Error("Provider activity creation failed");
      }

      // Create pricing
      const { data: pricing } = await serviceClient
        .schema("app_catalog")
        .from("pricing")
        .insert({
          name: { en: "Test Pricing" },
          description: { en: "Test pricing description" }
        })
        .select()
        .single();

      if (!pricing) {
        throw new Error("Pricing creation failed");
      }

      // Make sure provider is approved
      await serviceClient.schema("app_provider").from("approved_user").insert({
        user_id: provider.data.id
      });

      // Create a service for the provider
      const { data: service } = await serviceClient
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: provider.data.id,
          activity_id: activity.id,
          name: { en: "Test Service" },
          description: { en: "Test service description" },
          soda_amount: 100,
          pricing_id: pricing.id,
          status: "published"
        })
        .select()
        .single();

      if (!service) {
        throw new Error("Service creation failed");
      }

      createdServiceIds.push(service.id);

      // Customer creates an order
      const { data: order } = await serviceClient
        .schema("app_provider")
        .from("order")
        .insert({
          sender_id: customer.data.id,
          receiver_id: provider.data.id,
          service_id: service.id,
          soda_amount: 100,
          order_status: "pending"
        })
        .select()
        .single();

      if (!order) {
        throw new Error("Order creation failed");
      }

      createdOrderIds.push(order.id);

      // Check if notification was created for provider
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "provider.order.new");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "provider.order.new",
        (data) => data.order_id === order.id
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.order_id).toBe(order.id);
      expect(validatedNotification!.data.service_name).toBe("Test Service");
      expect(validatedNotification!.data.amount).toBe(100);

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("order status change notification is sent to customer", async () => {
      if (!customer.data || !provider.data)
        throw new Error("Customer or provider not defined");

      // Create necessary dependencies
      // Create catalog activity
      const { data: catalogActivity } = await serviceClient
        .schema("app_catalog")
        .from("activity")
        .insert({
          name: { en: "Status Change Activity" },
          description: { en: "Test activity for status change" }
        })
        .select()
        .single();

      if (!catalogActivity) {
        throw new Error("Catalog activity creation failed");
      }

      // Create provider activity
      const { data: activity } = await serviceClient
        .schema("app_provider")
        .from("activity")
        .insert({
          user_id: provider.data.id,
          activity_id: catalogActivity.id
        })
        .select()
        .single();

      if (!activity) {
        throw new Error("Provider activity creation failed");
      }

      // Create pricing
      const { data: pricing } = await serviceClient
        .schema("app_catalog")
        .from("pricing")
        .insert({
          name: { en: "Status Change Pricing" },
          description: { en: "Test pricing for status change" }
        })
        .select()
        .single();

      if (!pricing) {
        throw new Error("Pricing creation failed");
      }

      // Create a service and order first
      const { data: service } = await serviceClient
        .schema("app_provider")
        .from("service")
        .insert({
          user_id: provider.data.id,
          activity_id: activity.id,
          name: { en: "Status Change Service" },
          description: { en: "Test service for status change" },
          soda_amount: 150,
          pricing_id: pricing.id,
          status: "published"
        })
        .select()
        .single();

      if (!service) {
        throw new Error("Service creation failed");
      }

      createdServiceIds.push(service.id);

      const { data: order } = await serviceClient
        .schema("app_provider")
        .from("order")
        .insert({
          sender_id: customer.data.id,
          receiver_id: provider.data.id,
          service_id: service.id,
          soda_amount: 150,
          order_status: "pending"
        })
        .select()
        .single();

      if (!order) {
        throw new Error("Order creation failed");
      }

      createdOrderIds.push(order.id);

      // Provider accepts the order
      await serviceClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "accepted" })
        .eq("id", order.id);

      // Check if notification was created for customer
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "provider.order.status_change");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "provider.order.status_change",
        (data) =>
          data.old_status === "pending" && data.new_status === "accepted"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.old_status).toBe("pending");
      expect(validatedNotification!.data.new_status).toBe("accepted");
      expect(validatedNotification!.data.order_id).toBe(order.id);

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }

    // Cleanup created orders
    if (createdOrderIds.length > 0) {
      await serviceClient
        .schema("app_provider")
        .from("order")
        .delete()
        .in("id", createdOrderIds);
    }

    // Cleanup created services
    if (createdServiceIds.length > 0) {
      await serviceClient
        .schema("app_provider")
        .from("service")
        .delete()
        .in("id", createdServiceIds);
    }

    // Cleanup created applications
    if (createdApplicationIds.length > 0) {
      await serviceClient
        .schema("app_provider")
        .from("application")
        .delete()
        .in("user_id", createdApplicationIds);
    }
  });
});
