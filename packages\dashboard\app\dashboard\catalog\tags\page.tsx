"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { catalogClient } from "@/lib/supabase";
import { Tags, Plus, Search } from "lucide-react";

export default function TagsPage() {
  const [search, setSearch] = useState("");

  const { data: tags, isLoading } = useQuery({
    queryKey: ["tags", search],
    queryFn: async () => {
      let query = catalogClient
        .from("tag")
        .select("*")
        .order("created_at", { ascending: false });

      if (search) {
        query = query.or(
          `name->en.ilike.%${search}%,name->ja.ilike.%${search}%,name->ko.ilike.%${search}%,name->tr.ilike.%${search}%`
        );
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading tags...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tags</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage activity tags for better discovery and organization
          </p>
        </div>
        <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <Plus className="h-4 w-4 mr-2" />
          Add Tag
        </button>
      </div>

      {/* Search */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search tags..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </div>

      {/* Tags List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          {tags && tags.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => (
                <span
                  key={tag.id}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer"
                  style={{ backgroundColor: tag.color + '20', color: tag.color }}
                >
                  <Tags className="h-3 w-3 mr-1" />
                  {tag.name.en || tag.name.ja || tag.name.ko || tag.name.tr}
                </span>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Tags className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No tags</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new tag.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
