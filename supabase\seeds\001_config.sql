-- section app_core
-- anchor config
INSERT INTO
  app_core.config (id, max_requests_per_minute)
VALUES
  (TRUE, 100)
ON CONFLICT (id) DO NOTHING
;

-- !section
-- section app_transaction
-- anchor currency
INSERT INTO
  app_transaction.currency (
    code,
    units_per_soda,
    exchange_rate
  )
VALUES
  ('EUR', '0.23', '0.02261000'),
  ('GBP', '0.19', '0.01898000'),
  ('JPY', '36.61', '3.66100000'),
  ('TRY', '10.00', '1.00000000'),
  ('USD', '0.26', '0.02566000')
ON CONFLICT (code) DO NOTHING
;

-- anchor config
INSERT INTO
  app_transaction.config (
    base_currency,
    minimum_soda_withdrawal_amount,
    commission_percent,
    in_progress_timeout_hours,
    dispute_window_hours
  )
VALUES
  ('TRY', 200, 40, 3, 12)
ON CONFLICT (id) DO NOTHING
;

-- !section
-- section app_provider
-- anchor config
INSERT INTO
  app_provider.config (
    id,
    cap_reward_to_customer_for_review_submission,
    cap_reward_to_provider_for_review_approval,
    cap_reward_to_customer_for_completed_order,
    cap_reward_to_provider_for_completed_order,
    cap_cost_for_question_submission,
    cap_reward_for_question_answer
  )
VALUES
  (TRUE, 10, 5, 15, 25, 100, 100)
ON CONFLICT (id) DO UPDATE
SET
  cap_reward_to_customer_for_review_submission = EXCLUDED.cap_reward_to_customer_for_review_submission,
  cap_reward_to_provider_for_review_approval = EXCLUDED.cap_reward_to_provider_for_review_approval,
  cap_reward_to_customer_for_completed_order = EXCLUDED.cap_reward_to_customer_for_completed_order,
  cap_reward_to_provider_for_completed_order = EXCLUDED.cap_reward_to_provider_for_completed_order,
  cap_cost_for_question_submission = EXCLUDED.cap_cost_for_question_submission,
  cap_reward_for_question_answer = EXCLUDED.cap_reward_for_question_answer
;

-- !section