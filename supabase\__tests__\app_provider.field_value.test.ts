import { describe, expect, test } from "vitest";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { mockField } from "./mocks/app_catalog.field";
import { mockFieldValue } from "./mocks/app_provider.field_value";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { mockProviderActivity } from "./mocks/app_provider.activity";
import { randomUUID } from "node:crypto";
import { createRoleTestMatrix } from "./utils/matrix";

const admin = mockAdmin();
const customer = mockCustomer();
const provider = mockProvider();
const catalogActivity = mockCatalogActivity({ admin });
const selectedActivity = mockProviderActivity({
  provider,
  catalogActivity: catalogActivity
});

describe("Field Value Constraints", () => {
  const optionId = randomUUID();

  const selectField = mockField({
    admin,
    field: { type: "select" },
    options: [{ id: optionId, name: { en: "Option 1" } }]
  });
  const firstOptionValue = mockFieldValue({
    provider,
    selectedActivity,
    field: selectField,
    fieldValue: { field_option_id: optionId }
  });
  const secondOptionValue = mockFieldValue({
    provider,
    selectedActivity,
    field: selectField,
    fieldValue: { field_option_id: optionId }
  });
  const checkboxFieldValue = mockFieldValue({
    provider,
    selectedActivity,
    field: selectField,
    fieldValue: { checkbox: true }
  });
  const textFieldValue = mockFieldValue({
    provider,
    selectedActivity,
    field: selectField,
    fieldValue: { text: "Test Value" }
  });

  test("can only insert one option for select field", async () => {
    expect(firstOptionValue.id).toBeDefined();
    expect(secondOptionValue.id).toBeUndefined();
    expect(textFieldValue.id).toBeUndefined();
    expect(checkboxFieldValue.id).toBeUndefined();
  });
});

const rlsTestMatrix = createRoleTestMatrix<
  "admin" | "customer" | "provider",
  "field_value"
>({
  admin: {
    user: admin,
    permissions: {
      field_value: { view: true, insert: true, update: true, delete: true }
    }
  },
  customer: {
    user: customer,
    permissions: {
      field_value: { view: true, insert: false, update: false, delete: false }
    }
  },
  provider: {
    user: provider,
    permissions: {
      field_value: { view: true, insert: true, update: true, delete: true }
    }
  }
});

const sharedField = mockField({ admin, field: { type: "text" } });
const sharedFieldValue = mockFieldValue({
  provider,
  selectedActivity,
  field: sharedField,
  fieldValue: { text: "Test Value" }
});
const sharedField2 = mockField({ admin, field: { type: "text" } });

describe.each(rlsTestMatrix)("$role", ({ user, permissions }) => {
  const userFieldValue = mockFieldValue({
    provider: user,
    selectedActivity,
    field: sharedField2,
    fieldValue: { text: "Test Value" }
  });

  test(`can view: ${permissions.field_value.view}`, async () => {
    expect(sharedFieldValue.id).toBeDefined();
  });

  test(`can insert: ${permissions.field_value.insert}`, async () => {
    if (permissions.field_value.insert) {
      expect(userFieldValue.id).toBeDefined();
    } else {
      expect(userFieldValue.id).toBeUndefined();
    }
  });

  test(`can update: ${permissions.field_value.update}`, async () => {
    if (!user.client) throw new Error("User client is undefined");
    if (!userFieldValue.id) return;

    await user.client
      .schema("app_provider")
      .from("field_value")
      .update({ text: "Updated Value" })
      .eq("id", userFieldValue.id);

    const { data: checkFieldValue } = await user.client
      .schema("app_provider")
      .from("field_value")
      .select()
      .eq("id", userFieldValue.id)
      .single();

    if (permissions.field_value.update) {
      expect(checkFieldValue?.text).toBe("Updated Value");
    } else {
      expect(checkFieldValue?.text).toBe("Test Value");
    }
  });

  test(`can delete: ${permissions.field_value.delete}`, async () => {
    if (!user.client) throw new Error("User client is undefined");
    if (!userFieldValue.id) return;

    await user.client
      .schema("app_provider")
      .from("field_value")
      .delete()
      .eq("id", userFieldValue.id);

    const { data: checkFieldValue } = await user.client
      .schema("app_provider")
      .from("field_value")
      .select()
      .eq("id", userFieldValue.id)
      .single();

    if (permissions.field_value.delete) {
      expect(checkFieldValue).toBeNull();
    } else {
      expect(checkFieldValue?.id).toBe(userFieldValue.id);
    }
  });
});
