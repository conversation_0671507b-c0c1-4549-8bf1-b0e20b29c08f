# E-Senpai Admin Panel - Complete Sprint Roadmap

## Sprint Overview

**Duration:** 2-3 weeks (80-120 hours)  
**Team Size:** 1-2 experienced developers  
**Target:** Production-ready admin panel for dashboard.esenpai.com

## Phase 1: Foundation & Authentication (Days 1-2, 16-20 hours)

### Task 1.1: Project Setup & Infrastructure (4 hours)

**Priority:** Critical Path  
**Dependencies:** None  
**Complexity:** 3 story points

```json path=packages/dashboard/package.json mode=EDIT
{
  "name": "@esenpai/dashboard",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev --port 3001",
    "build": "next build",
    "start": "next start --port 3001",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "next": "^15.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "@tanstack/react-query": "^5.0.0",
    "@tanstack/react-table": "^8.0.0",
    "shared": "workspace:*",
    "zod": "^3.22.0",
    "date-fns": "^3.0.0",
    "recharts": "^2.8.0"
  }
}
```

**Deliverables:**

- Next.js 15 project structure
- Workspace configuration
- Environment setup
- Build pipeline configuration

### Task 1.2: Authentication & Access Control (8 hours)

**Priority:** Critical Path  
**Dependencies:** Task 1.1  
**Complexity:** 8 story points

```ts path=packages/dashboard/lib/auth.ts mode=EDIT
import { createClient } from "shared/lib/supabase/client";
import { createClient as createServiceClient } from "shared/lib/supabase/service";

export async function checkAdminAccess(userId: string, capability: string) {
  const serviceClient = createServiceClient();

  const { data, error } = await serviceClient
    .schema("app_access")
    .rpc("has_capability", {
      p_user_id: userId,
      p_capability: capability
    });

  if (error) throw error;
  return data;
}

export async function requireAdminRole(requiredCapabilities: string[]) {
  const supabase = createClient();
  const {
    data: { user }
  } = await supabase.auth.getUser();

  if (!user) throw new Error("Unauthorized");

  for (const capability of requiredCapabilities) {
    const hasAccess = await checkAdminAccess(user.id, capability);
    if (!hasAccess) throw new Error(`Missing capability: ${capability}`);
  }

  return user;
}
```

```tsx path=packages/dashboard/components/ProtectedRoute.tsx mode=EDIT
"use client";

import { useEffect, useState } from "react";
import { useRouter } from "shared/lib";
import { createClient } from "shared/lib/supabase/client";
import { checkAdminAccess } from "@/lib/auth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredCapabilities: string[];
}

export function ProtectedRoute({
  children,
  requiredCapabilities
}: ProtectedRouteProps) {
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function checkAuth() {
      try {
        const supabase = createClient();
        const {
          data: { user }
        } = await supabase.auth.getUser();

        if (!user) {
          router.push("/login");
          return;
        }

        const hasAllCapabilities = await Promise.all(
          requiredCapabilities.map((cap) => checkAdminAccess(user.id, cap))
        );

        if (hasAllCapabilities.every(Boolean)) {
          setIsAuthorized(true);
        } else {
          router.push("/unauthorized");
        }
      } catch (error) {
        router.push("/login");
      }
    }

    checkAuth();
  }, [requiredCapabilities, router]);

  if (isAuthorized === null) {
    return <div>Loading...</div>;
  }

  return isAuthorized ? <>{children}</> : null;
}
```

**Deliverables:**

- Authentication middleware
- Role-based access control
- Protected route components
- Login/logout functionality

### Task 1.3: Core Layout & Navigation (4 hours)

**Priority:** Critical Path  
**Dependencies:** Task 1.2  
**Complexity:** 5 story points

**Deliverables:**

- Responsive admin layout
- Workflow-based navigation
- Mobile-optimized sidebar
- Breadcrumb system

### Task 1.4: Real-time Infrastructure (4 hours)

**Priority:** High  
**Dependencies:** Task 1.1  
**Complexity:** 6 story points

```ts path=packages/dashboard/hooks/useRealtimeSubscription.ts mode=EDIT
import { useEffect, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { createClient } from "shared/lib/supabase/client";
import type { RealtimeChannel } from "@supabase/supabase-js";

interface UseRealtimeSubscriptionProps {
  schema: string;
  table: string;
  queryKey: string[];
  filter?: string;
}

export function useRealtimeSubscription({
  schema,
  table,
  queryKey,
  filter
}: UseRealtimeSubscriptionProps) {
  const queryClient = useQueryClient();
  const channelRef = useRef<RealtimeChannel | null>(null);

  useEffect(() => {
    const supabase = createClient();

    const channel = supabase
      .channel(`${schema}_${table}_changes`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema,
          table,
          filter
        },
        () => {
          queryClient.invalidateQueries({ queryKey });
        }
      )
      .subscribe();

    channelRef.current = channel;

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [schema, table, queryKey, filter, queryClient]);
}
```

**Deliverables:**

- Real-time subscription hooks
- Query invalidation system
- Connection management
- Error handling

## Phase 2: Catalog Management Completion (Days 3-4, 16-20 hours)

### Task 2.1: Activities Management (4 hours)

**Priority:** High  
**Dependencies:** Phase 1 complete  
**Complexity:** 5 story points  
**Can run in parallel with:** Task 2.2, 2.3

```tsx path=packages/dashboard/app/catalog/activities/components/ActivitiesList.tsx mode=EDIT
"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { catalogClient } from "@/lib/supabase";
import { Activity, Category } from "@/lib/catalog-types";
import { useRealtimeSubscription } from "@/hooks/useRealtimeSubscription";

export function ActivitiesList() {
  const [search, setSearch] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");

  const { data: activities, isLoading } = useQuery({
    queryKey: ["activities", search, categoryFilter],
    queryFn: async () => {
      let query = catalogClient
        .from("activity")
        .select(
          `
          *,
          category:category_id(id, name)
        `
        )
        .order("created_at", { ascending: false });

      if (search) {
        query = query.or(
          `name->en.ilike.%${search}%,name->jp.ilike.%${search}%`
        );
      }

      if (categoryFilter) {
        query = query.eq("category_id", categoryFilter);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as (Activity & { category: Category })[];
    }
  });

  useRealtimeSubscription({
    schema: "app_catalog",
    table: "activity",
    queryKey: ["activities", search, categoryFilter]
  });

  // Component implementation...
}
```

**Deliverables:**

- Activities CRUD interface
- Category association management
- Service count tracking
- Color and slug management

### Task 2.2: Tags & Relationships (3 hours)

**Priority:** Medium  
**Dependencies:** Phase 1 complete  
**Complexity:** 4 story points

**Deliverables:**

- Tags CRUD interface
- Activity-tag relationship management
- Bulk tag operations
- Tag usage analytics

### Task 2.3: Fields & Field Options (5 hours)

**Priority:** High  
**Dependencies:** Phase 1 complete  
**Complexity:** 7 story points

```tsx path=packages/dashboard/app/catalog/fields/components/FieldForm.tsx mode=EDIT
"use client";

import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { catalogClient } from "@/lib/supabase";
import { Field, FieldOption } from "@/lib/catalog-types";

interface FieldFormProps {
  field?: Field | null;
  onClose: () => void;
}

export function FieldForm({ field, onClose }: FieldFormProps) {
  const [formData, setFormData] = useState({
    name_en: field?.name?.en || "",
    name_jp: field?.name?.jp || "",
    description_en: field?.description?.en || "",
    description_jp: field?.description?.jp || "",
    type: field?.type || ("text" as const),
    is_required: field?.is_required || false
  });

  const [fieldOptions, setFieldOptions] = useState<
    Array<{
      id?: string;
      name_en: string;
      name_jp: string;
      value: string;
      sort_order: number;
    }>
  >([]);

  // Handle select/multiselect field options
  const showOptions =
    formData.type === "select" || formData.type === "multiselect";

  const mutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      const payload = {
        name: { en: data.name_en, jp: data.name_jp },
        description:
          data.description_en || data.description_jp
            ? {
                en: data.description_en,
                jp: data.description_jp
              }
            : null,
        type: data.type,
        is_required: data.is_required
      };

      let fieldId: string;

      if (field) {
        const { error } = await catalogClient
          .from("field")
          .update(payload)
          .eq("id", field.id);
        if (error) throw error;
        fieldId = field.id;
      } else {
        const { data: newField, error } = await catalogClient
          .from("field")
          .insert(payload)
          .select("id")
          .single();
        if (error) throw error;
        fieldId = newField.id;
      }

      // Handle field options for select/multiselect fields
      if (showOptions && fieldOptions.length > 0) {
        // Delete existing options if editing
        if (field) {
          await catalogClient
            .from("field_option")
            .delete()
            .eq("field_id", fieldId);
        }

        // Insert new options
        const optionsToInsert = fieldOptions.map((option) => ({
          field_id: fieldId,
          name: { en: option.name_en, jp: option.name_jp },
          value: option.value,
          sort_order: option.sort_order
        }));

        const { error: optionsError } = await catalogClient
          .from("field_option")
          .insert(optionsToInsert);
        if (optionsError) throw optionsError;
      }
    }
    // ... rest of mutation logic
  });

  // Component JSX with field options management...
}
```

**Deliverables:**

- Fields CRUD with type support
- Field options management
- Category/activity field associations
- Validation rules

### Task 2.4: Services, Pricing & Platforms (4 hours)

**Priority:** Medium  
**Dependencies:** Tasks 2.1-2.3  
**Complexity:** 6 story points

**Deliverables:**

- Services catalog management
- Pricing model definitions
- Platform management
- Service approval workflows

## Phase 3: User Management Workflow (Days 5-6, 16-20 hours)

### Task 3.1: User Analytics Dashboard (6 hours)

**Priority:** High  
**Dependencies:** Phase 1 complete  
**Complexity:** 8 story points

```tsx path=packages/dashboard/app/users/components/UserAnalyticsDashboard.tsx mode=EDIT
"use client";

import { useQuery } from "@tanstack/react-query";
import { createClient } from "shared/lib/supabase/service";
import { Card, CardContent, CardHeader, CardTitle } from "shared/ui";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from "recharts";

export function UserAnalyticsDashboard() {
  const { data: userMetrics } = useQuery({
    queryKey: ["user-analytics"],
    queryFn: async () => {
      const supabase = createClient();

      // User growth over time
      const { data: userGrowth } = await supabase
        .schema("app_account")
        .from("profile")
        .select("created_at")
        .order("created_at");

      // Financial summaries per user
      const { data: userFinancials } = await supabase
        .schema("app_transaction")
        .rpc("get_user_financial_summary");

      // Engagement metrics
      const { data: engagementData } = await supabase
        .schema("app_provider")
        .from("order").select(`
          customer_id,
          created_at,
          status,
          total_soda
        `);

      return {
        userGrowth: processUserGrowthData(userGrowth),
        userFinancials,
        engagement: processEngagementData(engagementData)
      };
    },
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>User Growth</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={userMetrics?.userGrowth}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="users" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Additional analytics cards... */}
    </div>
  );
}

function processUserGrowthData(data: any[]) {
  // Process raw data into chart format
  // Group by date, count users
  return data; // Simplified
}

function processEngagementData(data: any[]) {
  // Process engagement metrics
  return data; // Simplified
}
```

**Deliverables:**

- User growth analytics
- Engagement metrics dashboard
- Financial summaries per user
- Behavior tracking visualizations

### Task 3.2: User Management Interface (5 hours)

**Priority:** High  
**Dependencies:** Task 3.1  
**Complexity:** 6 story points

**Deliverables:**

- User list with advanced filtering
- User profile management
- Role assignment interface
- KYC status management

### Task 3.3: Moderation Tools (5 hours)

**Priority:** High  
**Dependencies:** Task 3.2  
**Complexity:** 7 story points

```tsx path=packages/dashboard/app/users/components/ModerationQueue.tsx mode=EDIT
"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createClient } from "shared/lib/supabase/service";
import { Button, Card, Badge, Textarea } from "shared/ui";
import { useRealtimeSubscription } from "@/hooks/useRealtimeSubscription";

interface ModerationItem {
  id: string;
  type: "user_report" | "content_review" | "ban_appeal";
  user_id: string;
  reported_user_id?: string;
  content_id?: string;
  reason: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  user: {
    email: string;
    profile: { display_name: string };
  };
}

export function ModerationQueue() {
  const [selectedItem, setSelectedItem] = useState<ModerationItem | null>(null);
  const [moderationNote, setModerationNote] = useState("");
  const queryClient = useQueryClient();

  const { data: moderationItems, isLoading } = useQuery({
    queryKey: ["moderation-queue"],
    queryFn: async () => {
      const supabase = createClient();

      const { data, error } = await supabase
        .schema("app_support")
        .from("moderation_queue")
        .select(
          `
          *,
          user:user_id(email, profile:app_account.profile(display_name)),
          reported_user:reported_user_id(email, profile:app_account.profile(display_name))
        `
        )
        .eq("status", "pending")
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data as ModerationItem[];
    }
  });

  useRealtimeSubscription({
    schema: "app_support",
    table: "moderation_queue",
    queryKey: ["moderation-queue"],
    filter: "status=eq.pending"
  });

  const moderationMutation = useMutation({
    mutationFn: async ({
      itemId,
      action,
      note
    }: {
      itemId: string;
      action: "approve" | "reject";
      note: string;
    }) => {
      const supabase = createClient();

      const { error } = await supabase
        .schema("app_support")
        .rpc("process_moderation_item", {
          p_item_id: itemId,
          p_action: action,
          p_moderator_note: note
        });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["moderation-queue"] });
      setSelectedItem(null);
      setModerationNote("");
    }
  });

  const handleModeration = (action: "approve" | "reject") => {
    if (!selectedItem) return;

    moderationMutation.mutate({
      itemId: selectedItem.id,
      action,
      note: moderationNote
    });
  };

  if (isLoading) return <div>Loading moderation queue...</div>;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Pending Items</h3>
        {moderationItems?.map((item) => (
          <Card
            key={item.id}
            className={`cursor-pointer transition-colors ${
              selectedItem?.id === item.id ? "ring-2 ring-primary" : ""
            }`}
            onClick={() => setSelectedItem(item)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline">{item.type.replace("_", " ")}</Badge>
                <span className="text-sm text-muted-foreground">
                  {new Date(item.created_at).toLocaleDateString()}
                </span>
              </div>
              <p className="text-sm">{item.reason}</p>
              <p className="text-xs text-muted-foreground mt-1">
                User: {item.user.profile?.display_name || item.user.email}
              </p>
            </div>
          </Card>
        ))}
      </div>

      {selectedItem && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Review Item</h3>
          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">Details</h4>
                <p className="text-sm text-muted-foreground">
                  {selectedItem.reason}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Moderation Note
                </label>
                <Textarea
                  value={moderationNote}
                  onChange={(e) => setModerationNote(e.target.value)}
                  placeholder="Add your moderation decision reasoning..."
                  rows={3}
                />
              </div>

              <div className="flex space-x-2">
                <Button
                  onClick={() => handleModeration("approve")}
                  disabled={moderationMutation.isPending}
                  variant="default"
                >
                  Approve
                </Button>
                <Button
                  onClick={() => handleModeration("reject")}
                  disabled={moderationMutation.isPending}
                  variant="destructive"
                >
                  Reject
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
```

**Deliverables:**

- Ban management system
- Content review queue
- User report handling
- Automated moderation rules

## Phase 4: Provider Management Workflow (Days 7-8, 16-20 hours)

### Task 4.1: Application Review System (6 hours)

**Priority:** High  
**Dependencies:** Phase 1 complete  
**Complexity:** 8 story points

**Deliverables:**

- Application review interface
- Approve/reject workflows
- Application status tracking
- Reviewer assignment system

### Task 4.2: Service Approval & Management (5 hours)

**Priority:** High  
**Dependencies:** Task 4.1, Phase 2 complete  
**Complexity:** 6 story points

**Deliverables:**

- Service approval workflows
- Service quality monitoring
- Content moderation for services
- Bulk approval operations

### Task 4.3: Performance Monitoring (5 hours)

**Priority:** Medium  
**Dependencies:** Task 4.1  
**Complexity:** 7 story points

**Deliverables:**

- Provider performance dashboard
- Earnings analytics
- Response time monitoring
- Provider ranking system

## Phase 5: Financial Operations Workflow (Days 9-10, 16-20 hours)

### Task 5.1: Transaction Monitoring (6 hours)

**Priority:** Critical  
**Dependencies:** Phase 1 complete  
**Complexity:** 8 story points

```tsx path=packages/dashboard/app/finance/components/TransactionMonitor.tsx mode=EDIT
"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { createClient } from "shared/lib/supabase/service";
import { Card, Input, Select, Badge } from "shared/ui";
import { useRealtimeSubscription } from "@/hooks/useRealtimeSubscription";

export function TransactionMonitor() {
  const [filters, setFilters] = useState({
    type: "",
    status: "",
    dateRange: "24h",
    minAmount: "",
    maxAmount: ""
  });

  const { data: transactions, isLoading } = useQuery({
    queryKey: ["transactions", filters],
    queryFn: async () => {
      const supabase = createClient();

      let query = supabase
        .schema("app_transaction")
        .from("transaction")
        .select(
          `
          *,
          from_user:from_user_id(email, profile:app_account.profile(display_name)),
          to_user:to_user_id(email, profile:app_account.profile(display_name))
        `
        )
        .order("created_at", { ascending: false })
        .limit(100);

      // Apply filters
      if (filters.type) {
        query = query.eq("type", filters.type);
      }

      if (filters.status) {
        query = query.eq("status", filters.status);
      }

      if (filters.dateRange === "24h") {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        query = query.gte("created_at", yesterday.toISOString());
      }

      if (filters.minAmount) {
        query = query.gte("amount", parseInt(filters.minAmount));
      }

      if (filters.maxAmount) {
        query = query.lte("amount", parseInt(filters.maxAmount));
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    refetchInterval: 5000 // Real-time updates every 5 seconds
  });

  useRealtimeSubscription({
    schema: "app_transaction",
    table: "transaction",
    queryKey: ["transactions", filters]
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "success";
      case "pending":
        return "warning";
      case "failed":
        return "destructive";
      case "disputed":
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Select
            value={filters.type}
            onValueChange={(value) =>
              setFilters((prev) => ({ ...prev, type: value }))
            }
          >
            <option value="">All Types</option>
            <option value="deposit">Deposit</option>
            <option value="withdrawal">Withdrawal</option>
            <option value="order_payment">Order Payment</option>
            <option value="refund">Refund</option>
          </Select>

          <Select
            value={filters.status}
            onValueChange={(value) =>
              setFilters((prev) => ({ ...prev, status: value }))
            }
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="disputed">Disputed</option>
          </Select>

          <Input
            placeholder="Min Amount"
            type="number"
            value={filters.minAmount}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, minAmount: e.target.value }))
            }
          />

          <Input
            placeholder="Max Amount"
            type="number"
            value={filters.maxAmount}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, maxAmount: e.target.value }))
            }
          />

          <Select
            value={filters.dateRange}
            onValueChange={(value) =>
              setFilters((prev) => ({ ...prev, dateRange: value }))
            }
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="all">All Time</option>
          </Select>
        </div>
      </Card>

      {/* Transactions List */}
      <div className="space-y-2">
        {transactions?.map((transaction) => (
          <Card key={transaction.id} className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Badge variant={getStatusColor(transaction.status)}>
                  {transaction.status}
                </Badge>
                <div>
                  <p className="font-medium">{transaction.type}</p>
                  <p className="text-sm text-muted-foreground">
                    {transaction.from_user?.profile?.display_name ||
                      transaction.from_user?.email}
                    →
                    {transaction.to_user?.profile?.display_name ||
                      transaction.to_user?.email}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">
                  {transaction.amount} {transaction.currency}
                </p>
                <p className="text-sm text-muted-foreground">
                  {new Date(transaction.created_at).toLocaleString()}
                </p>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
```

**Deliverables:**

- Real-time transaction monitoring
- Advanced filtering and search
- Transaction details view
- Suspicious activity alerts

### Task 5.2: Currency Management (4 hours)

**Priority:** High  
**Dependencies:** Task 5.1  
**Complexity:** 6 story points

**Deliverables:**

- Soda/caps balance monitoring
- Exchange rate management
- Currency operation controls
- Financial reporting

### Task 5.3: Dispute Resolution (6 hours)

**Priority:** High  
**Dependencies:** Task 5.1  
**Complexity:** 7 story points

**Deliverables:**

- Dispute management interface
- Escalation workflows
- Evidence review system
- Resolution tracking

## Phase 6: Support Operations Workflow (Days 11-12, 16-20 hours)

### Task 6.1: Ticket Management System (8 hours)

**Priority:** High  
**Dependencies:** Phase 1 complete  
**Complexity:** 9 story points

**Deliverables:**

- Support ticket interface
- Agent assignment system
- Priority management
- Response templates

### Task 6.2: Community Management (4 hours)

**Priority:** Medium  
**Dependencies:** Task 6.1  
**Complexity:** 5 story points

**Deliverables:**

- Content moderation tools
- Community guidelines enforcement
- User report processing
- Automated moderation rules

### Task 6.3: Escalation & Workflows (4 hours)

**Priority:** Medium  
**Dependencies:** Task 6.1  
**Complexity:** 6 story points

**Deliverables:**

- Escalation rule engine
- Workflow automation
- SLA monitoring
- Performance metrics

## Phase 7: Analytics & Dashboard (Days 13-14, 16-20 hours)

### Task 7.1: Real-time Dashboard (8 hours)

**Priority:** High  
**Dependencies:** Phases 3-6 complete  
**Complexity:** 9 story points

```tsx path=packages/dashboard/app/components/RealTimeDashboard.tsx mode=EDIT
"use client";

import { useQuery } from "@tanstack/react-query";
import { createClient } from "shared/lib/supabase/service";
import { Card, CardContent, CardHeader, CardTitle } from "shared/ui";
import { Activity, DollarSign, Users, MessageSquare } from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from "recharts";

export function RealTimeDashboard() {
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ["dashboard-overview"],
    queryFn: async () => {
      const supabase = createClient();

      // Parallel queries for dashboard metrics
      const [
        { data: userStats },
        { data: transactionStats },
        { data: supportStats },
        { data: providerStats },
        { data: recentActivity }
      ] = await Promise.all([
        supabase.schema("app_account").rpc("get_user_stats"),
        supabase.schema("app_transaction").rpc("get_transaction_stats"),
        supabase.schema("app_support").rpc("get_support_stats"),
        supabase.schema("app_provider").rpc("get_provider_stats"),
        supabase.schema("app_dashboard").rpc("get_recent_activity")
      ]);

      return {
        userStats,
        transactionStats,
        supportStats,
        providerStats,
        recentActivity
      };
    },
    refetchInterval: 10000 // Update every 10 seconds
  });

  if (isLoading) {
    return <div>Loading dashboard...</div>;
  }

  const metrics = [
    {
      title: "Active Users",
      value: dashboardData?.userStats?.active_users || 0,
      change: "+12%",
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Daily Revenue",
      value: `${dashboardData?.transactionStats?.daily_revenue || 0} soda`,
      change: "+8%",
      icon: DollarSign,
      color: "text-green-600"
    },
    {
      title: "Open Tickets",
      value: dashboardData?.supportStats?.open_tickets || 0,
      change: "-3%",
      icon: MessageSquare,
      color: "text-orange-600"
    },
    {
      title: "Active Providers",
      value: dashboardData?.providerStats?.active_providers || 0,
      change: "+5%",
      icon: Activity,
      color: "text-purple-600"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${metric.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <p className="text-xs text-muted-foreground">
                  {metric.change} from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Activity Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Activity (Last 7 Days)</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={dashboardData?.recentActivity}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="users"
                stroke="#8884d8"
                strokeWidth={2}
                name="Active Users"
              />
              <Line
                type="monotone"
                dataKey="orders"
                stroke="#82ca9d"
                strokeWidth={2}
                name="Orders"
              />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#ffc658"
                strokeWidth={2}
                name="Revenue"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Recent Activity Feed */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dashboardData?.recentActivity
              ?.slice(0, 10)
              .map((activity: any, index: number) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm">{activity.description}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

**Deliverables:**

- Live activity dashboard
- Key performance indicators
- Real-time charts and graphs
- Activity feed

### Task 7.2: Business Intelligence (4 hours)

**Priority:** Medium  
**Dependencies:** Task 7.1  
**Complexity:** 6 story points

**Deliverables:**

- Revenue analytics
- User growth tracking
- Provider performance analytics
- Platform health monitoring

### Task 7.3: Reporting System (4 hours)

**Priority:** Medium  
**Dependencies:** Task 7.1  
**Complexity:** 5 story points

**Deliverables:**

- Automated report generation
- Export functionality
- Scheduled reports
- Custom report builder

## Phase 8: Mobile Optimization & Deployment (Days 15-16, 16-20 hours)

### Task 8.1: Mobile Responsiveness (6 hours)

**Priority:** High  
**Dependencies:** All previous phases  
**Complexity:** 7 story points

**Deliverables:**

- Mobile-optimized layouts
- Touch-friendly interfaces
- Responsive navigation
- Mobile workflow optimization

### Task 8.2: Performance Optimization (4 hours)

**Priority:** High  
**Dependencies:** Task 8.1  
**Complexity:** 6 story points

**Deliverables:**

- Code splitting
- Lazy loading
- Query optimization
- Bundle size optimization

### Task 8.3: Deployment & Configuration (6 hours)

**Priority:** Critical  
**Dependencies:** Task 8.2  
**Complexity:** 8 story points

```js path=packages/dashboard/next.config.js mode=EDIT
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  experimental: {
    optimizePackageImports: ["shared", "lucide-react", "recharts"]
  },
  env: {
    NEXT_PUBLIC_APP_URL:
      process.env.NEXT_PUBLIC_APP_URL || "https://dashboard.esenpai.com"
  },
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "/api/:path*"
      }
    ];
  },
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY"
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff"
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin"
          }
        ]
      }
    ];
  }
};

module.exports = nextConfig;
```

**Deliverables:**

- Production deployment configuration
- Environment setup
- Security headers
- Monitoring setup

## Testing Strategy (Parallel to Development)

### Unit Testing (8 hours total)

- Component testing with React Testing Library
- Hook testing for custom hooks
- Utility function testing
- Form validation testing

### Integration Testing (6 hours total)

- API integration testing
- Database operation testing
- Authentication flow testing
- Real-time subscription testing

### E2E Testing (4 hours total)

- Critical workflow testing
- Mobile responsiveness testing
- Performance testing
- Security testing

## Risk Mitigation & Dependencies

### Critical Path Dependencies:

1. **Authentication System** → All other features
2. **Database Schema Access** → All CRUD operations
3. **Real-time Infrastructure** → Live dashboard features
4. **Mobile Optimization** → Production readiness

### Parallel Development Opportunities:

- Catalog management (Tasks 2.1-2.4) can be developed simultaneously
- User/Provider/Financial workflows can be built in parallel after Phase 1
- Analytics can be developed alongside core workflows

### Risk Mitigation:

- **Database Access Issues**: Implement comprehensive error handling and fallback queries
- **Real-time Performance**: Implement connection pooling and subscription cleanup
- **Mobile Complexity**: Start with desktop-first, then optimize for mobile
- **Integration Complexity**: Build modular components with clear interfaces

## Success Metrics

### Technical Metrics:

- Page load times < 2 seconds
- Real-time updates < 500ms latency
- Mobile responsiveness score > 95%
- Test coverage > 80%

### Functional Metrics:

- All CRUD operations working
- Real-time features functional
- Mobile workflows complete
- Role-based access enforced

### Business Metrics:

- Complete operational control over E-Senpai platform
- Reduced manual administrative overhead
- Improved response times for support operations
- Enhanced financial oversight capabilities

This roadmap provides a comprehensive, executable plan for building the complete E-Senpai admin panel in a single sprint with clear priorities, dependencies, and deliverables.
