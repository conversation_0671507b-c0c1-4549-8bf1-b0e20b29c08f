"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { catalogClient } from "@/lib/supabase";
import { useRealtimeSubscription } from "@/hooks/useRealtimeSubscription";
import { Activity, Category } from "@/lib/types";
import { Plus, Search, Edit, Trash2 } from "lucide-react";

export default function ActivitiesPage() {
  const [search, setSearch] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const queryClient = useQueryClient();

  const { data: activities, isLoading } = useQuery({
    queryKey: ["activities", search, categoryFilter],
    queryFn: async () => {
      let query = catalogClient
        .from("activity")
        .select(`
          *,
          category:category_id(id, name, slug)
        `)
        .order("created_at", { ascending: false });

      if (search) {
        query = query.or(
          `name->en.ilike.%${search}%,name->ja.ilike.%${search}%,name->ko.ilike.%${search}%,name->tr.ilike.%${search}%`
        );
      }

      if (categoryFilter) {
        query = query.eq("category_id", categoryFilter);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as (Activity & { category: Category })[];
    }
  });

  const { data: categories } = useQuery({
    queryKey: ["categories"],
    queryFn: async () => {
      const { data, error } = await catalogClient
        .from("category")
        .select("*")
        .order("name->en");
      if (error) throw error;
      return data as Category[];
    }
  });

  useRealtimeSubscription({
    schema: "app_catalog",
    table: "activity",
    queryKey: ["activities", search, categoryFilter]
  });

  const deleteMutation = useMutation({
    mutationFn: async (activityId: string) => {
      const { error } = await catalogClient
        .from("activity")
        .delete()
        .eq("id", activityId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activities"] });
    }
  });

  const handleDelete = async (activityId: string) => {
    if (confirm("Are you sure you want to delete this activity?")) {
      deleteMutation.mutate(activityId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading activities...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Activities</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage platform activities and their categories
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Activity
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search activities..."
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
          >
            <option value="">All Categories</option>
            {categories?.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name.en || category.name.ja || category.name.ko || category.name.tr}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Activities List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-4">
            {activities?.map((activity) => (
              <div
                key={activity.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: activity.color }}
                      />
                      <h3 className="text-lg font-medium text-gray-900">
                        {activity.name.en || activity.name.ja || activity.name.ko || activity.name.tr}
                      </h3>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {activity.category?.name.en || activity.category?.name.ja || activity.category?.name.ko || activity.category?.name.tr}
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-600">
                      Slug: {activity.slug}
                    </p>
                    {activity.description && (
                      <p className="mt-2 text-sm text-gray-700">
                        {activity.description.en || activity.description.ja || activity.description.ko || activity.description.tr}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      className="p-2 text-gray-400 hover:text-gray-600"
                      title="Edit activity"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(activity.id)}
                      className="p-2 text-gray-400 hover:text-red-600"
                      title="Delete activity"
                      disabled={deleteMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {activities?.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No activities found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
