-- This is just a cleaning migration to remove all users and start fresh
-- Drop all app schemas to remove dependencies
DROP SCHEMA IF EXISTS app_chat CASCADE
;

DROP SCHEMA IF EXISTS app_test CASCADE
;

DROP SCHEMA IF EXISTS app_ui CASCADE
;

DROP SCHEMA IF EXISTS app_support CASCADE
;

DROP SCHEMA IF EXISTS app_wiki CASCADE
;

DROP SCHEMA IF EXISTS app_flag CASCADE
;

DROP SCHEMA IF EXISTS app_dashboard CASCADE
;

DROP SCHEMA IF EXISTS app_provider CASCADE
;

DROP SCHEMA IF EXISTS app_transaction CASCADE
;

DROP SCHEMA IF EXISTS app_account CASCADE
;

DROP SCHEMA IF EXISTS app_catalog CASCADE
;

DROP SCHEMA IF EXISTS app_media CASCADE
;

DROP SCHEMA IF EXISTS app_access CASCADE
;

DROP SCHEMA IF EXISTS app_core CASCADE
;

-- Now, clean auth tables
DELETE FROM auth.identities
;

DELETE FROM auth.users
;

DELETE FROM auth.audit_log_entries
;
