import { test, expect, afterAll } from "vitest";
import { mockCustomer, mockProvider } from "./mocks/auth.user";
import { mockService } from "./mocks/app_provider.service";
import { serviceClient } from "./utils/client";
const customer = mockCustomer();
const provider = mockProvider();
const service = mockService(provider);

const logsToClean: string[] = [];

afterAll(async () => {
  await serviceClient
    .schema("app_provider")
    .from("order")
    .delete()
    .in("id", logsToClean);
});

test("sufficient balance can submit", async () => {
  if (!customer.client) throw new Error("Customer data is undefined");
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!service.providerServiceId) throw new Error("Service data is undefined");

  // Set user's balance to a low amount
  await serviceClient.schema("app_transaction").from("wallet").upsert({
    user_id: customer.data.id,
    soda_balance: 1000
  });

  const order = await customer.client
    .schema("app_provider")
    .rpc("submit_order", {
      p_service_id: service.providerServiceId,
      p_unit_count: 1
    });

  expect(order.data?.id).toBeDefined();

  logsToClean.push(String(order.data?.id));
});

test("insufficient balance cannot submit", async () => {
  if (!customer.data) throw new Error("Customer data is undefined");
  if (!customer.client) throw new Error("Customer client is undefined");
  if (!service.providerServiceId) throw new Error("Service data is undefined");

  // Set user's balance to a low amount
  await serviceClient.schema("app_transaction").from("wallet").upsert({
    user_id: customer.data.id,
    soda_balance: 10 // Less than the service cost (50)
  });

  // Attempt to submit the order and expect an error
  const order = await customer.client
    .schema("app_provider")
    .rpc("submit_order", {
      p_service_id: service.providerServiceId,
      p_unit_count: 1
    });

  expect(order.error).toBeDefined();

  if (order.data?.id) logsToClean.push(String(order.data?.id));
});
