import { afterAll, describe, expect, test } from "vitest";
import { mockCustomer, mockProvider } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { findValidatedNotification } from "./utils/validation";

describe("Transaction Notification System", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const createdNotificationIds: string[] = [];
  const createdDepositIds: string[] = [];
  const createdTransferIds: string[] = [];
  const createdEscrowIds: string[] = [];
  const createdWithdrawalRequestIds: string[] = [];

  describe("Deposit Notifications", () => {
    test("deposit success notification is sent", async () => {
      if (!customer.data) throw new Error("Customer not defined");

      // Create a deposit
      const { data: deposit } = await serviceClient
        .schema("app_transaction")
        .from("deposit")
        .insert({
          user_id: customer.data.id,
          amount: 100,
          currency: "TRY",
          soda_credited: 100,
          cap_credited: 0
        })
        .select()
        .single();

      if (!deposit) {
        throw new Error("Deposit creation failed");
      }

      createdDepositIds.push(deposit.id);

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "transaction.deposit.success");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "transaction.deposit.success",
        (data) => data.amount === 100
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.amount).toBe(100);
      expect(validatedNotification!.data.currency).toBe("TRY");
      expect(validatedNotification!.data.deposit_id).toBe(deposit.id);

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });
  });

  describe("Transfer Notifications", () => {
    test("transfer received notification is sent", async () => {
      if (!customer.data || !provider.data)
        throw new Error("Users not defined");

      // Create a transfer from customer to provider
      const { data: transfer } = await serviceClient
        .schema("app_transaction")
        .from("transfer")
        .insert({
          sender_id: customer.data.id,
          receiver_id: provider.data.id,
          soda_amount: 50,
          cap_amount: 0
        })
        .select()
        .single();

      if (!transfer) {
        throw new Error("Transfer creation failed");
      }

      createdTransferIds.push(transfer.id);

      // Check if notification was created for receiver (provider) and validate structure
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "transaction.transfer.received");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "transaction.transfer.received",
        (data) => data.amount === 50
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.amount).toBe(50);
      expect(validatedNotification!.data.transfer_id).toBe(transfer.id);
      expect(validatedNotification!.data.sender_username).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });
  });

  describe("Withdrawal Notifications", () => {
    test("withdrawal status change notification is sent", async () => {
      if (!customer.data) throw new Error("Customer not defined");

      // Create a withdrawal request
      const { data: withdrawalRequest, error: withdrawalError } =
        await serviceClient
          .schema("app_transaction")
          .from("withdrawal_request")
          .insert({
            user_id: customer.data.id,
            soda_amount: 200,
            currency: "TRY",
            status: "pending"
          })
          .select()
          .single();

      if (!withdrawalRequest) {
        throw new Error(
          `Withdrawal request creation failed: ${withdrawalError?.message || "Unknown error"}`
        );
      }

      createdWithdrawalRequestIds.push(withdrawalRequest.user_id);

      // Update withdrawal status
      await serviceClient
        .schema("app_transaction")
        .from("withdrawal_request")
        .update({ status: "processing" })
        .eq("user_id", withdrawalRequest.user_id);

      // Check if notification was created
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "transaction.withdrawal.status_change");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "transaction.withdrawal.status_change",
        (data) =>
          data.old_status === "pending" && data.new_status === "processing"
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.amount).toBe(200);
      expect(validatedNotification!.data.old_status).toBe("pending");
      expect(validatedNotification!.data.new_status).toBe("processing");
      expect(validatedNotification!.data.withdrawal_id).toBe(customer.data.id);

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });
  });

  describe("Escrow Notifications", () => {
    test("escrow status change notification is sent to both parties", async () => {
      if (!customer.data || !provider.data)
        throw new Error("Users not defined");

      // Create an escrow
      const { data: escrow } = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .insert({
          sender_id: customer.data.id,
          receiver_id: provider.data.id,
          soda_amount: 200,
          status: "pending"
        })
        .select()
        .single();

      if (!escrow) {
        throw new Error("Escrow creation failed");
      }

      createdEscrowIds.push(escrow.id);

      // Update escrow status to released
      await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .update({ status: "released" })
        .eq("id", escrow.id);

      // Check if notifications were created for both sender and receiver and validate structure
      const { data: senderNotifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "transaction.escrow.status_change");

      const { data: receiverNotifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "transaction.escrow.status_change");

      expect(senderNotifications).toBeDefined();
      expect(receiverNotifications).toBeDefined();

      const validatedSenderNotification = findValidatedNotification(
        senderNotifications!,
        "transaction.escrow.status_change",
        (data) => data.new_status === "released"
      );

      const validatedReceiverNotification = findValidatedNotification(
        receiverNotifications!,
        "transaction.escrow.status_change",
        (data) => data.new_status === "released"
      );

      expect(validatedSenderNotification).toBeDefined();
      expect(validatedSenderNotification!.data.new_status).toBe("released");
      expect(validatedSenderNotification!.data.old_status).toBe("pending");
      expect(validatedSenderNotification!.data.amount).toBe(200);
      expect(validatedSenderNotification!.data.escrow_id).toBe(escrow.id);

      expect(validatedReceiverNotification).toBeDefined();
      expect(validatedReceiverNotification!.data.new_status).toBe("released");
      expect(validatedReceiverNotification!.data.old_status).toBe("pending");
      expect(validatedReceiverNotification!.data.amount).toBe(200);
      expect(validatedReceiverNotification!.data.escrow_id).toBe(escrow.id);

      if (validatedSenderNotification!.notification.id) {
        createdNotificationIds.push(
          validatedSenderNotification!.notification.id
        );
      }
      if (validatedReceiverNotification!.notification.id) {
        createdNotificationIds.push(
          validatedReceiverNotification!.notification.id
        );
      }
    });
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }

    // Cleanup created deposits
    if (createdDepositIds.length > 0) {
      await serviceClient
        .schema("app_transaction")
        .from("deposit")
        .delete()
        .in("id", createdDepositIds);
    }

    // Cleanup created transfers
    if (createdTransferIds.length > 0) {
      await serviceClient
        .schema("app_transaction")
        .from("transfer")
        .delete()
        .in("id", createdTransferIds);
    }

    // Cleanup created escrows
    if (createdEscrowIds.length > 0) {
      await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .delete()
        .in("id", createdEscrowIds);
    }

    // Cleanup created withdrawal requests
    if (createdWithdrawalRequestIds.length > 0) {
      await serviceClient
        .schema("app_transaction")
        .from("withdrawal_request")
        .delete()
        .in("user_id", createdWithdrawalRequestIds);
    }
  });
});
