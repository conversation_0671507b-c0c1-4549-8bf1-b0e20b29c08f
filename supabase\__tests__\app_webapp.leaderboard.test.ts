import { describe, test, expect, beforeAll } from "vitest";
import { mockCustomer } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";

describe("Caps Leaderboard", () => {
  const customer1 = mockCustomer({ cap_balance: 100000 });
  const customer2 = mockCustomer({ cap_balance: 200000 });
  const customer3 = mockCustomer({ cap_balance: 50000 });
  const customer4 = mockCustomer({ cap_balance: 0 }); // Should not appear in leaderboard
  const customer5 = mockCustomer({ cap_balance: 150000 });

  beforeAll(async () => {
    await serviceClient.schema("app_webapp").rpc("refresh_caps_leaderboard");
  });

  describe("Materialized View Creation", () => {
    const customer6 = mockCustomer({ cap_balance: 350000 });

    test("should create caps_leaderboard materialized view", async () => {
      const { data, error } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*")
        .limit(1);

      expect(error).toBeNull();
      expect(data).toBeDefined();
    });

    test("should refresh materialized view successfully", async () => {
      // get winner before refresh
      const { data: winnerBefore } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*")
        .order("rank")
        .limit(1)
        .single();

      expect(winnerBefore?.user_id).toBe(customer2.data?.id);

      const { error } = await serviceClient
        .schema("app_webapp")
        .rpc("refresh_caps_leaderboard");

      expect(error).toBeNull();

      const { data: winnerAfter } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*")
        .order("rank")
        .limit(1)
        .single();

      expect(winnerAfter?.user_id).toBe(customer6.data?.id);
    });
  });

  describe("Leaderboard Ranking", () => {
    test("should rank users correctly by cap balance", async () => {
      if (
        !customer1.data ||
        !customer2.data ||
        !customer3.data ||
        !customer5.data
      ) {
        throw new Error("Customer data not defined");
      }

      const { data: leaderboard, error } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*")
        .order("rank");

      expect(error).toBeNull();
      expect(leaderboard).toBeDefined();

      // Find our test users in the leaderboard
      const user1Rank = leaderboard?.find(
        (entry) => entry.user_id === customer1.data?.id
      );
      const user2Rank = leaderboard?.find(
        (entry) => entry.user_id === customer2.data?.id
      );
      const user3Rank = leaderboard?.find(
        (entry) => entry.user_id === customer3.data?.id
      );
      const user4Rank = leaderboard?.find(
        (entry) => entry.user_id === customer4.data?.id
      );
      const user5Rank = leaderboard?.find(
        (entry) => entry.user_id === customer5.data?.id
      );

      // Verify ranking order: customer2 (2000) > customer5 (1500) > customer1 (1000) > customer3 (500)
      expect(user2Rank?.cap_balance).toBe(200000);
      expect(user5Rank?.cap_balance).toBe(150000);
      expect(user1Rank?.cap_balance).toBe(100000);
      expect(user3Rank?.cap_balance).toBe(50000);

      // Customer4 with 0 caps should not appear
      expect(user4Rank).toBeUndefined();

      // Verify ranks are in correct order
      if (user2Rank && user5Rank && user1Rank && user3Rank) {
        expect(user2Rank.rank).toBeLessThan(Number(user5Rank.rank));
        expect(user5Rank.rank).toBeLessThan(Number(user1Rank.rank));
        expect(user1Rank.rank).toBeLessThan(Number(user3Rank.rank));
      }
    });

    test("should include user profile information", async () => {
      if (!customer1.data) throw new Error("Customer1 data not defined");

      const { data: leaderboard, error } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*")
        .order("rank");

      expect(error).toBeNull();

      const user1Entry = leaderboard?.find(
        (entry) => entry.user_id === customer1.data?.id
      );
      expect(user1Entry).toBeDefined();
      expect(user1Entry?.username).toBeDefined();
      expect(user1Entry?.join_date).toBeDefined();
    });

    // mock 20 users with varying cap balances
    Array.from({ length: 16 }).forEach(async () => {
      mockCustomer({ cap_balance: 200 });
    });

    test("should limit to top 20 users only", async () => {
      // get total user count from profile
      const { data: users } = await serviceClient
        .schema("app_account")
        .from("profile")
        .select("user_id");

      expect(users?.length).toBeGreaterThan(20);

      const { data: leaderboard, error } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*");

      expect(error).toBeNull();
      expect(leaderboard).toBeDefined();
      expect(leaderboard!.length).toBeLessThanOrEqual(20);
    });
  });

  describe("User Rank Lookup", () => {
    test("should get user's rank correctly", async () => {
      if (!customer2.client || !customer2.data) {
        throw new Error("Customer2 not defined");
      }

      const { data: userRank, error } = await customer2.client
        .schema("app_webapp")
        .rpc("get_user_caps_rank");

      expect(error).toBeNull();
      expect(userRank).toBeDefined();
      expect(userRank?.[0]?.cap_balance).toBe(200000);
      expect(userRank?.[0]?.rank).toBeDefined();
      expect(userRank?.[0]?.total_users).toBeGreaterThan(0);
    });

    test("should return empty result for user with 0 caps", async () => {
      if (!customer4.client) throw new Error("Customer4 client not defined");

      const { data: userRank, error } = await customer4.client
        .schema("app_webapp")
        .rpc("get_user_caps_rank");

      expect(error).toBeNull();
      expect(userRank).toEqual([]);
    });
  });

  describe("Privacy Settings", () => {
    const hiddenCustomer = mockCustomer({ cap_balance: 9999999 });

    test("should hide users who opt out of leaderboard", async () => {
      if (!hiddenCustomer.data || !hiddenCustomer.client) {
        throw new Error("Hidden customer not defined");
      }

      // Set privacy to hide from leaderboard
      await serviceClient.schema("app_account").from("privacy").upsert({
        user_id: hiddenCustomer.data.id,
        show_in_leaderboard: false,
        show_activity: true
      });

      // Refresh leaderboard to apply privacy changes
      await serviceClient.schema("app_webapp").rpc("refresh_caps_leaderboard");

      const { data: leaderboard, error } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*");

      expect(error).toBeNull();

      // User should not appear in leaderboard despite having high cap balance
      const hiddenUserEntry = leaderboard?.find(
        (entry) => entry.user_id === hiddenCustomer.data?.id
      );
      expect(hiddenUserEntry).toBeUndefined();

      // User should not get their rank either
      const { data: userRank } = await hiddenCustomer.client
        .schema("app_webapp")
        .rpc("get_user_caps_rank");

      expect(userRank).toEqual([]);
    });

    test("should show users with default privacy settings", async () => {
      if (!customer1.data) throw new Error("Customer1 data not defined");

      // Ensure user has default privacy (should show in leaderboard)
      await serviceClient.schema("app_account").from("privacy").upsert({
        user_id: customer1.data.id,
        show_in_leaderboard: true,
        show_activity: true
      });

      // Refresh leaderboard
      await serviceClient.schema("app_webapp").rpc("refresh_caps_leaderboard");

      const { data: leaderboard, error } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*");

      expect(error).toBeNull();

      const userEntry = leaderboard?.find(
        (entry) => entry.user_id === customer1.data?.id
      );
      expect(userEntry).toBeDefined();
      expect(userEntry?.cap_balance).toBe(100000);
    });
  });

  describe("Edge Cases", () => {
    test("should handle materialized view queries correctly", async () => {
      const { data: leaderboard, error } = await serviceClient
        .schema("app_webapp")
        .from("caps_leaderboard")
        .select("*")
        .order("rank");

      expect(error).toBeNull();
      expect(leaderboard).toBeDefined();

      // Verify all entries have required fields
      leaderboard?.forEach((entry) => {
        expect(entry.rank).toBeDefined();
        expect(entry.user_id).toBeDefined();
        expect(entry.cap_balance).toBeGreaterThan(0);
        expect(entry.username).toBeDefined();
      });
    });
  });
});
