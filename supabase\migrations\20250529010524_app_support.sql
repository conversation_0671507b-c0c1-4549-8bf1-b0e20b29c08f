-- section <PERSON><PERSON>EMA
DROP SCHEMA IF EXISTS app_support CASCADE
;

CREATE SCHEMA app_support
;

GRANT USAGE ON SCHEMA app_support TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_support TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_support TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_support TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_support
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_support
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_support
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor TICKET_TYPE
CREATE TYPE app_support.TICKET_TYPE AS ENUM('report', 'dispute')
;

-- anchor TICKET_STATUS
CREATE TYPE app_support.TICKET_STATUS AS ENUM('open', 'closed')
;

-- !section
-- section TABLES
-- anchor ticket
CREATE TABLE app_support.ticket (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
  user_id UUID REFERENCES auth.users (id) ON DELETE CASCADE,
  assigned_to UUID REFERENCES auth.users (id) ON DELETE CASCADE,
  disputed_order_id UUID REFERENCES app_provider.order (id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  problem_description TEXT NOT NULL,
  status app_support.TICKET_STATUS NOT NULL DEFAULT 'open',
  type app_support.TICKET_TYPE NOT NULL,
  resolution_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL
)
;

-- anchor ticket_comment
CREATE TABLE app_support.ticket_comment (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
  ticket_id UUID NOT NULL REFERENCES app_support.ticket (id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  comment_message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL
)
;

-- anchor flag
CREATE TABLE app_support.flag (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
  flagger_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  flagged_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  flagged_service_id UUID REFERENCES app_provider.service (id) ON DELETE CASCADE,
  flagged_service_modifier_id UUID REFERENCES app_provider.service_modifier (id) ON DELETE CASCADE,
  flagged_field_value_id UUID REFERENCES app_provider.field_value (id) ON DELETE CASCADE,
  flagged_activity_id UUID REFERENCES app_provider.activity (id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  additional_info TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL
)
;

-- !section
-- section TRIGGERS
-- anchor ticket
CREATE TRIGGER on_update_ticket BEFORE
UPDATE ON app_support.ticket FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- !section
-- section FUNCTIONS
-- anchor create_ticket
CREATE OR REPLACE FUNCTION app_support.create_ticket (
  p_title TEXT,
  p_problem_description TEXT,
  p_type app_support.TICKET_TYPE,
  p_disputed_order_id UUID DEFAULT NULL
) RETURNS app_support.ticket LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_active_ticket_count INTEGER;
  v_new_ticket app_support.ticket;
BEGIN
  -- Check if user has capability to create tickets
  IF NOT app_access.has_capability('app_support.ticket.create.own') THEN
    RAISE EXCEPTION 'Insufficient permissions to create tickets';
  END IF;

  -- Validate dispute ticket requirements
  IF p_type = 'dispute' THEN
    IF p_disputed_order_id IS NULL THEN
      RAISE EXCEPTION 'Disputed order ID is required for dispute tickets';
    END IF;

    -- Verify the order exists and user is involved in it
    IF NOT EXISTS (
      SELECT 1 FROM app_provider.order
      WHERE id = p_disputed_order_id
      AND (sender_id = v_user_id OR receiver_id = v_user_id)
    ) THEN
      RAISE EXCEPTION 'Invalid disputed order ID or user not involved in the order';
    END IF;
  ELSE
    -- For non-dispute tickets, disputed_order_id should be NULL
    IF p_disputed_order_id IS NOT NULL THEN
      RAISE EXCEPTION 'Disputed order ID should only be provided for dispute tickets';
    END IF;
  END IF;

  -- Check active ticket limit (10 per user)
  SELECT COUNT(*) INTO v_active_ticket_count
  FROM app_support.ticket
  WHERE user_id = v_user_id
  AND status = 'open';

  IF v_active_ticket_count >= 10 THEN
    RAISE EXCEPTION 'Maximum active ticket limit (10) reached. Please wait for existing tickets to be resolved.';
  END IF;

  -- Create the ticket
  INSERT INTO app_support.ticket (
    user_id,
    disputed_order_id,
    title,
    problem_description,
    type
  ) VALUES (
    v_user_id,
    p_disputed_order_id,
    p_title,
    p_problem_description,
    p_type
  ) RETURNING * INTO v_new_ticket;

  RETURN v_new_ticket;
END;
$$
;

-- anchor update_ticket_status
CREATE OR REPLACE FUNCTION app_support.update_ticket_status (
  p_ticket_id UUID,
  p_new_status app_support.TICKET_STATUS,
  p_resolution_notes TEXT DEFAULT NULL
) RETURNS app_support.ticket LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_ticket app_support.ticket;
  v_updated_ticket app_support.ticket;
BEGIN
  -- Get the ticket
  SELECT * INTO v_ticket
  FROM app_support.ticket
  WHERE id = p_ticket_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Ticket not found';
  END IF;

  -- Check permissions
  IF NOT (
    -- Admin can update any ticket status
    app_access.has_capability('app_support.ticket.edit.status') OR
    -- Support agent can update assigned tickets
    (app_access.has_capability('app_support.ticket.edit.assign') AND v_ticket.assigned_to = v_user_id)
  ) THEN
    RAISE EXCEPTION 'Insufficient permissions to update ticket status';
  END IF;

  -- Validate status transitions
  IF v_ticket.status = 'closed' AND p_new_status != 'open' THEN
    RAISE EXCEPTION 'Closed tickets can only be reopened';
  END IF;

  -- Update the ticket
  UPDATE app_support.ticket
  SET
    status = p_new_status,
    resolution_notes = CASE
      WHEN p_new_status = 'closed' THEN COALESCE(p_resolution_notes, resolution_notes)
      ELSE resolution_notes
    END
  WHERE id = p_ticket_id
  RETURNING * INTO v_updated_ticket;

  RETURN v_updated_ticket;
END;
$$
;

-- anchor assign_ticket
CREATE OR REPLACE FUNCTION app_support.assign_ticket (
  p_ticket_id UUID,
  p_assigned_to UUID DEFAULT NULL
) RETURNS app_support.ticket LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_ticket app_support.ticket;
  v_updated_ticket app_support.ticket;
BEGIN
  -- Check if user has capability to assign tickets
  IF NOT app_access.has_capability('app_support.ticket.edit.assign') THEN
    RAISE EXCEPTION 'Insufficient permissions to assign tickets';
  END IF;

  -- Get the ticket
  SELECT * INTO v_ticket
  FROM app_support.ticket
  WHERE id = p_ticket_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Ticket not found';
  END IF;

  -- Validate assignee exists and has support capabilities
  IF p_assigned_to IS NOT NULL THEN
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_assigned_to) THEN
      RAISE EXCEPTION 'Assigned user does not exist';
    END IF;

    -- Check if assignee has support capabilities
    IF NOT (
      app_access.has_capability('app_support.ticket.view.assigned', p_assigned_to) OR
      app_access.has_capability('app_support.ticket.view.all', p_assigned_to)
    ) THEN
      RAISE EXCEPTION 'Assigned user does not have support capabilities';
    END IF;
  END IF;

  -- Update the ticket assignment
  UPDATE app_support.ticket
  SET assigned_to = p_assigned_to
  WHERE id = p_ticket_id
  RETURNING * INTO v_updated_ticket;

  RETURN v_updated_ticket;
END;
$$
;

-- anchor create_ticket_comment
CREATE OR REPLACE FUNCTION app_support.create_ticket_comment (
  p_ticket_id UUID,
  p_comment_message TEXT
) RETURNS app_support.ticket_comment LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_ticket app_support.ticket;
  v_consecutive_count INTEGER := 0;
  v_last_comment_user_id UUID;
  v_new_comment app_support.ticket_comment;
BEGIN
  -- Check if user has capability to create comments
  IF NOT app_access.has_capability('app_support.ticket_comment.create.own') THEN
    RAISE EXCEPTION 'Insufficient permissions to create ticket comments';
  END IF;

  -- Get the ticket and verify it exists
  SELECT * INTO v_ticket
  FROM app_support.ticket
  WHERE id = p_ticket_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Ticket not found';
  END IF;

  -- Check if ticket is closed
  IF v_ticket.status = 'closed' THEN
    RAISE EXCEPTION 'Cannot add comments to closed tickets';
  END IF;

  -- Check if user is allowed to comment on this ticket
  IF NOT (
    v_ticket.user_id = v_user_id OR
    v_ticket.assigned_to = v_user_id OR
    app_access.has_capability('app_support.ticket.view.all')
  ) THEN
    RAISE EXCEPTION 'Insufficient permissions to comment on this ticket';
  END IF;

  -- Check consecutive comment limit (10 consecutive comments per user)
  -- Count consecutive comments from the same user starting from the most recent
  WITH recent_comments AS (
    SELECT user_id, ROW_NUMBER() OVER (ORDER BY created_at DESC) as rn
    FROM app_support.ticket_comment
    WHERE ticket_id = p_ticket_id
    ORDER BY created_at DESC
    LIMIT 10
  )
  SELECT COUNT(*) INTO v_consecutive_count
  FROM recent_comments
  WHERE user_id = v_user_id
  AND rn <= (
    SELECT COALESCE(MIN(rn), 11)
    FROM recent_comments
    WHERE user_id != v_user_id
  );

  IF v_consecutive_count >= 10 THEN
    RAISE EXCEPTION 'Maximum consecutive comment limit (10) reached. Please wait for another user to comment.';
  END IF;

  -- Create the comment
  INSERT INTO app_support.ticket_comment (
    ticket_id,
    user_id,
    comment_message
  ) VALUES (
    p_ticket_id,
    v_user_id,
    p_comment_message
  ) RETURNING * INTO v_new_comment;

  RETURN v_new_comment;
END;
$$
;

-- anchor create_flag
CREATE OR REPLACE FUNCTION app_support.create_flag (
  p_flagged_id UUID,
  p_reason TEXT,
  p_additional_info TEXT DEFAULT NULL,
  p_flagged_service_id UUID DEFAULT NULL,
  p_flagged_service_modifier_id UUID DEFAULT NULL,
  p_flagged_field_value_id UUID DEFAULT NULL,
  p_flagged_activity_id UUID DEFAULT NULL
) RETURNS app_support.flag LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_user_id UUID := auth.uid();
  v_new_flag app_support.flag;
BEGIN
  -- Check if user has capability to create flags
  IF NOT app_access.has_capability('app_support.flag.create') THEN
    RAISE EXCEPTION 'Insufficient permissions to create flags';
  END IF;

  -- Create the flag
  INSERT INTO app_support.flag (
    flagger_id,
    flagged_id,
    reason,
    additional_info,
    flagged_service_id,
    flagged_service_modifier_id,
    flagged_field_value_id,
    flagged_activity_id
  ) VALUES (
    v_user_id,
    p_flagged_id,
    p_reason,
    p_additional_info,
    p_flagged_service_id,
    p_flagged_service_modifier_id,
    p_flagged_field_value_id,
    p_flagged_activity_id
  ) RETURNING * INTO v_new_flag;

  RETURN v_new_flag;
END;
$$
;

-- anchor notify_ticket_created
CREATE OR REPLACE FUNCTION app_support.notify_ticket_created () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  PERFORM app_account.create_notification(
    'support.ticket.created',
    NEW.user_id,
    'notifications.support.ticket.created.title',
    'notifications.support.ticket.created.message',
    jsonb_build_object(
      'ticket_id', NEW.id,
      'title', NEW.title,
      'type', NEW.type
    ),
    '/support/tickets/' || NEW.id::text
  );

  RETURN NEW;
END;
$$
;

-- anchor notify_ticket_status_change
CREATE OR REPLACE FUNCTION app_support.notify_ticket_status_change () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- Only notify if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    PERFORM app_account.create_notification(
      'support.ticket.status_change',
      NEW.user_id,
      'notifications.support.ticket.status_change.title',
      'notifications.support.ticket.status_change.message',
      jsonb_build_object(
        'ticket_id', NEW.id,
        'title', NEW.title,
        'old_status', OLD.status,
        'new_status', NEW.status
      ),
      '/support/tickets/' || NEW.id::text
    );
  END IF;

  RETURN NEW;
END;
$$
;

-- anchor notify_ticket_comment
CREATE OR REPLACE FUNCTION app_support.notify_ticket_comment () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_ticket_user_id UUID;
  v_commenter_username TEXT;
  v_ticket_title TEXT;
BEGIN
  -- Get ticket owner and title
  SELECT user_id, title INTO v_ticket_user_id, v_ticket_title
  FROM app_support.ticket
  WHERE id = NEW.ticket_id;

  -- Get commenter username
  SELECT username INTO v_commenter_username
  FROM app_account.profile
  WHERE user_id = NEW.user_id;

  -- Only notify if the commenter is not the ticket owner
  IF v_ticket_user_id != NEW.user_id THEN
    PERFORM app_account.create_notification(
      'support.ticket.comment',
      v_ticket_user_id,
      'notifications.support.ticket.comment.title',
      'notifications.support.ticket.comment.message',
      jsonb_build_object(
        'ticket_id', NEW.ticket_id,
        'comment_id', NEW.id,
        'ticket_title', v_ticket_title,
        'commenter_username', COALESCE(v_commenter_username, 'Support Agent'),
        'content_preview', LEFT(NEW.comment_message, 50)
      ),
      '/support/tickets/' || NEW.ticket_id::text
    );
  END IF;

  RETURN NEW;
END;
$$
;

-- anchor notify_flag_created
CREATE OR REPLACE FUNCTION app_support.notify_flag_created () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  PERFORM app_account.create_notification(
    'support.flag.created',
    NEW.flagger_id,
    'notifications.support.flag.created.title',
    'notifications.support.flag.created.message',
    jsonb_build_object(
      'flag_id', NEW.id,
      'reason', NEW.reason,
      'flagged_id', NEW.flagged_id
    ),
    '/support/flags'
  );

  RETURN NEW;
END;
$$
;

-- !section
-- section TRIGGERS
-- anchor notification triggers
CREATE TRIGGER ticket_notify_created
AFTER INSERT ON app_support.ticket FOR EACH ROW
EXECUTE FUNCTION app_support.notify_ticket_created ()
;

CREATE TRIGGER ticket_notify_status_change
AFTER UPDATE OF status ON app_support.ticket FOR EACH ROW
EXECUTE FUNCTION app_support.notify_ticket_status_change ()
;

CREATE TRIGGER ticket_comment_notify_new
AFTER INSERT ON app_support.ticket_comment FOR EACH ROW
EXECUTE FUNCTION app_support.notify_ticket_comment ()
;

CREATE TRIGGER flag_notify_created
AFTER INSERT ON app_support.flag FOR EACH ROW
EXECUTE FUNCTION app_support.notify_flag_created ()
;

-- !section
-- section RLS POLICIES
-- anchor ticket
ALTER TABLE app_support.ticket ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "ticket_select_own" ON app_support.ticket FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability ('app_support.ticket.view.own')
    )
    AND (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "ticket_select_assigned" ON app_support.ticket FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability (
          'app_support.ticket.view.assigned'
        )
    )
    AND assigned_to = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "ticket_select_all_admin" ON app_support.ticket FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability ('app_support.ticket.view.all')
    )
  )
;

-- Prevent direct INSERT - must use create_ticket function
CREATE POLICY "ticket_insert_deny_all" ON app_support.ticket FOR INSERT
WITH
  CHECK (FALSE)
;

-- Prevent direct UPDATE - must use update_ticket_status or assign_ticket functions
CREATE POLICY "ticket_update_deny_all" ON app_support.ticket
FOR UPDATE
  USING (FALSE)
;

-- anchor ticket_comment
ALTER TABLE app_support.ticket_comment ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "ticket_comment_select_own" ON app_support.ticket_comment FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability ('app_support.ticket.view.own')
    )
    AND (
      SELECT
        EXISTS (
          SELECT
            1
          FROM
            app_support.ticket
          WHERE
            id = ticket_id
            AND user_id = (
              SELECT
                auth.uid ()
            )
        )
    )
  )
;

CREATE POLICY "ticket_comment_select_assigned" ON app_support.ticket_comment FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability (
          'app_support.ticket_comment.view.assigned'
        )
    )
    AND (
      SELECT
        EXISTS (
          SELECT
            1
          FROM
            app_support.ticket
          WHERE
            id = ticket_id
            AND assigned_to = (
              SELECT
                auth.uid ()
            )
        )
    )
  )
;

CREATE POLICY "ticket_comment_select_all_admin" ON app_support.ticket_comment FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability (
          'app_support.ticket_comment.view.all'
        )
    )
  )
;

-- Prevent direct INSERT - must use create_ticket_comment function
CREATE POLICY "ticket_comment_insert_deny_all" ON app_support.ticket_comment FOR INSERT
WITH
  CHECK (FALSE)
;

-- Policy to allow admins to delete tickets
CREATE POLICY "ticket_delete_admin" ON app_support.ticket FOR DELETE USING (
  (
    SELECT
      app_access.has_capability (
        'app_support.ticket.delete.all'
      )
  )
)
;

-- Policy to allow admins to delete ticket comments
CREATE POLICY "ticket_comment_delete_admin" ON app_support.ticket_comment FOR DELETE USING (
  (
    SELECT
      app_access.has_capability (
        'app_support.ticket_comment.delete.all'
      )
  )
)
;

-- anchor flag
ALTER TABLE app_support.flag ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "flag_select_admin" ON app_support.flag FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability ('app_support.flag.view.all')
    )
  )
;

-- Policy to allow admins to delete flags
CREATE POLICY "flag_delete_admin" ON app_support.flag FOR DELETE USING (
  (
    SELECT
      app_access.has_capability ('app_support.flag.delete.all')
  )
)
;

-- !section
-- section CAPABILITIES
-- anchor customer
SELECT
  app_access.define_role_capability (
    'customer',
    ARRAY[
      'app_support.ticket.view.own',
      'app_support.ticket.create.own',
      'app_support.ticket_comment.create.own',
      'app_support.flag.create'
    ]
  )
;

-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY[
      'app_support.ticket.view.own',
      'app_support.ticket.create.own',
      'app_support.ticket_comment.create.own',
      'app_support.flag.create'
    ]
  )
;

-- anchor support_agent
SELECT
  app_access.define_role_capability (
    'support_agent',
    ARRAY[
      'app_support.ticket.view.assigned',
      'app_support.ticket.edit.assign',
      'app_support.ticket.edit.status',
      'app_support.ticket_comment.create.own',
      'app_support.flag.create'
    ]
  )
;

-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'app_support.ticket.view.all',
      'app_support.ticket.edit.assign',
      'app_support.ticket.edit.status',
      'app_support.ticket.delete.all',
      'app_support.ticket_comment.create.own',
      'app_support.ticket_comment.delete.all',
      'app_support.flag.create',
      'app_support.flag.view.all',
      'app_support.flag.delete.all'
    ]
  )
;

-- !section