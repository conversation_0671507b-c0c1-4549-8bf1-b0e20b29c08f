"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+icu-skeleton-parser@1.8.14";
exports.ids = ["vendor-chunks/@formatjs+icu-skeleton-parser@1.8.14"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC4xNC9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1za2VsZXRvbi1wYXJzZXIvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTRCO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAZm9ybWF0anMraWN1LXNrZWxldG9uLXBhcnNlckAxLjguMTRcXG5vZGVfbW9kdWxlc1xcQGZvcm1hdGpzXFxpY3Utc2tlbGV0b24tcGFyc2VyXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vZGF0ZS10aW1lJztcbmV4cG9ydCAqIGZyb20gJy4vbnVtYmVyJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(rsc)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC4xNC9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1za2VsZXRvbi1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC4xNFxcbm9kZV9tb2R1bGVzXFxAZm9ybWF0anNcXGljdS1za2VsZXRvbi1wYXJzZXJcXGxpYlxccmVnZXguZ2VuZXJhdGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEBnZW5lcmF0ZWQgZnJvbSByZWdleC1nZW4udHNcbmV4cG9ydCB2YXIgV0hJVEVfU1BBQ0VfUkVHRVggPSAvW1xcdC1cXHIgXFx4ODVcXHUyMDBFXFx1MjAwRlxcdTIwMjhcXHUyMDI5XS9pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC4xNC9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1za2VsZXRvbi1wYXJzZXIvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTRCO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAZm9ybWF0anMraWN1LXNrZWxldG9uLXBhcnNlckAxLjguMTRcXG5vZGVfbW9kdWxlc1xcQGZvcm1hdGpzXFxpY3Utc2tlbGV0b24tcGFyc2VyXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vZGF0ZS10aW1lJztcbmV4cG9ydCAqIGZyb20gJy4vbnVtYmVyJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC4xNC9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1za2VsZXRvbi1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC4xNFxcbm9kZV9tb2R1bGVzXFxAZm9ybWF0anNcXGljdS1za2VsZXRvbi1wYXJzZXJcXGxpYlxccmVnZXguZ2VuZXJhdGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEBnZW5lcmF0ZWQgZnJvbSByZWdleC1nZW4udHNcbmV4cG9ydCB2YXIgV0hJVEVfU1BBQ0VfUkVHRVggPSAvW1xcdC1cXHIgXFx4ODVcXHUyMDBFXFx1MjAwRlxcdTIwMjhcXHUyMDI5XS9pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n");

/***/ })

};
;