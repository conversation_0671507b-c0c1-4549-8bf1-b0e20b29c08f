"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { catalogClient } from "@/lib/supabase";
import { useRealtimeSubscription } from "@/hooks/useRealtimeSubscription";
import { Category } from "@/lib/types";
import { Plus, Search, Edit, Trash2, Layers } from "lucide-react";

export default function CategoriesPage() {
  const [search, setSearch] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const queryClient = useQueryClient();

  const { data: categories, isLoading } = useQuery({
    queryKey: ["categories", search],
    queryFn: async () => {
      let query = catalogClient
        .from("category")
        .select("*")
        .order("created_at", { ascending: false });

      if (search) {
        query = query.or(
          `name->en.ilike.%${search}%,name->ja.ilike.%${search}%,name->ko.ilike.%${search}%,name->tr.ilike.%${search}%`
        );
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as Category[];
    }
  });

  // Get activity counts per category
  const { data: activityCounts } = useQuery({
    queryKey: ["category-activity-counts"],
    queryFn: async () => {
      const { data, error } = await catalogClient
        .from("activity")
        .select("category_id");
      
      if (error) throw error;
      
      const counts: Record<string, number> = {};
      data.forEach((activity) => {
        counts[activity.category_id] = (counts[activity.category_id] || 0) + 1;
      });
      
      return counts;
    }
  });

  useRealtimeSubscription({
    schema: "app_catalog",
    table: "category",
    queryKey: ["categories", search]
  });

  const deleteMutation = useMutation({
    mutationFn: async (categoryId: string) => {
      const { error } = await catalogClient
        .from("category")
        .delete()
        .eq("id", categoryId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      queryClient.invalidateQueries({ queryKey: ["category-activity-counts"] });
    }
  });

  const handleDelete = async (categoryId: string) => {
    const activityCount = activityCounts?.[categoryId] || 0;
    if (activityCount > 0) {
      alert(`Cannot delete category with ${activityCount} activities. Please move or delete the activities first.`);
      return;
    }
    
    if (confirm("Are you sure you want to delete this category?")) {
      deleteMutation.mutate(categoryId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading categories...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage activity categories and their organization
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </button>
      </div>

      {/* Search */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search categories..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories?.map((category) => (
          <div
            key={category.id}
            className="bg-white shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: category.color }}
                  >
                    <Layers className="h-3 w-3 text-white" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {category.name.en || category.name.ja || category.name.ko || category.name.tr}
                  </h3>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="Edit category"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="p-1 text-gray-400 hover:text-red-600"
                    title="Delete category"
                    disabled={deleteMutation.isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">
                Slug: {category.slug}
              </p>
              
              {category.description && (
                <p className="text-sm text-gray-700 mb-4">
                  {category.description.en || category.description.ja || category.description.ko || category.description.tr}
                </p>
              )}
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500">
                  {activityCounts?.[category.id] || 0} activities
                </span>
                <span className="text-gray-400">
                  Created {new Date(category.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {categories?.length === 0 && (
        <div className="text-center py-12">
          <Layers className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No categories</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new category.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
