"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Users,
  UserCheck,
  DollarSign,
  MessageSquare,
  BarChart3,
  Settings,
  Menu,
  X,
  Gamepad2,
  Tags,
  Layers
} from "lucide-react";

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
  {
    name: "Catalog",
    href: "/dashboard/catalog",
    icon: Layers,
    children: [
      { name: "Activities", href: "/dashboard/catalog/activities" },
      { name: "Categories", href: "/dashboard/catalog/categories" },
      { name: "Tags", href: "/dashboard/catalog/tags" },
      { name: "Fields", href: "/dashboard/catalog/fields" }
    ]
  },
  {
    name: "Users",
    href: "/dashboard/users",
    icon: Users,
    children: [
      { name: "Analytics", href: "/dashboard/users/analytics" },
      { name: "Management", href: "/dashboard/users/management" },
      { name: "Moderation", href: "/dashboard/users/moderation" }
    ]
  },
  {
    name: "Providers",
    href: "/dashboard/providers",
    icon: User<PERSON><PERSON><PERSON>,
    children: [
      { name: "Applications", href: "/dashboard/providers/applications" },
      { name: "Services", href: "/dashboard/providers/services" },
      { name: "Performance", href: "/dashboard/providers/performance" }
    ]
  },
  {
    name: "Finance",
    href: "/dashboard/finance",
    icon: DollarSign,
    children: [
      { name: "Transactions", href: "/dashboard/finance/transactions" },
      { name: "Currency", href: "/dashboard/finance/currency" },
      { name: "Disputes", href: "/dashboard/finance/disputes" }
    ]
  },
  {
    name: "Support",
    href: "/dashboard/support",
    icon: MessageSquare,
    children: [
      { name: "Tickets", href: "/dashboard/support/tickets" },
      { name: "Community", href: "/dashboard/support/community" }
    ]
  },
  {
    name: "Analytics",
    href: "/dashboard/analytics",
    icon: BarChart3,
    children: [
      { name: "Overview", href: "/dashboard/analytics/overview" },
      { name: "Reports", href: "/dashboard/analytics/reports" }
    ]
  }
];

export function DashboardSidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();

  return (
    <>
      {/* Mobile sidebar */}
      <div className={`lg:hidden ${sidebarOpen ? "block" : "hidden"}`}>
        <div className="fixed inset-0 z-40 flex">
          <div
            className="fixed inset-0 bg-gray-600 bg-opacity-75"
            onClick={() => setSidebarOpen(false)}
          />
          <div className="relative flex w-full max-w-xs flex-1 flex-col bg-white">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                className="ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setSidebarOpen(false)}
              >
                <X className="h-6 w-6 text-white" />
              </button>
            </div>
            <SidebarContent pathname={pathname} />
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200">
          <SidebarContent pathname={pathname} />
        </div>
      </div>

      {/* Mobile menu button */}
      <div className="lg:hidden">
        <button
          type="button"
          className="fixed top-4 left-4 z-50 inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
          onClick={() => setSidebarOpen(true)}
        >
          <Menu className="h-6 w-6" />
        </button>
      </div>
    </>
  );
}

function SidebarContent({ pathname }: { pathname: string }) {
  return (
    <div className="flex flex-1 flex-col overflow-y-auto pt-5 pb-4">
      <div className="flex flex-shrink-0 items-center px-4">
        <h1 className="text-xl font-bold text-gray-900">E-Senpai Admin</h1>
      </div>
      <nav className="mt-5 flex-1 space-y-1 px-2">
        {navigation.map((item) => (
          <div key={item.name}>
            <Link
              href={item.href}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                pathname === item.href
                  ? "bg-gray-100 text-gray-900"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              }`}
            >
              <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
              {item.name}
            </Link>
            {item.children && (
              <div className="ml-8 space-y-1">
                {item.children.map((child) => (
                  <Link
                    key={child.name}
                    href={child.href}
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      pathname === child.href
                        ? "bg-gray-100 text-gray-900"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    }`}
                  >
                    {child.name}
                  </Link>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>
    </div>
  );
}
