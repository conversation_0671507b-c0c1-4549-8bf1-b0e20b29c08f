"use client";

import { useQuery } from "@tanstack/react-query";
import { catalogClient } from "@/lib/supabase";
import Link from "next/link";
import { Layers, Gamepad2, Tags, Settings, ArrowRight } from "lucide-react";

export default function CatalogOverviewPage() {
  const { data: catalogStats, isLoading } = useQuery({
    queryKey: ["catalog-stats"],
    queryFn: async () => {
      const [
        { count: activityCount },
        { count: categoryCount },
        { count: tagCount },
        { count: fieldCount }
      ] = await Promise.all([
        catalogClient
          .from("activity")
          .select("*", { count: "exact", head: true }),
        catalogClient
          .from("category")
          .select("*", { count: "exact", head: true }),
        catalogClient.from("tag").select("*", { count: "exact", head: true }),
        catalogClient.from("field").select("*", { count: "exact", head: true })
      ]);

      return {
        activities: activityCount || 0,
        categories: categoryCount || 0,
        tags: tagCount || 0,
        fields: fieldCount || 0
      };
    }
  });

  const catalogSections = [
    {
      title: "Activities",
      description: "Manage platform activities and their configurations",
      icon: Gamepad2,
      href: "/dashboard/catalog/activities",
      count: catalogStats?.activities,
      color: "bg-blue-500"
    },
    {
      title: "Categories",
      description: "Organize activities into logical categories",
      icon: Layers,
      href: "/dashboard/catalog/categories",
      count: catalogStats?.categories,
      color: "bg-green-500"
    },
    {
      title: "Tags",
      description: "Create and manage activity tags for better discovery",
      icon: Tags,
      href: "/dashboard/catalog/tags",
      count: catalogStats?.tags,
      color: "bg-yellow-500"
    },
    {
      title: "Fields",
      description: "Configure custom fields for activities and services",
      icon: Settings,
      href: "/dashboard/catalog/fields",
      count: catalogStats?.fields,
      color: "bg-purple-500"
    }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading catalog overview...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Catalog Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage activities, categories, tags, and custom fields for the
          platform
        </p>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {catalogSections.map((section) => {
          const Icon = section.icon;
          return (
            <div
              key={section.title}
              className="bg-white overflow-hidden shadow rounded-lg"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div
                      className={`w-8 h-8 ${section.color} rounded-lg flex items-center justify-center`}
                    >
                      <Icon className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {section.title}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {section.count ?? 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Management Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {catalogSections.map((section) => {
          const Icon = section.icon;
          return (
            <Link
              key={section.title}
              href={section.href}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400 transition-colors"
            >
              <div>
                <span
                  className={`rounded-lg inline-flex p-3 ${section.color} text-white ring-4 ring-white`}
                >
                  <Icon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium flex items-center justify-between">
                  <span>{section.title}</span>
                  <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600" />
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  {section.description}
                </p>
                <div className="mt-3">
                  <span className="text-sm font-medium text-gray-900">
                    {section.count ?? 0} items
                  </span>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Gamepad2 className="h-4 w-4 mr-2" />
              Add Activity
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Layers className="h-4 w-4 mr-2" />
              Add Category
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Tags className="h-4 w-4 mr-2" />
              Add Tag
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Settings className="h-4 w-4 mr-2" />
              Add Field
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
