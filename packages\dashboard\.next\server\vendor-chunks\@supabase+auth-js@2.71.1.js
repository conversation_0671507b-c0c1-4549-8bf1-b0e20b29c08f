"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+auth-js@2.71.1";
exports.ids = ["vendor-chunks/@supabase+auth-js@2.71.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n\nconst AuthAdminApi = _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthAdminApi);\n//# sourceMappingURL=AuthAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzEuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvQXV0aEFkbWluQXBpLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThDO0FBQzlDLHFCQUFxQix1REFBYztBQUNuQyxpRUFBZSxZQUFZLEVBQUM7QUFDNUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrYXV0aC1qc0AyLjcxLjFcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWpzXFxkaXN0XFxtb2R1bGVcXEF1dGhBZG1pbkFwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgR29UcnVlQWRtaW5BcGkgZnJvbSAnLi9Hb1RydWVBZG1pbkFwaSc7XG5jb25zdCBBdXRoQWRtaW5BcGkgPSBHb1RydWVBZG1pbkFwaTtcbmV4cG9ydCBkZWZhdWx0IEF1dGhBZG1pbkFwaTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUF1dGhBZG1pbkFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n\nconst AuthClient = _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthClient);\n//# sourceMappingURL=AuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzEuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvQXV0aENsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUMxQyxtQkFBbUIscURBQVk7QUFDL0IsaUVBQWUsVUFBVSxFQUFDO0FBQzFCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi43MS4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxBdXRoQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBHb1RydWVDbGllbnQgZnJvbSAnLi9Hb1RydWVDbGllbnQnO1xuY29uc3QgQXV0aENsaWVudCA9IEdvVHJ1ZUNsaWVudDtcbmV4cG9ydCBkZWZhdWx0IEF1dGhDbGllbnQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1BdXRoQ2xpZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueAdminApi)\n/* harmony export */ });\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/types */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/types.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n\nclass GoTrueAdminApi {\n    constructor({ url = '', headers = {}, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(fetch);\n        this.mfa = {\n            listFactors: this._listFactors.bind(this),\n            deleteFactor: this._deleteFactor.bind(this),\n        };\n    }\n    /**\n     * Removes a logged-in session.\n     * @param jwt A valid, logged-in JWT.\n     * @param scope The logout sope.\n     */\n    async signOut(jwt, scope = _lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES[0]) {\n        if (_lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES.indexOf(scope) < 0) {\n            throw new Error(`@supabase/auth-js: Parameter scope must be one of ${_lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES.join(', ')}`);\n        }\n        try {\n            await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n                headers: this.headers,\n                jwt,\n                noResolveJson: true,\n            });\n            return { data: null, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends an invite link to an email address.\n     * @param email The email address of the user.\n     * @param options Additional options to be included when inviting.\n     */\n    async inviteUserByEmail(email, options = {}) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/invite`, {\n                body: { email, data: options.data },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates email links and OTPs to be sent via a custom email provider.\n     * @param email The user's email.\n     * @param options.password User password. For signup only.\n     * @param options.data Optional user metadata. For signup only.\n     * @param options.redirectTo The redirect url which should be appended to the generated link\n     */\n    async generateLink(params) {\n        try {\n            const { options } = params, rest = __rest(params, [\"options\"]);\n            const body = Object.assign(Object.assign({}, rest), options);\n            if ('newEmail' in rest) {\n                // replace newEmail with new_email in request body\n                body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n                delete body['newEmail'];\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n                body: body,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._generateLinkResponse,\n                redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return {\n                    data: {\n                        properties: null,\n                        user: null,\n                    },\n                    error,\n                };\n            }\n            throw error;\n        }\n    }\n    // User Admin API\n    /**\n     * Creates a new user.\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async createUser(attributes) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/users`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get a list of users.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n     */\n    async listUsers(params) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        try {\n            const pagination = { nextPage: null, lastPage: 0, total: 0 };\n            const response = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users`, {\n                headers: this.headers,\n                noResolveJson: true,\n                query: {\n                    page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n                    per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : '',\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._noResolveJsonResponse,\n            });\n            if (response.error)\n                throw response.error;\n            const users = await response.json();\n            const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n            const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n            if (links.length > 0) {\n                links.forEach((link) => {\n                    const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n                    const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n                    pagination[`${rel}Page`] = page;\n                });\n                pagination.total = parseInt(total);\n            }\n            return { data: Object.assign(Object.assign({}, users), pagination), error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { users: [] }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get user by id.\n     *\n     * @param uid The user's unique identifier\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async getUserById(uid) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(uid);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates the user data.\n     *\n     * @param attributes The data you want to update.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async updateUserById(uid, attributes) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(uid);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Delete a user. Requires a `service_role` key.\n     *\n     * @param id The user id you want to remove.\n     * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n     * Defaults to false for backward compatibility.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async deleteUser(id, shouldSoftDelete = false) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(id);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n                headers: this.headers,\n                body: {\n                    should_soft_delete: shouldSoftDelete,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _listFactors(params) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.userId);\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n                headers: this.headers,\n                xform: (factors) => {\n                    return { data: { factors }, error: null };\n                },\n            });\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _deleteFactor(params) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.userId);\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.id);\n        try {\n            const data = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n                headers: this.headers,\n            });\n            return { data, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=GoTrueAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueClient)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_local_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/local-storage */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\");\n/* harmony import */ var _lib_polyfills__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/polyfills */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\");\n/* harmony import */ var _lib_version__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/version */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n/* harmony import */ var _lib_base64url__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/base64url */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\n\n\n\n\n\n\n\n\n(0,_lib_polyfills__WEBPACK_IMPORTED_MODULE_6__.polyfillGlobalThis)(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n    url: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.GOTRUE_URL,\n    storageKey: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEY,\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    headers: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_HEADERS,\n    flowType: 'implicit',\n    debug: false,\n    hasCustomAuthorizationHeader: false,\n};\nasync function lockNoOp(name, acquireTimeout, fn) {\n    return await fn();\n}\n/**\n * Caches JWKS values for all clients created in the same environment. This is\n * especially useful for shared-memory execution environments such as Vercel's\n * Fluid Compute, AWS Lambda or Supabase's Edge Functions. Regardless of how\n * many clients are created, if they share the same storage key they will use\n * the same JWKS cache, significantly speeding up getClaims() with asymmetric\n * JWTs.\n */\nconst GLOBAL_JWKS = {};\nclass GoTrueClient {\n    /**\n     * Create a new client for use in the browser.\n     */\n    constructor(options) {\n        var _a, _b;\n        /**\n         * @experimental\n         */\n        this.userStorage = null;\n        this.memoryStorage = null;\n        this.stateChangeEmitters = new Map();\n        this.autoRefreshTicker = null;\n        this.visibilityChangedCallback = null;\n        this.refreshingDeferred = null;\n        /**\n         * Keeps track of the async client initialization.\n         * When null or not yet resolved the auth state is `unknown`\n         * Once resolved the the auth state is known and it's save to call any further client methods.\n         * Keep extra care to never reject or throw uncaught errors\n         */\n        this.initializePromise = null;\n        this.detectSessionInUrl = true;\n        this.hasCustomAuthorizationHeader = false;\n        this.suppressGetSessionWarning = false;\n        this.lockAcquired = false;\n        this.pendingInLock = [];\n        /**\n         * Used to broadcast state change events to other tabs listening.\n         */\n        this.broadcastChannel = null;\n        this.logger = console.log;\n        this.instanceID = GoTrueClient.nextInstanceID;\n        GoTrueClient.nextInstanceID += 1;\n        if (this.instanceID > 0 && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)()) {\n            console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n        }\n        const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n        this.logDebugMessages = !!settings.debug;\n        if (typeof settings.debug === 'function') {\n            this.logger = settings.debug;\n        }\n        this.persistSession = settings.persistSession;\n        this.storageKey = settings.storageKey;\n        this.autoRefreshToken = settings.autoRefreshToken;\n        this.admin = new _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            url: settings.url,\n            headers: settings.headers,\n            fetch: settings.fetch,\n        });\n        this.url = settings.url;\n        this.headers = settings.headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.resolveFetch)(settings.fetch);\n        this.lock = settings.lock || lockNoOp;\n        this.detectSessionInUrl = settings.detectSessionInUrl;\n        this.flowType = settings.flowType;\n        this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n        if (settings.lock) {\n            this.lock = settings.lock;\n        }\n        else if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n            this.lock = _lib_locks__WEBPACK_IMPORTED_MODULE_8__.navigatorLock;\n        }\n        else {\n            this.lock = lockNoOp;\n        }\n        if (!this.jwks) {\n            this.jwks = { keys: [] };\n            this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n        }\n        this.mfa = {\n            verify: this._verify.bind(this),\n            enroll: this._enroll.bind(this),\n            unenroll: this._unenroll.bind(this),\n            challenge: this._challenge.bind(this),\n            listFactors: this._listFactors.bind(this),\n            challengeAndVerify: this._challengeAndVerify.bind(this),\n            getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n        };\n        if (this.persistSession) {\n            if (settings.storage) {\n                this.storage = settings.storage;\n            }\n            else {\n                if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.supportsLocalStorage)()) {\n                    this.storage = globalThis.localStorage;\n                }\n                else {\n                    this.memoryStorage = {};\n                    this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n                }\n            }\n            if (settings.userStorage) {\n                this.userStorage = settings.userStorage;\n            }\n        }\n        else {\n            this.memoryStorage = {};\n            this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n        }\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n            try {\n                this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n            }\n            catch (e) {\n                console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n            }\n            (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', async (event) => {\n                this._debug('received broadcast notification from other tab or client', event);\n                await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n            });\n        }\n        this.initialize();\n    }\n    /**\n     * The JWKS used for verifying asymmetric JWTs\n     */\n    get jwks() {\n        var _a, _b;\n        return (_b = (_a = GLOBAL_JWKS[this.storageKey]) === null || _a === void 0 ? void 0 : _a.jwks) !== null && _b !== void 0 ? _b : { keys: [] };\n    }\n    set jwks(value) {\n        GLOBAL_JWKS[this.storageKey] = Object.assign(Object.assign({}, GLOBAL_JWKS[this.storageKey]), { jwks: value });\n    }\n    get jwks_cached_at() {\n        var _a, _b;\n        return (_b = (_a = GLOBAL_JWKS[this.storageKey]) === null || _a === void 0 ? void 0 : _a.cachedAt) !== null && _b !== void 0 ? _b : Number.MIN_SAFE_INTEGER;\n    }\n    set jwks_cached_at(value) {\n        GLOBAL_JWKS[this.storageKey] = Object.assign(Object.assign({}, GLOBAL_JWKS[this.storageKey]), { cachedAt: value });\n    }\n    _debug(...args) {\n        if (this.logDebugMessages) {\n            this.logger(`GoTrueClient@${this.instanceID} (${_lib_version__WEBPACK_IMPORTED_MODULE_7__.version}) ${new Date().toISOString()}`, ...args);\n        }\n        return this;\n    }\n    /**\n     * Initializes the client session either from the url or from storage.\n     * This method is automatically called when instantiating the client, but should also be called\n     * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n     */\n    async initialize() {\n        if (this.initializePromise) {\n            return await this.initializePromise;\n        }\n        this.initializePromise = (async () => {\n            return await this._acquireLock(-1, async () => {\n                return await this._initialize();\n            });\n        })();\n        return await this.initializePromise;\n    }\n    /**\n     * IMPORTANT:\n     * 1. Never throw in this method, as it is called from the constructor\n     * 2. Never return a session from this method as it would be cached over\n     *    the whole lifetime of the client\n     */\n    async _initialize() {\n        var _a;\n        try {\n            const params = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.parseParametersFromURL)(window.location.href);\n            let callbackUrlType = 'none';\n            if (this._isImplicitGrantCallback(params)) {\n                callbackUrlType = 'implicit';\n            }\n            else if (await this._isPKCECallback(params)) {\n                callbackUrlType = 'pkce';\n            }\n            /**\n             * Attempt to get the session from the URL only if these conditions are fulfilled\n             *\n             * Note: If the URL isn't one of the callback url types (implicit or pkce),\n             * then there could be an existing session so we don't want to prematurely remove it\n             */\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n                const { data, error } = await this._getSessionFromURL(params, callbackUrlType);\n                if (error) {\n                    this._debug('#_initialize()', 'error detecting session from URL', error);\n                    if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthImplicitGrantRedirectError)(error)) {\n                        const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n                        if (errorCode === 'identity_already_exists' ||\n                            errorCode === 'identity_not_found' ||\n                            errorCode === 'single_identity_not_deletable') {\n                            return { error };\n                        }\n                    }\n                    // failed login attempt via url,\n                    // remove old session as in verifyOtp, signUp and signInWith*\n                    await this._removeSession();\n                    return { error };\n                }\n                const { session, redirectType } = data;\n                this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n                await this._saveSession(session);\n                setTimeout(async () => {\n                    if (redirectType === 'recovery') {\n                        await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n                    }\n                    else {\n                        await this._notifyAllSubscribers('SIGNED_IN', session);\n                    }\n                }, 0);\n                return { error: null };\n            }\n            // no login attempt via callback url try to recover session from storage\n            await this._recoverAndRefresh();\n            return { error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { error };\n            }\n            return {\n                error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError('Unexpected error during initialization', error),\n            };\n        }\n        finally {\n            await this._handleVisibilityChange();\n            this._debug('#_initialize()', 'end');\n        }\n    }\n    /**\n     * Creates a new anonymous user.\n     *\n     * @returns A session where the is_anonymous claim in the access token JWT set to true\n     */\n    async signInAnonymously(credentials) {\n        var _a, _b, _c;\n        try {\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                headers: this.headers,\n                body: {\n                    data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n                    gotrue_meta_security: { captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Creates a new user.\n     *\n     * Be aware that if a user account exists in the system you may get back an\n     * error message that attempts to hide this information from the user.\n     * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n     *\n     * @returns A logged-in session if the server has \"autoconfirm\" ON\n     * @returns A user if the server has \"autoconfirm\" OFF\n     */\n    async signUp(credentials) {\n        var _a, _b, _c;\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: {\n                        email,\n                        password,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n                        channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user with an email and password or phone and password.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or that the\n     * email/phone and password combination is wrong or that the account can only\n     * be accessed via social login.\n     */\n    async signInWithPassword(credentials) {\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return { data: { user: null, session: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError() };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return {\n                data: Object.assign({ user: data.user, session: data.session }, (data.weak_password ? { weakPassword: data.weak_password } : null)),\n                error,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user via a third-party provider.\n     * This method supports the PKCE flow.\n     */\n    async signInWithOAuth(credentials) {\n        var _a, _b, _c, _d;\n        return await this._handleProviderSignIn(credentials.provider, {\n            redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n            scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n            queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n            skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect,\n        });\n    }\n    /**\n     * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n     */\n    async exchangeCodeForSession(authCode) {\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return this._exchangeCodeForSession(authCode);\n        });\n    }\n    /**\n     * Signs in a user by verifying a message signed by the user's private key.\n     * Only Solana supported at this time, using the Sign in with Solana standard.\n     */\n    async signInWithWeb3(credentials) {\n        const { chain } = credentials;\n        if (chain === 'solana') {\n            return await this.signInWithSolana(credentials);\n        }\n        throw new Error(`@supabase/auth-js: Unsupported chain \"${chain}\"`);\n    }\n    async signInWithSolana(credentials) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n        let message;\n        let signature;\n        if ('message' in credentials) {\n            message = credentials.message;\n            signature = credentials.signature;\n        }\n        else {\n            const { chain, wallet, statement, options } = credentials;\n            let resolvedWallet;\n            if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)()) {\n                if (typeof wallet !== 'object' || !(options === null || options === void 0 ? void 0 : options.url)) {\n                    throw new Error('@supabase/auth-js: Both wallet and url must be specified in non-browser environments.');\n                }\n                resolvedWallet = wallet;\n            }\n            else if (typeof wallet === 'object') {\n                resolvedWallet = wallet;\n            }\n            else {\n                const windowAny = window;\n                if ('solana' in windowAny &&\n                    typeof windowAny.solana === 'object' &&\n                    (('signIn' in windowAny.solana && typeof windowAny.solana.signIn === 'function') ||\n                        ('signMessage' in windowAny.solana &&\n                            typeof windowAny.solana.signMessage === 'function'))) {\n                    resolvedWallet = windowAny.solana;\n                }\n                else {\n                    throw new Error(`@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.`);\n                }\n            }\n            const url = new URL((_a = options === null || options === void 0 ? void 0 : options.url) !== null && _a !== void 0 ? _a : window.location.href);\n            if ('signIn' in resolvedWallet && resolvedWallet.signIn) {\n                const output = await resolvedWallet.signIn(Object.assign(Object.assign(Object.assign({ issuedAt: new Date().toISOString() }, options === null || options === void 0 ? void 0 : options.signInWithSolana), { \n                    // non-overridable properties\n                    version: '1', domain: url.host, uri: url.href }), (statement ? { statement } : null)));\n                let outputToProcess;\n                if (Array.isArray(output) && output[0] && typeof output[0] === 'object') {\n                    outputToProcess = output[0];\n                }\n                else if (output &&\n                    typeof output === 'object' &&\n                    'signedMessage' in output &&\n                    'signature' in output) {\n                    outputToProcess = output;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() returned unrecognized value');\n                }\n                if ('signedMessage' in outputToProcess &&\n                    'signature' in outputToProcess &&\n                    (typeof outputToProcess.signedMessage === 'string' ||\n                        outputToProcess.signedMessage instanceof Uint8Array) &&\n                    outputToProcess.signature instanceof Uint8Array) {\n                    message =\n                        typeof outputToProcess.signedMessage === 'string'\n                            ? outputToProcess.signedMessage\n                            : new TextDecoder().decode(outputToProcess.signedMessage);\n                    signature = outputToProcess.signature;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields');\n                }\n            }\n            else {\n                if (!('signMessage' in resolvedWallet) ||\n                    typeof resolvedWallet.signMessage !== 'function' ||\n                    !('publicKey' in resolvedWallet) ||\n                    typeof resolvedWallet !== 'object' ||\n                    !resolvedWallet.publicKey ||\n                    !('toBase58' in resolvedWallet.publicKey) ||\n                    typeof resolvedWallet.publicKey.toBase58 !== 'function') {\n                    throw new Error('@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API');\n                }\n                message = [\n                    `${url.host} wants you to sign in with your Solana account:`,\n                    resolvedWallet.publicKey.toBase58(),\n                    ...(statement ? ['', statement, ''] : ['']),\n                    'Version: 1',\n                    `URI: ${url.href}`,\n                    `Issued At: ${(_c = (_b = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _b === void 0 ? void 0 : _b.issuedAt) !== null && _c !== void 0 ? _c : new Date().toISOString()}`,\n                    ...(((_d = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _d === void 0 ? void 0 : _d.notBefore)\n                        ? [`Not Before: ${options.signInWithSolana.notBefore}`]\n                        : []),\n                    ...(((_e = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _e === void 0 ? void 0 : _e.expirationTime)\n                        ? [`Expiration Time: ${options.signInWithSolana.expirationTime}`]\n                        : []),\n                    ...(((_f = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _f === void 0 ? void 0 : _f.chainId)\n                        ? [`Chain ID: ${options.signInWithSolana.chainId}`]\n                        : []),\n                    ...(((_g = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _g === void 0 ? void 0 : _g.nonce) ? [`Nonce: ${options.signInWithSolana.nonce}`] : []),\n                    ...(((_h = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _h === void 0 ? void 0 : _h.requestId)\n                        ? [`Request ID: ${options.signInWithSolana.requestId}`]\n                        : []),\n                    ...(((_k = (_j = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _j === void 0 ? void 0 : _j.resources) === null || _k === void 0 ? void 0 : _k.length)\n                        ? [\n                            'Resources',\n                            ...options.signInWithSolana.resources.map((resource) => `- ${resource}`),\n                        ]\n                        : []),\n                ].join('\\n');\n                const maybeSignature = await resolvedWallet.signMessage(new TextEncoder().encode(message), 'utf8');\n                if (!maybeSignature || !(maybeSignature instanceof Uint8Array)) {\n                    throw new Error('@supabase/auth-js: Wallet signMessage() API returned an recognized value');\n                }\n                signature = maybeSignature;\n            }\n        }\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=web3`, {\n                headers: this.headers,\n                body: Object.assign({ chain: 'solana', message, signature: (0,_lib_base64url__WEBPACK_IMPORTED_MODULE_9__.bytesToBase64URL)(signature) }, (((_l = credentials.options) === null || _l === void 0 ? void 0 : _l.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: (_m = credentials.options) === null || _m === void 0 ? void 0 : _m.captchaToken } }\n                    : null)),\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign({}, data), error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _exchangeCodeForSession(authCode) {\n        const storageItem = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n                headers: this.headers,\n                body: {\n                    auth_code: authCode,\n                    code_verifier: codeVerifier,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null, redirectType: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign(Object.assign({}, data), { redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null }), error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Allows signing in with an OIDC ID token. The authentication provider used\n     * should be enabled and configured.\n     */\n    async signInWithIdToken(credentials) {\n        try {\n            const { options, provider, token, access_token, nonce } = credentials;\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n                headers: this.headers,\n                body: {\n                    provider,\n                    id_token: token,\n                    access_token,\n                    nonce,\n                    gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user using magiclink or a one-time password (OTP).\n     *\n     * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n     * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n     * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or, that the account\n     * can only be accessed via social login.\n     *\n     * Do note that you will need to configure a Whatsapp sender on Twilio\n     * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n     * channel is not supported on other providers\n     * at this time.\n     * This method supports PKCE when an email is passed.\n     */\n    async signInWithOtp(credentials) {\n        var _a, _b, _c, _d, _e;\n        try {\n            if ('email' in credentials) {\n                const { email, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            if ('phone' in credentials) {\n                const { phone, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n                        create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms',\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number.');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n     */\n    async verifyOtp(params) {\n        var _a, _b;\n        try {\n            let redirectTo = undefined;\n            let captchaToken = undefined;\n            if ('options' in params) {\n                redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n                captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n            }\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/verify`, {\n                headers: this.headers,\n                body: Object.assign(Object.assign({}, params), { gotrue_meta_security: { captcha_token: captchaToken } }),\n                redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data) {\n                throw new Error('An error occurred on token verification.');\n            }\n            const session = data.session;\n            const user = data.user;\n            if (session === null || session === void 0 ? void 0 : session.access_token) {\n                await this._saveSession(session);\n                await this._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Attempts a single-sign on using an enterprise Identity Provider. A\n     * successful SSO attempt will redirect the current page to the identity\n     * provider authorization page. The redirect URL is implementation and SSO\n     * protocol specific.\n     *\n     * You can use it by providing a SSO domain. Typically you can extract this\n     * domain by asking users for their email address. If this domain is\n     * registered on the Auth instance the redirect will use that organization's\n     * currently active SSO Identity Provider for the login.\n     *\n     * If you have built an organization-specific login page, you can use the\n     * organization's SSO Identity Provider UUID directly instead.\n     */\n    async signInWithSSO(params) {\n        var _a, _b, _c;\n        try {\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (this.flowType === 'pkce') {\n                ;\n                [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/sso`, {\n                body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, ('providerId' in params ? { provider_id: params.providerId } : null)), ('domain' in params ? { domain: params.domain } : null)), { redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined }), (((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n                    : null)), { skip_http_redirect: true, code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._ssoResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends a reauthentication OTP to the user's email or phone number.\n     * Requires the user to be signed-in.\n     */\n    async reauthenticate() {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._reauthenticate();\n        });\n    }\n    async _reauthenticate() {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError)\n                    throw sessionError;\n                if (!session)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n                    headers: this.headers,\n                    jwt: session.access_token,\n                });\n                return { data: { user: null, session: null }, error };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n     */\n    async resend(credentials) {\n        try {\n            const endpoint = `${this.url}/resend`;\n            if ('email' in credentials) {\n                const { email, type, options } = credentials;\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            else if ('phone' in credentials) {\n                const { phone, type, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns the session, refreshing it if necessary.\n     *\n     * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n     *\n     * **IMPORTANT:** This method loads values directly from the storage attached\n     * to the client. If that storage is based on request cookies for example,\n     * the values in it may not be authentic and therefore it's strongly advised\n     * against using this method and its results in such circumstances. A warning\n     * will be emitted if this is detected. Use {@link #getUser()} instead.\n     */\n    async getSession() {\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return this._useSession(async (result) => {\n                return result;\n            });\n        });\n        return result;\n    }\n    /**\n     * Acquires a global lock based on the storage key.\n     */\n    async _acquireLock(acquireTimeout, fn) {\n        this._debug('#_acquireLock', 'begin', acquireTimeout);\n        try {\n            if (this.lockAcquired) {\n                const last = this.pendingInLock.length\n                    ? this.pendingInLock[this.pendingInLock.length - 1]\n                    : Promise.resolve();\n                const result = (async () => {\n                    await last;\n                    return await fn();\n                })();\n                this.pendingInLock.push((async () => {\n                    try {\n                        await result;\n                    }\n                    catch (e) {\n                        // we just care if it finished\n                    }\n                })());\n                return result;\n            }\n            return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n                this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n                try {\n                    this.lockAcquired = true;\n                    const result = fn();\n                    this.pendingInLock.push((async () => {\n                        try {\n                            await result;\n                        }\n                        catch (e) {\n                            // we just care if it finished\n                        }\n                    })());\n                    await result;\n                    // keep draining the queue until there's nothing to wait on\n                    while (this.pendingInLock.length) {\n                        const waitOn = [...this.pendingInLock];\n                        await Promise.all(waitOn);\n                        this.pendingInLock.splice(0, waitOn.length);\n                    }\n                    return await result;\n                }\n                finally {\n                    this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n                    this.lockAcquired = false;\n                }\n            });\n        }\n        finally {\n            this._debug('#_acquireLock', 'end');\n        }\n    }\n    /**\n     * Use instead of {@link #getSession} inside the library. It is\n     * semantically usually what you want, as getting a session involves some\n     * processing afterwards that requires only one client operating on the\n     * session at once across multiple tabs or processes.\n     */\n    async _useSession(fn) {\n        this._debug('#_useSession', 'begin');\n        try {\n            // the use of __loadSession here is the only correct use of the function!\n            const result = await this.__loadSession();\n            return await fn(result);\n        }\n        finally {\n            this._debug('#_useSession', 'end');\n        }\n    }\n    /**\n     * NEVER USE DIRECTLY!\n     *\n     * Always use {@link #_useSession}.\n     */\n    async __loadSession() {\n        this._debug('#__loadSession()', 'begin');\n        if (!this.lockAcquired) {\n            this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n        }\n        try {\n            let currentSession = null;\n            const maybeSession = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey);\n            this._debug('#getSession()', 'session from storage', maybeSession);\n            if (maybeSession !== null) {\n                if (this._isValidSession(maybeSession)) {\n                    currentSession = maybeSession;\n                }\n                else {\n                    this._debug('#getSession()', 'session from storage is not valid');\n                    await this._removeSession();\n                }\n            }\n            if (!currentSession) {\n                return { data: { session: null }, error: null };\n            }\n            // A session is considered expired before the access token _actually_\n            // expires. When the autoRefreshToken option is off (or when the tab is\n            // in the background), very eager users of getSession() -- like\n            // realtime-js -- might send a valid JWT which will expire by the time it\n            // reaches the server.\n            const hasExpired = currentSession.expires_at\n                ? currentSession.expires_at * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS\n                : false;\n            this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n            if (!hasExpired) {\n                if (this.userStorage) {\n                    const maybeUser = (await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.userStorage, this.storageKey + '-user'));\n                    if (maybeUser === null || maybeUser === void 0 ? void 0 : maybeUser.user) {\n                        currentSession.user = maybeUser.user;\n                    }\n                    else {\n                        currentSession.user = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.userNotAvailableProxy)();\n                    }\n                }\n                if (this.storage.isServer && currentSession.user) {\n                    let suppressWarning = this.suppressGetSessionWarning;\n                    const proxySession = new Proxy(currentSession, {\n                        get: (target, prop, receiver) => {\n                            if (!suppressWarning && prop === 'user') {\n                                // only show warning when the user object is being accessed from the server\n                                console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                                suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                                this.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n                            }\n                            return Reflect.get(target, prop, receiver);\n                        },\n                    });\n                    currentSession = proxySession;\n                }\n                return { data: { session: currentSession }, error: null };\n            }\n            const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n                return { data: { session: null }, error };\n            }\n            return { data: { session }, error: null };\n        }\n        finally {\n            this._debug('#__loadSession()', 'end');\n        }\n    }\n    /**\n     * Gets the current user details if there is an existing session. This method\n     * performs a network request to the Supabase Auth server, so the returned\n     * value is authentic and can be used to base authorization rules on.\n     *\n     * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n     */\n    async getUser(jwt) {\n        if (jwt) {\n            return await this._getUser(jwt);\n        }\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return await this._getUser();\n        });\n        return result;\n    }\n    async _getUser(jwt) {\n        try {\n            if (jwt) {\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: jwt,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            }\n            return await this._useSession(async (result) => {\n                var _a, _b, _c;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                // returns an error if there is no access_token or custom authorization header\n                if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !this.hasCustomAuthorizationHeader) {\n                    return { data: { user: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError() };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthSessionMissingError)(error)) {\n                    // JWT contains a `session_id` which does not correspond to an active\n                    // session in the database, indicating the user is signed out.\n                    await this._removeSession();\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n                }\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates user data for a logged in user.\n     */\n    async updateUser(attributes, options = {}) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._updateUser(attributes, options);\n        });\n    }\n    async _updateUser(attributes, options = {}) {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    throw sessionError;\n                }\n                if (!sessionData.session) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const session = sessionData.session;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce' && attributes.email != null) {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { data, error: userError } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'PUT', `${this.url}/user`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: Object.assign(Object.assign({}, attributes), { code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                    jwt: session.access_token,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n                if (userError)\n                    throw userError;\n                session.user = data.user;\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('USER_UPDATED', session);\n                return { data: { user: session.user }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n     * If the refresh token or access token in the current session is invalid, an error will be thrown.\n     * @param currentSession The current session that minimally contains an access token and refresh token.\n     */\n    async setSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._setSession(currentSession);\n        });\n    }\n    async _setSession(currentSession) {\n        try {\n            if (!currentSession.access_token || !currentSession.refresh_token) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            }\n            const timeNow = Date.now() / 1000;\n            let expiresAt = timeNow;\n            let hasExpired = true;\n            let session = null;\n            const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(currentSession.access_token);\n            if (payload.exp) {\n                expiresAt = payload.exp;\n                hasExpired = expiresAt <= timeNow;\n            }\n            if (hasExpired) {\n                const { session: refreshedSession, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!refreshedSession) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                session = refreshedSession;\n            }\n            else {\n                const { data, error } = await this._getUser(currentSession.access_token);\n                if (error) {\n                    throw error;\n                }\n                session = {\n                    access_token: currentSession.access_token,\n                    refresh_token: currentSession.refresh_token,\n                    user: data.user,\n                    token_type: 'bearer',\n                    expires_in: expiresAt - timeNow,\n                    expires_at: expiresAt,\n                };\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user: session.user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns a new session, regardless of expiry status.\n     * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n     * If the current session's refresh token is invalid, an error will be thrown.\n     * @param currentSession The current session. If passed in, it must contain a refresh token.\n     */\n    async refreshSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._refreshSession(currentSession);\n        });\n    }\n    async _refreshSession(currentSession) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                if (!currentSession) {\n                    const { data, error } = result;\n                    if (error) {\n                        throw error;\n                    }\n                    currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n                }\n                if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!session) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                return { data: { user: session.user, session }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets the session data from a URL string\n     */\n    async _getSessionFromURL(params, callbackUrlType) {\n        try {\n            if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)())\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No browser detected.');\n            // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n            if (params.error || params.error_description || params.error_code) {\n                // The error class returned implies that the redirect is from an implicit grant flow\n                // but it could also be from a redirect error from a PKCE flow.\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n                    error: params.error || 'unspecified_error',\n                    code: params.error_code || 'unspecified_code',\n                });\n            }\n            // Checks for mismatches between the flowType initialised in the client and the URL parameters\n            switch (callbackUrlType) {\n                case 'implicit':\n                    if (this.flowType === 'pkce') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n                    }\n                    break;\n                case 'pkce':\n                    if (this.flowType === 'implicit') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n                    }\n                    break;\n                default:\n                // there's no mismatch so we continue\n            }\n            // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n            if (callbackUrlType === 'pkce') {\n                this._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n                if (!params.code)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('No code detected.');\n                const { data, error } = await this._exchangeCodeForSession(params.code);\n                if (error)\n                    throw error;\n                const url = new URL(window.location.href);\n                url.searchParams.delete('code');\n                window.history.replaceState(window.history.state, '', url.toString());\n                return { data: { session: data.session, redirectType: null }, error: null };\n            }\n            const { provider_token, provider_refresh_token, access_token, refresh_token, expires_in, expires_at, token_type, } = params;\n            if (!access_token || !expires_in || !refresh_token || !token_type) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No session defined in URL');\n            }\n            const timeNow = Math.round(Date.now() / 1000);\n            const expiresIn = parseInt(expires_in);\n            let expiresAt = timeNow + expiresIn;\n            if (expires_at) {\n                expiresAt = parseInt(expires_at);\n            }\n            const actuallyExpiresIn = expiresAt - timeNow;\n            if (actuallyExpiresIn * 1000 <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS) {\n                console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n            }\n            const issuedAt = expiresAt - expiresIn;\n            if (timeNow - issuedAt >= 120) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n            }\n            else if (timeNow - issuedAt < 0) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n            }\n            const { data, error } = await this._getUser(access_token);\n            if (error)\n                throw error;\n            const session = {\n                provider_token,\n                provider_refresh_token,\n                access_token,\n                expires_in: expiresIn,\n                expires_at: expiresAt,\n                refresh_token,\n                token_type,\n                user: data.user,\n            };\n            // Remove tokens from URL\n            window.location.hash = '';\n            this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n            return { data: { session, redirectType: params.type }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n     */\n    _isImplicitGrantCallback(params) {\n        return Boolean(params.access_token || params.error_description);\n    }\n    /**\n     * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n     */\n    async _isPKCECallback(params) {\n        const currentStorageContent = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        return !!(params.code && currentStorageContent);\n    }\n    /**\n     * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n     *\n     * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n     * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n     *\n     * If using `others` scope, no `SIGNED_OUT` event is fired!\n     */\n    async signOut(options = { scope: 'global' }) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._signOut(options);\n        });\n    }\n    async _signOut({ scope } = { scope: 'global' }) {\n        return await this._useSession(async (result) => {\n            var _a;\n            const { data, error: sessionError } = result;\n            if (sessionError) {\n                return { error: sessionError };\n            }\n            const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n            if (accessToken) {\n                const { error } = await this.admin.signOut(accessToken, scope);\n                if (error) {\n                    // ignore 404s since user might not exist anymore\n                    // ignore 401s since an invalid or expired JWT should sign out the current session\n                    if (!((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthApiError)(error) &&\n                        (error.status === 404 || error.status === 401 || error.status === 403))) {\n                        return { error };\n                    }\n                }\n            }\n            if (scope !== 'others') {\n                await this._removeSession();\n                await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            }\n            return { error: null };\n        });\n    }\n    /**\n     * Receive a notification every time an auth event happens.\n     * @param callback A callback function to be invoked when an auth event happens.\n     */\n    onAuthStateChange(callback) {\n        const id = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.uuid)();\n        const subscription = {\n            id,\n            callback,\n            unsubscribe: () => {\n                this._debug('#unsubscribe()', 'state change callback with id removed', id);\n                this.stateChangeEmitters.delete(id);\n            },\n        };\n        this._debug('#onAuthStateChange()', 'registered callback with id', id);\n        this.stateChangeEmitters.set(id, subscription);\n        (async () => {\n            await this.initializePromise;\n            await this._acquireLock(-1, async () => {\n                this._emitInitialSession(id);\n            });\n        })();\n        return { data: { subscription } };\n    }\n    async _emitInitialSession(id) {\n        return await this._useSession(async (result) => {\n            var _a, _b;\n            try {\n                const { data: { session }, error, } = result;\n                if (error)\n                    throw error;\n                await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n            }\n            catch (err) {\n                await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n                console.error(err);\n            }\n        });\n    }\n    /**\n     * Sends a password reset request to an email address. This method supports the PKCE flow.\n     *\n     * @param email The email address of the user.\n     * @param options.redirectTo The URL to send the user to after they click the password reset link.\n     * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n     */\n    async resetPasswordForEmail(email, options = {}) {\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n            ;\n            [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey, true // isPasswordRecovery\n            );\n        }\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/recover`, {\n                body: {\n                    email,\n                    code_challenge: codeChallenge,\n                    code_challenge_method: codeChallengeMethod,\n                    gotrue_meta_security: { captcha_token: options.captchaToken },\n                },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets all the identities linked to a user.\n     */\n    async getUserIdentities() {\n        var _a;\n        try {\n            const { data, error } = await this.getUser();\n            if (error)\n                throw error;\n            return { data: { identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : [] }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Links an oauth identity to an existing user.\n     * This method supports the PKCE flow.\n     */\n    async linkIdentity(credentials) {\n        var _a;\n        try {\n            const { data, error } = await this._useSession(async (result) => {\n                var _a, _b, _c, _d, _e;\n                const { data, error } = result;\n                if (error)\n                    throw error;\n                const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n                    redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n                    scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n                    queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n                    skipBrowserRedirect: true,\n                });\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', url, {\n                    headers: this.headers,\n                    jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined,\n                });\n            });\n            if (error)\n                throw error;\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n                window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n            }\n            return { data: { provider: credentials.provider, url: data === null || data === void 0 ? void 0 : data.url }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { provider: credentials.provider, url: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n     */\n    async unlinkIdentity(identity) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n                    headers: this.headers,\n                    jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates a new JWT.\n     * @param refreshToken A valid refresh token that was returned on login.\n     */\n    async _refreshAccessToken(refreshToken) {\n        const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            const startedAt = Date.now();\n            // will attempt to refresh the token with exponential backoff\n            return await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.retryable)(async (attempt) => {\n                if (attempt > 0) {\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.sleep)(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n                }\n                this._debug(debugName, 'refreshing attempt', attempt);\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n                    body: { refresh_token: refreshToken },\n                    headers: this.headers,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }, (attempt, error) => {\n                const nextBackOffInterval = 200 * Math.pow(2, attempt);\n                return (error &&\n                    (0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error) &&\n                    // retryable only if the request can be sent before the backoff overflows the tick duration\n                    Date.now() + nextBackOffInterval - startedAt < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n            });\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    _isValidSession(maybeSession) {\n        const isValidSession = typeof maybeSession === 'object' &&\n            maybeSession !== null &&\n            'access_token' in maybeSession &&\n            'refresh_token' in maybeSession &&\n            'expires_at' in maybeSession;\n        return isValidSession;\n    }\n    async _handleProviderSignIn(provider, options) {\n        const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n            redirectTo: options.redirectTo,\n            scopes: options.scopes,\n            queryParams: options.queryParams,\n        });\n        this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n        // try to open on the browser\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !options.skipBrowserRedirect) {\n            window.location.assign(url);\n        }\n        return { data: { provider, url }, error: null };\n    }\n    /**\n     * Recovers the session from LocalStorage and refreshes the token\n     * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n     */\n    async _recoverAndRefresh() {\n        var _a, _b;\n        const debugName = '#_recoverAndRefresh()';\n        this._debug(debugName, 'begin');\n        try {\n            const currentSession = (await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey));\n            if (currentSession && this.userStorage) {\n                let maybeUser = (await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.userStorage, this.storageKey + '-user'));\n                if (!this.storage.isServer && Object.is(this.storage, this.userStorage) && !maybeUser) {\n                    // storage and userStorage are the same storage medium, for example\n                    // window.localStorage if userStorage does not have the user from\n                    // storage stored, store it first thereby migrating the user object\n                    // from storage -> userStorage\n                    maybeUser = { user: currentSession.user };\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.userStorage, this.storageKey + '-user', maybeUser);\n                }\n                currentSession.user = (_a = maybeUser === null || maybeUser === void 0 ? void 0 : maybeUser.user) !== null && _a !== void 0 ? _a : (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.userNotAvailableProxy)();\n            }\n            else if (currentSession && !currentSession.user) {\n                // user storage is not set, let's check if it was previously enabled so\n                // we bring back the storage as it should be\n                if (!currentSession.user) {\n                    // test if userStorage was previously enabled and the storage medium was the same, to move the user back under the same key\n                    const separateUser = (await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey + '-user'));\n                    if (separateUser && (separateUser === null || separateUser === void 0 ? void 0 : separateUser.user)) {\n                        currentSession.user = separateUser.user;\n                        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, this.storageKey + '-user');\n                        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.storage, this.storageKey, currentSession);\n                    }\n                    else {\n                        currentSession.user = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.userNotAvailableProxy)();\n                    }\n                }\n            }\n            this._debug(debugName, 'session from storage', currentSession);\n            if (!this._isValidSession(currentSession)) {\n                this._debug(debugName, 'session is not valid');\n                if (currentSession !== null) {\n                    await this._removeSession();\n                }\n                return;\n            }\n            const expiresWithMargin = ((_b = currentSession.expires_at) !== null && _b !== void 0 ? _b : Infinity) * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS;\n            this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS}s`);\n            if (expiresWithMargin) {\n                if (this.autoRefreshToken && currentSession.refresh_token) {\n                    const { error } = await this._callRefreshToken(currentSession.refresh_token);\n                    if (error) {\n                        console.error(error);\n                        if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                            this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                            await this._removeSession();\n                        }\n                    }\n                }\n            }\n            else if (currentSession.user &&\n                currentSession.user.__isUserNotAvailableProxy === true) {\n                // If we have a proxy user, try to get the real user data\n                try {\n                    const { data, error: userError } = await this._getUser(currentSession.access_token);\n                    if (!userError && (data === null || data === void 0 ? void 0 : data.user)) {\n                        currentSession.user = data.user;\n                        await this._saveSession(currentSession);\n                        await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n                    }\n                    else {\n                        this._debug(debugName, 'could not get user data, skipping SIGNED_IN notification');\n                    }\n                }\n                catch (getUserError) {\n                    console.error('Error getting user data:', getUserError);\n                    this._debug(debugName, 'error getting user data, skipping SIGNED_IN notification', getUserError);\n                }\n            }\n            else {\n                // no need to persist currentSession again, as we just loaded it from\n                // local storage; persisting it again may overwrite a value saved by\n                // another client with access to the same local storage\n                await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n            }\n        }\n        catch (err) {\n            this._debug(debugName, 'error', err);\n            console.error(err);\n            return;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    async _callRefreshToken(refreshToken) {\n        var _a, _b;\n        if (!refreshToken) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n        }\n        // refreshing is already in progress\n        if (this.refreshingDeferred) {\n            return this.refreshingDeferred.promise;\n        }\n        const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            this.refreshingDeferred = new _lib_helpers__WEBPACK_IMPORTED_MODULE_4__.Deferred();\n            const { data, error } = await this._refreshAccessToken(refreshToken);\n            if (error)\n                throw error;\n            if (!data.session)\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            await this._saveSession(data.session);\n            await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n            const result = { session: data.session, error: null };\n            this.refreshingDeferred.resolve(result);\n            return result;\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                const result = { session: null, error };\n                if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                    await this._removeSession();\n                }\n                (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n                return result;\n            }\n            (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n            throw error;\n        }\n        finally {\n            this.refreshingDeferred = null;\n            this._debug(debugName, 'end');\n        }\n    }\n    async _notifyAllSubscribers(event, session, broadcast = true) {\n        const debugName = `#_notifyAllSubscribers(${event})`;\n        this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n        try {\n            if (this.broadcastChannel && broadcast) {\n                this.broadcastChannel.postMessage({ event, session });\n            }\n            const errors = [];\n            const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n                try {\n                    await x.callback(event, session);\n                }\n                catch (e) {\n                    errors.push(e);\n                }\n            });\n            await Promise.all(promises);\n            if (errors.length > 0) {\n                for (let i = 0; i < errors.length; i += 1) {\n                    console.error(errors[i]);\n                }\n                throw errors[0];\n            }\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    /**\n     * set currentSession and currentUser\n     * process to _startAutoRefreshToken if possible\n     */\n    async _saveSession(session) {\n        this._debug('#_saveSession()', session);\n        // _saveSession is always called whenever a new session has been acquired\n        // so we can safely suppress the warning returned by future getSession calls\n        this.suppressGetSessionWarning = true;\n        // Create a shallow copy to work with, to avoid mutating the original session object if it's used elsewhere\n        const sessionToProcess = Object.assign({}, session);\n        const userIsProxy = sessionToProcess.user && sessionToProcess.user.__isUserNotAvailableProxy === true;\n        if (this.userStorage) {\n            if (!userIsProxy && sessionToProcess.user) {\n                // If it's a real user object, save it to userStorage.\n                await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.userStorage, this.storageKey + '-user', {\n                    user: sessionToProcess.user,\n                });\n            }\n            else if (userIsProxy) {\n                // If it's the proxy, it means user was not found in userStorage.\n                // We should ensure no stale user data for this key exists in userStorage if we were to save null,\n                // or simply not save the proxy. For now, we don't save the proxy here.\n                // If there's a need to clear userStorage if user becomes proxy, that logic would go here.\n            }\n            // Prepare the main session data for primary storage: remove the user property before cloning\n            // This is important because the original session.user might be the proxy\n            const mainSessionData = Object.assign({}, sessionToProcess);\n            delete mainSessionData.user; // Remove user (real or proxy) before cloning for main storage\n            const clonedMainSessionData = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.deepClone)(mainSessionData);\n            await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.storage, this.storageKey, clonedMainSessionData);\n        }\n        else {\n            // No userStorage is configured.\n            // In this case, session.user should ideally not be a proxy.\n            // If it were, structuredClone would fail. This implies an issue elsewhere if user is a proxy here\n            const clonedSession = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.deepClone)(sessionToProcess); // sessionToProcess still has its original user property\n            await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.storage, this.storageKey, clonedSession);\n        }\n    }\n    async _removeSession() {\n        this._debug('#_removeSession()');\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, this.storageKey);\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, this.storageKey + '-code-verifier');\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, this.storageKey + '-user');\n        if (this.userStorage) {\n            await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.userStorage, this.storageKey + '-user');\n        }\n        await this._notifyAllSubscribers('SIGNED_OUT', null);\n    }\n    /**\n     * Removes any registered visibilitychange callback.\n     *\n     * {@see #startAutoRefresh}\n     * {@see #stopAutoRefresh}\n     */\n    _removeVisibilityChangedCallback() {\n        this._debug('#_removeVisibilityChangedCallback()');\n        const callback = this.visibilityChangedCallback;\n        this.visibilityChangedCallback = null;\n        try {\n            if (callback && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n                window.removeEventListener('visibilitychange', callback);\n            }\n        }\n        catch (e) {\n            console.error('removing visibilitychange callback failed', e);\n        }\n    }\n    /**\n     * This is the private implementation of {@link #startAutoRefresh}. Use this\n     * within the library.\n     */\n    async _startAutoRefresh() {\n        await this._stopAutoRefresh();\n        this._debug('#_startAutoRefresh()');\n        const ticker = setInterval(() => this._autoRefreshTokenTick(), _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n        this.autoRefreshTicker = ticker;\n        if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n            // ticker is a NodeJS Timeout object that has an `unref` method\n            // https://nodejs.org/api/timers.html#timeoutunref\n            // When auto refresh is used in NodeJS (like for testing) the\n            // `setInterval` is preventing the process from being marked as\n            // finished and tests run endlessly. This can be prevented by calling\n            // `unref()` on the returned object.\n            ticker.unref();\n            // @ts-expect-error TS has no context of Deno\n        }\n        else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n            // similar like for NodeJS, but with the Deno API\n            // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n            // @ts-expect-error TS has no context of Deno\n            Deno.unrefTimer(ticker);\n        }\n        // run the tick immediately, but in the next pass of the event loop so that\n        // #_initialize can be allowed to complete without recursively waiting on\n        // itself\n        setTimeout(async () => {\n            await this.initializePromise;\n            await this._autoRefreshTokenTick();\n        }, 0);\n    }\n    /**\n     * This is the private implementation of {@link #stopAutoRefresh}. Use this\n     * within the library.\n     */\n    async _stopAutoRefresh() {\n        this._debug('#_stopAutoRefresh()');\n        const ticker = this.autoRefreshTicker;\n        this.autoRefreshTicker = null;\n        if (ticker) {\n            clearInterval(ticker);\n        }\n    }\n    /**\n     * Starts an auto-refresh process in the background. The session is checked\n     * every few seconds. Close to the time of expiration a process is started to\n     * refresh the session. If refreshing fails it will be retried for as long as\n     * necessary.\n     *\n     * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n     * to call this function, it will be called for you.\n     *\n     * On browsers the refresh process works only when the tab/window is in the\n     * foreground to conserve resources as well as prevent race conditions and\n     * flooding auth with requests. If you call this method any managed\n     * visibility change callback will be removed and you must manage visibility\n     * changes on your own.\n     *\n     * On non-browser platforms the refresh process works *continuously* in the\n     * background, which may not be desirable. You should hook into your\n     * platform's foreground indication mechanism and call these methods\n     * appropriately to conserve resources.\n     *\n     * {@see #stopAutoRefresh}\n     */\n    async startAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._startAutoRefresh();\n    }\n    /**\n     * Stops an active auto refresh process running in the background (if any).\n     *\n     * If you call this method any managed visibility change callback will be\n     * removed and you must manage visibility changes on your own.\n     *\n     * See {@link #startAutoRefresh} for more details.\n     */\n    async stopAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._stopAutoRefresh();\n    }\n    /**\n     * Runs the auto refresh token tick.\n     */\n    async _autoRefreshTokenTick() {\n        this._debug('#_autoRefreshTokenTick()', 'begin');\n        try {\n            await this._acquireLock(0, async () => {\n                try {\n                    const now = Date.now();\n                    try {\n                        return await this._useSession(async (result) => {\n                            const { data: { session }, } = result;\n                            if (!session || !session.refresh_token || !session.expires_at) {\n                                this._debug('#_autoRefreshTokenTick()', 'no session');\n                                return;\n                            }\n                            // session will expire in this many ticks (or has already expired if <= 0)\n                            const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n                            this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                            if (expiresInTicks <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD) {\n                                await this._callRefreshToken(session.refresh_token);\n                            }\n                        });\n                    }\n                    catch (e) {\n                        console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n                    }\n                }\n                finally {\n                    this._debug('#_autoRefreshTokenTick()', 'end');\n                }\n            });\n        }\n        catch (e) {\n            if (e.isAcquireTimeout || e instanceof _lib_locks__WEBPACK_IMPORTED_MODULE_8__.LockAcquireTimeoutError) {\n                this._debug('auto refresh token tick lock not available');\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    /**\n     * Registers callbacks on the browser / platform, which in-turn run\n     * algorithms when the browser window/tab are in foreground. On non-browser\n     * platforms it assumes always foreground.\n     */\n    async _handleVisibilityChange() {\n        this._debug('#_handleVisibilityChange()');\n        if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n            if (this.autoRefreshToken) {\n                // in non-browser environments the refresh token ticker runs always\n                this.startAutoRefresh();\n            }\n            return false;\n        }\n        try {\n            this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n            window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n            // now immediately call the visbility changed callback to setup with the\n            // current visbility state\n            await this._onVisibilityChanged(true); // initial call\n        }\n        catch (error) {\n            console.error('_handleVisibilityChange', error);\n        }\n    }\n    /**\n     * Callback registered with `window.addEventListener('visibilitychange')`.\n     */\n    async _onVisibilityChanged(calledFromInitialize) {\n        const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n        this._debug(methodName, 'visibilityState', document.visibilityState);\n        if (document.visibilityState === 'visible') {\n            if (this.autoRefreshToken) {\n                // in browser environments the refresh token ticker runs only on focused tabs\n                // which prevents race conditions\n                this._startAutoRefresh();\n            }\n            if (!calledFromInitialize) {\n                // called when the visibility has changed, i.e. the browser\n                // transitioned from hidden -> visible so we need to see if the session\n                // should be recovered immediately... but to do that we need to acquire\n                // the lock first asynchronously\n                await this.initializePromise;\n                await this._acquireLock(-1, async () => {\n                    if (document.visibilityState !== 'visible') {\n                        this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n                        // visibility has changed while waiting for the lock, abort\n                        return;\n                    }\n                    // recover the session\n                    await this._recoverAndRefresh();\n                });\n            }\n        }\n        else if (document.visibilityState === 'hidden') {\n            if (this.autoRefreshToken) {\n                this._stopAutoRefresh();\n            }\n        }\n    }\n    /**\n     * Generates the relevant login URL for a third-party provider.\n     * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n     * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n     * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n     */\n    async _getUrlForProvider(url, provider, options) {\n        const urlParams = [`provider=${encodeURIComponent(provider)}`];\n        if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n            urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n        }\n        if (options === null || options === void 0 ? void 0 : options.scopes) {\n            urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n        }\n        if (this.flowType === 'pkce') {\n            const [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            const flowParams = new URLSearchParams({\n                code_challenge: `${encodeURIComponent(codeChallenge)}`,\n                code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n            });\n            urlParams.push(flowParams.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.queryParams) {\n            const query = new URLSearchParams(options.queryParams);\n            urlParams.push(query.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n            urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n        }\n        return `${url}?${urlParams.join('&')}`;\n    }\n    async _unenroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _enroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                const body = Object.assign({ friendly_name: params.friendlyName, factor_type: params.factorType }, (params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }));\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors`, {\n                    body,\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n                if (error) {\n                    return { data: null, error };\n                }\n                if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n                    data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n                }\n                return { data, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * {@see GoTrueMFAApi#verify}\n     */\n    async _verify(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n                        body: { code: params.code, challenge_id: params.challengeId },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                    if (error) {\n                        return { data: null, error };\n                    }\n                    await this._saveSession(Object.assign({ expires_at: Math.round(Date.now() / 1000) + data.expires_in }, data));\n                    await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n                    return { data, error };\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challenge}\n     */\n    async _challenge(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n                        body: { channel: params.channel },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challengeAndVerify}\n     */\n    async _challengeAndVerify(params) {\n        // both _challenge and _verify independently acquire the lock, so no need\n        // to acquire it here\n        const { data: challengeData, error: challengeError } = await this._challenge({\n            factorId: params.factorId,\n        });\n        if (challengeError) {\n            return { data: null, error: challengeError };\n        }\n        return await this._verify({\n            factorId: params.factorId,\n            challengeId: challengeData.id,\n            code: params.code,\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#listFactors}\n     */\n    async _listFactors() {\n        // use #getUser instead of #_getUser as the former acquires a lock\n        const { data: { user }, error: userError, } = await this.getUser();\n        if (userError) {\n            return { data: null, error: userError };\n        }\n        const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n        const totp = factors.filter((factor) => factor.factor_type === 'totp' && factor.status === 'verified');\n        const phone = factors.filter((factor) => factor.factor_type === 'phone' && factor.status === 'verified');\n        return {\n            data: {\n                all: factors,\n                totp,\n                phone,\n            },\n            error: null,\n        };\n    }\n    /**\n     * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n     */\n    async _getAuthenticatorAssuranceLevel() {\n        return this._acquireLock(-1, async () => {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                if (!session) {\n                    return {\n                        data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n                        error: null,\n                    };\n                }\n                const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(session.access_token);\n                let currentLevel = null;\n                if (payload.aal) {\n                    currentLevel = payload.aal;\n                }\n                let nextLevel = currentLevel;\n                const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter((factor) => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n                if (verifiedFactors.length > 0) {\n                    nextLevel = 'aal2';\n                }\n                const currentAuthenticationMethods = payload.amr || [];\n                return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null };\n            });\n        });\n    }\n    async fetchJwk(kid, jwks = { keys: [] }) {\n        // try fetching from the supplied jwks\n        let jwk = jwks.keys.find((key) => key.kid === kid);\n        if (jwk) {\n            return jwk;\n        }\n        const now = Date.now();\n        // try fetching from cache\n        jwk = this.jwks.keys.find((key) => key.kid === kid);\n        // jwk exists and jwks isn't stale\n        if (jwk && this.jwks_cached_at + _lib_constants__WEBPACK_IMPORTED_MODULE_1__.JWKS_TTL > now) {\n            return jwk;\n        }\n        // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n        const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n            headers: this.headers,\n        });\n        if (error) {\n            throw error;\n        }\n        if (!data.keys || data.keys.length === 0) {\n            return null;\n        }\n        this.jwks = data;\n        this.jwks_cached_at = now;\n        // Find the signing key\n        jwk = data.keys.find((key) => key.kid === kid);\n        if (!jwk) {\n            return null;\n        }\n        return jwk;\n    }\n    /**\n     * Extracts the JWT claims present in the access token by first verifying the\n     * JWT against the server's JSON Web Key Set endpoint\n     * `/.well-known/jwks.json` which is often cached, resulting in significantly\n     * faster responses. Prefer this method over {@link #getUser} which always\n     * sends a request to the Auth server for each JWT.\n     *\n     * If the project is not using an asymmetric JWT signing key (like ECC or\n     * RSA) it always sends a request to the Auth server (similar to {@link\n     * #getUser}) to verify the JWT.\n     *\n     * @param jwt An optional specific JWT you wish to verify, not the one you\n     *            can obtain from {@link #getSession}.\n     * @param options Various additional options that allow you to customize the\n     *                behavior of this method.\n     */\n    async getClaims(jwt, options = {}) {\n        try {\n            let token = jwt;\n            if (!token) {\n                const { data, error } = await this.getSession();\n                if (error || !data.session) {\n                    return { data: null, error };\n                }\n                token = data.session.access_token;\n            }\n            const { header, payload, signature, raw: { header: rawHeader, payload: rawPayload }, } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(token);\n            if (!(options === null || options === void 0 ? void 0 : options.allowExpired)) {\n                // Reject expired JWTs should only happen if jwt argument was passed\n                (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.validateExp)(payload.exp);\n            }\n            const signingKey = !header.alg ||\n                header.alg.startsWith('HS') ||\n                !header.kid ||\n                !('crypto' in globalThis && 'subtle' in globalThis.crypto)\n                ? null\n                : await this.fetchJwk(header.kid, (options === null || options === void 0 ? void 0 : options.keys) ? { keys: options.keys } : options === null || options === void 0 ? void 0 : options.jwks);\n            // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n            if (!signingKey) {\n                const { error } = await this.getUser(token);\n                if (error) {\n                    throw error;\n                }\n                // getUser succeeds so the claims in the JWT can be trusted\n                return {\n                    data: {\n                        claims: payload,\n                        header,\n                        signature,\n                    },\n                    error: null,\n                };\n            }\n            const algorithm = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAlgorithm)(header.alg);\n            // Convert JWK to CryptoKey\n            const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n                'verify',\n            ]);\n            // Verify the signature\n            const isValid = await crypto.subtle.verify(algorithm, publicKey, signature, (0,_lib_base64url__WEBPACK_IMPORTED_MODULE_9__.stringToUint8Array)(`${rawHeader}.${rawPayload}`));\n            if (!isValid) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('Invalid JWT signature');\n            }\n            // If verification succeeds, decode and return claims\n            return {\n                data: {\n                    claims: payload,\n                    header,\n                    signature,\n                },\n                error: null,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\nGoTrueClient.nextInstanceID = 0;\n//# sourceMappingURL=GoTrueClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _AuthClient__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   AuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.CustomAuthError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _lib_types__WEBPACK_IMPORTED_MODULE_4__.SIGN_OUT_SCOPES),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.internals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.processLock)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n/* harmony import */ var _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\");\n/* harmony import */ var _AuthClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/AuthClient.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/types */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/types.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzEuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNKO0FBQ0E7QUFDSjtBQUM0QjtBQUN0QztBQUNDO0FBQzJGO0FBQ3hIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi43MS4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgR29UcnVlQWRtaW5BcGkgZnJvbSAnLi9Hb1RydWVBZG1pbkFwaSc7XG5pbXBvcnQgR29UcnVlQ2xpZW50IGZyb20gJy4vR29UcnVlQ2xpZW50JztcbmltcG9ydCBBdXRoQWRtaW5BcGkgZnJvbSAnLi9BdXRoQWRtaW5BcGknO1xuaW1wb3J0IEF1dGhDbGllbnQgZnJvbSAnLi9BdXRoQ2xpZW50JztcbmV4cG9ydCB7IEdvVHJ1ZUFkbWluQXBpLCBHb1RydWVDbGllbnQsIEF1dGhBZG1pbkFwaSwgQXV0aENsaWVudCB9O1xuZXhwb3J0ICogZnJvbSAnLi9saWIvdHlwZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZXJyb3JzJztcbmV4cG9ydCB7IG5hdmlnYXRvckxvY2ssIE5hdmlnYXRvckxvY2tBY3F1aXJlVGltZW91dEVycm9yLCBpbnRlcm5hbHMgYXMgbG9ja0ludGVybmFscywgcHJvY2Vzc0xvY2ssIH0gZnJvbSAnLi9saWIvbG9ja3MnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64UrlToUint8Array: () => (/* binding */ base64UrlToUint8Array),\n/* harmony export */   byteFromBase64URL: () => (/* binding */ byteFromBase64URL),\n/* harmony export */   byteToBase64URL: () => (/* binding */ byteToBase64URL),\n/* harmony export */   bytesToBase64URL: () => (/* binding */ bytesToBase64URL),\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8),\n/* harmony export */   stringToUint8Array: () => (/* binding */ stringToUint8Array)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('');\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('');\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nfunction byteToBase64URL(byte, state, emit) {\n    if (byte !== null) {\n        state.queue = (state.queue << 8) | byte;\n        state.queuedBits += 8;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n    else if (state.queuedBits > 0) {\n        state.queue = state.queue << (6 - state.queuedBits);\n        state.queuedBits = 6;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n}\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nfunction byteFromBase64URL(charCode, state, emit) {\n    const bits = FROM_BASE64URL[charCode];\n    if (bits > -1) {\n        // valid Base64-URL character\n        state.queue = (state.queue << 6) | bits;\n        state.queuedBits += 6;\n        while (state.queuedBits >= 8) {\n            emit((state.queue >> (state.queuedBits - 8)) & 0xff);\n            state.queuedBits -= 8;\n        }\n    }\n    else if (bits === -2) {\n        // ignore spaces, tabs, newlines, =\n        return;\n    }\n    else {\n        throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`);\n    }\n}\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    const emitter = (char) => {\n        base64.push(char);\n    };\n    const state = { queue: 0, queuedBits: 0 };\n    stringToUTF8(str, (byte) => {\n        byteToBase64URL(byte, state, emitter);\n    });\n    byteToBase64URL(null, state, emitter);\n    return base64.join('');\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const utf8Emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const utf8State = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    const b64State = { queue: 0, queuedBits: 0 };\n    const byteEmit = (byte) => {\n        stringFromUTF8(byte, utf8State, utf8Emit);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit);\n    }\n    return conv.join('');\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\nfunction base64UrlToUint8Array(str) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onByte = (byte) => {\n        result.push(byte);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), state, onByte);\n    }\n    return new Uint8Array(result);\n}\nfunction stringToUint8Array(str) {\n    const result = [];\n    stringToUTF8(str, (byte) => result.push(byte));\n    return new Uint8Array(result);\n}\nfunction bytesToBase64URL(bytes) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onChar = (char) => {\n        result.push(char);\n    };\n    bytes.forEach((byte) => byteToBase64URL(byte, state, onChar));\n    // always call with `null` after processing all bytes\n    byteToBase64URL(null, state, onChar);\n    return result.join('');\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_VERSIONS: () => (/* binding */ API_VERSIONS),\n/* harmony export */   API_VERSION_HEADER_NAME: () => (/* binding */ API_VERSION_HEADER_NAME),\n/* harmony export */   AUDIENCE: () => (/* binding */ AUDIENCE),\n/* harmony export */   AUTO_REFRESH_TICK_DURATION_MS: () => (/* binding */ AUTO_REFRESH_TICK_DURATION_MS),\n/* harmony export */   AUTO_REFRESH_TICK_THRESHOLD: () => (/* binding */ AUTO_REFRESH_TICK_THRESHOLD),\n/* harmony export */   BASE64URL_REGEX: () => (/* binding */ BASE64URL_REGEX),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   EXPIRY_MARGIN_MS: () => (/* binding */ EXPIRY_MARGIN_MS),\n/* harmony export */   GOTRUE_URL: () => (/* binding */ GOTRUE_URL),\n/* harmony export */   JWKS_TTL: () => (/* binding */ JWKS_TTL),\n/* harmony export */   NETWORK_FAILURE: () => (/* binding */ NETWORK_FAILURE),\n/* harmony export */   STORAGE_KEY: () => (/* binding */ STORAGE_KEY)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3;\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nconst EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS;\nconst GOTRUE_URL = 'http://localhost:9999';\nconst STORAGE_KEY = 'supabase.auth.token';\nconst AUDIENCE = '';\nconst DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst NETWORK_FAILURE = {\n    MAX_RETRIES: 10,\n    RETRY_INTERVAL: 2, // in deciseconds\n};\nconst API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version';\nconst API_VERSIONS = {\n    '2024-01-01': {\n        timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n        name: '2024-01-01',\n    },\n};\nconst BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;\nconst JWKS_TTL = 10 * 60 * 1000; // 10 minutes\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthApiError: () => (/* binding */ AuthApiError),\n/* harmony export */   AuthError: () => (/* binding */ AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* binding */ AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* binding */ AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* binding */ AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* binding */ AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* binding */ AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* binding */ AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* binding */ AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* binding */ AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* binding */ AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* binding */ CustomAuthError),\n/* harmony export */   isAuthApiError: () => (/* binding */ isAuthApiError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* binding */ isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* binding */ isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* binding */ isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* binding */ isAuthWeakPasswordError)\n/* harmony export */ });\nclass AuthError extends Error {\n    constructor(message, status, code) {\n        super(message);\n        this.__isAuthError = true;\n        this.name = 'AuthError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthError(error) {\n    return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nclass AuthApiError extends AuthError {\n    constructor(message, status, code) {\n        super(message, status, code);\n        this.name = 'AuthApiError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthApiError(error) {\n    return isAuthError(error) && error.name === 'AuthApiError';\n}\nclass AuthUnknownError extends AuthError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'AuthUnknownError';\n        this.originalError = originalError;\n    }\n}\nclass CustomAuthError extends AuthError {\n    constructor(message, name, status, code) {\n        super(message, status, code);\n        this.name = name;\n        this.status = status;\n    }\n}\nclass AuthSessionMissingError extends CustomAuthError {\n    constructor() {\n        super('Auth session missing!', 'AuthSessionMissingError', 400, undefined);\n    }\n}\nfunction isAuthSessionMissingError(error) {\n    return isAuthError(error) && error.name === 'AuthSessionMissingError';\n}\nclass AuthInvalidTokenResponseError extends CustomAuthError {\n    constructor() {\n        super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined);\n    }\n}\nclass AuthInvalidCredentialsError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidCredentialsError', 400, undefined);\n    }\n}\nclass AuthImplicitGrantRedirectError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthImplicitGrantRedirectError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nfunction isAuthImplicitGrantRedirectError(error) {\n    return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError';\n}\nclass AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nclass AuthRetryableFetchError extends CustomAuthError {\n    constructor(message, status) {\n        super(message, 'AuthRetryableFetchError', status, undefined);\n    }\n}\nfunction isAuthRetryableFetchError(error) {\n    return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nclass AuthWeakPasswordError extends CustomAuthError {\n    constructor(message, status, reasons) {\n        super(message, 'AuthWeakPasswordError', status, 'weak_password');\n        this.reasons = reasons;\n    }\n}\nfunction isAuthWeakPasswordError(error) {\n    return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\nclass AuthInvalidJwtError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt');\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _generateLinkResponse: () => (/* binding */ _generateLinkResponse),\n/* harmony export */   _noResolveJsonResponse: () => (/* binding */ _noResolveJsonResponse),\n/* harmony export */   _request: () => (/* binding */ _request),\n/* harmony export */   _sessionResponse: () => (/* binding */ _sessionResponse),\n/* harmony export */   _sessionResponsePassword: () => (/* binding */ _sessionResponsePassword),\n/* harmony export */   _ssoResponse: () => (/* binding */ _ssoResponse),\n/* harmony export */   _userResponse: () => (/* binding */ _userResponse),\n/* harmony export */   handleError: () => (/* binding */ handleError)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nasync function handleError(error) {\n    var _a;\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_1__.looksLikeFetchResponse)(error)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n        // status in 500...599 range - server had an error, request might be retryed.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n        data = await error.json();\n    }\n    catch (e) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError(_getErrorMessage(e), e);\n    }\n    let errorCode = undefined;\n    const responseAPIVersion = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.parseResponseAPIVersion)(error);\n    if (responseAPIVersion &&\n        responseAPIVersion.getTime() >= _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].timestamp &&\n        typeof data === 'object' &&\n        data &&\n        typeof data.code === 'string') {\n        errorCode = data.code;\n    }\n    else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n        errorCode = data.error_code;\n    }\n    if (!errorCode) {\n        // Legacy support for weak password errors, when there were no error codes\n        if (typeof data === 'object' &&\n            data &&\n            typeof data.weak_password === 'object' &&\n            data.weak_password &&\n            Array.isArray(data.weak_password.reasons) &&\n            data.weak_password.reasons.length &&\n            data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n        }\n    }\n    else if (errorCode === 'weak_password') {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, ((_a = data.weak_password) === null || _a === void 0 ? void 0 : _a.reasons) || []);\n    }\n    else if (errorCode === 'session_not_found') {\n        // The `session_id` inside the JWT does not correspond to a row in the\n        // `sessions` table. This usually means the user has signed out, has been\n        // deleted, or their session has somehow been terminated.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n    }\n    throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthApiError(_getErrorMessage(data), error.status || 500, errorCode);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json;charset=UTF-8' }, options === null || options === void 0 ? void 0 : options.headers);\n    params.body = JSON.stringify(body);\n    return Object.assign(Object.assign({}, params), parameters);\n};\nasync function _request(fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (!headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME]) {\n        headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME] = _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].name;\n    }\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n        headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n        qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = await _handleRequest(fetcher, method, url + queryString, {\n        headers,\n        noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson,\n    }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : { data: Object.assign({}, data), error: null };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n        result = await fetcher(url, Object.assign({}, requestParams));\n    }\n    catch (e) {\n        console.error(e);\n        // fetch failed, likely due to a network or CORS error\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n        await handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n        return result;\n    }\n    try {\n        return await result.json();\n    }\n    catch (e) {\n        await handleError(e);\n    }\n}\nfunction _sessionResponse(data) {\n    var _a;\n    let session = null;\n    if (hasSession(data)) {\n        session = Object.assign({}, data);\n        if (!data.expires_at) {\n            session.expires_at = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.expiresAt)(data.expires_in);\n        }\n    }\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { session, user }, error: null };\n}\nfunction _sessionResponsePassword(data) {\n    const response = _sessionResponse(data);\n    if (!response.error &&\n        data.weak_password &&\n        typeof data.weak_password === 'object' &&\n        Array.isArray(data.weak_password.reasons) &&\n        data.weak_password.reasons.length &&\n        data.weak_password.message &&\n        typeof data.weak_password.message === 'string' &&\n        data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        response.data.weak_password = data.weak_password;\n    }\n    return response;\n}\nfunction _userResponse(data) {\n    var _a;\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { user }, error: null };\n}\nfunction _ssoResponse(data) {\n    return { data, error: null };\n}\nfunction _generateLinkResponse(data) {\n    const { action_link, email_otp, hashed_token, redirect_to, verification_type } = data, rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n    const properties = {\n        action_link,\n        email_otp,\n        hashed_token,\n        redirect_to,\n        verification_type,\n    };\n    const user = Object.assign({}, rest);\n    return {\n        data: {\n            properties,\n            user,\n        },\n        error: null,\n    };\n}\nfunction _noResolveJsonResponse(data) {\n    return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n    return data.access_token && data.refresh_token && data.expires_in;\n}\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Deferred: () => (/* binding */ Deferred),\n/* harmony export */   decodeJWT: () => (/* binding */ decodeJWT),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   expiresAt: () => (/* binding */ expiresAt),\n/* harmony export */   generatePKCEChallenge: () => (/* binding */ generatePKCEChallenge),\n/* harmony export */   generatePKCEVerifier: () => (/* binding */ generatePKCEVerifier),\n/* harmony export */   getAlgorithm: () => (/* binding */ getAlgorithm),\n/* harmony export */   getCodeChallengeAndMethod: () => (/* binding */ getCodeChallengeAndMethod),\n/* harmony export */   getItemAsync: () => (/* binding */ getItemAsync),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   looksLikeFetchResponse: () => (/* binding */ looksLikeFetchResponse),\n/* harmony export */   parseParametersFromURL: () => (/* binding */ parseParametersFromURL),\n/* harmony export */   parseResponseAPIVersion: () => (/* binding */ parseResponseAPIVersion),\n/* harmony export */   removeItemAsync: () => (/* binding */ removeItemAsync),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   retryable: () => (/* binding */ retryable),\n/* harmony export */   setItemAsync: () => (/* binding */ setItemAsync),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   supportsLocalStorage: () => (/* binding */ supportsLocalStorage),\n/* harmony export */   userNotAvailableProxy: () => (/* binding */ userNotAvailableProxy),\n/* harmony export */   uuid: () => (/* binding */ uuid),\n/* harmony export */   validateExp: () => (/* binding */ validateExp),\n/* harmony export */   validateUUID: () => (/* binding */ validateUUID)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base64url */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\nfunction expiresAt(expiresIn) {\n    const timeNow = Math.round(Date.now() / 1000);\n    return timeNow + expiresIn;\n}\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nconst isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined';\nconst localStorageWriteTests = {\n    tested: false,\n    writable: false,\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nconst supportsLocalStorage = () => {\n    if (!isBrowser()) {\n        return false;\n    }\n    try {\n        if (typeof globalThis.localStorage !== 'object') {\n            return false;\n        }\n    }\n    catch (e) {\n        // DOM exception when accessing `localStorage`\n        return false;\n    }\n    if (localStorageWriteTests.tested) {\n        return localStorageWriteTests.writable;\n    }\n    const randomKey = `lswt-${Math.random()}${Math.random()}`;\n    try {\n        globalThis.localStorage.setItem(randomKey, randomKey);\n        globalThis.localStorage.removeItem(randomKey);\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = true;\n    }\n    catch (e) {\n        // localStorage can't be written to\n        // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = false;\n    }\n    return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nfunction parseParametersFromURL(href) {\n    const result = {};\n    const url = new URL(href);\n    if (url.hash && url.hash[0] === '#') {\n        try {\n            const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n            hashSearchParams.forEach((value, key) => {\n                result[key] = value;\n            });\n        }\n        catch (e) {\n            // hash is not a query string\n        }\n    }\n    // search parameters take precedence over hash parameters\n    url.searchParams.forEach((value, key) => {\n        result[key] = value;\n    });\n    return result;\n}\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst looksLikeFetchResponse = (maybeResponse) => {\n    return (typeof maybeResponse === 'object' &&\n        maybeResponse !== null &&\n        'status' in maybeResponse &&\n        'ok' in maybeResponse &&\n        'json' in maybeResponse &&\n        typeof maybeResponse.json === 'function');\n};\n// Storage helpers\nconst setItemAsync = async (storage, key, data) => {\n    await storage.setItem(key, JSON.stringify(data));\n};\nconst getItemAsync = async (storage, key) => {\n    const value = await storage.getItem(key);\n    if (!value) {\n        return null;\n    }\n    try {\n        return JSON.parse(value);\n    }\n    catch (_a) {\n        return value;\n    }\n};\nconst removeItemAsync = async (storage, key) => {\n    await storage.removeItem(key);\n};\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nclass Deferred {\n    constructor() {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        this.promise = new Deferred.promiseConstructor((res, rej) => {\n            // eslint-disable-next-line @typescript-eslint/no-extra-semi\n            ;\n            this.resolve = res;\n            this.reject = rej;\n        });\n    }\n}\nDeferred.promiseConstructor = Promise;\nfunction decodeJWT(token) {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('Invalid JWT structure');\n    }\n    // Regex checks for base64url format\n    for (let i = 0; i < parts.length; i++) {\n        if (!_constants__WEBPACK_IMPORTED_MODULE_0__.BASE64URL_REGEX.test(parts[i])) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('JWT not in base64url format');\n        }\n    }\n    const data = {\n        // using base64url lib\n        header: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[0])),\n        payload: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[1])),\n        signature: (0,_base64url__WEBPACK_IMPORTED_MODULE_2__.base64UrlToUint8Array)(parts[2]),\n        raw: {\n            header: parts[0],\n            payload: parts[1],\n        },\n    };\n    return data;\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nasync function sleep(time) {\n    return await new Promise((accept) => {\n        setTimeout(() => accept(null), time);\n    });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nfunction retryable(fn, isRetryable) {\n    const promise = new Promise((accept, reject) => {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        (async () => {\n            for (let attempt = 0; attempt < Infinity; attempt++) {\n                try {\n                    const result = await fn(attempt);\n                    if (!isRetryable(attempt, null, result)) {\n                        accept(result);\n                        return;\n                    }\n                }\n                catch (e) {\n                    if (!isRetryable(attempt, e)) {\n                        reject(e);\n                        return;\n                    }\n                }\n            }\n        })();\n    });\n    return promise;\n}\nfunction dec2hex(dec) {\n    return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nfunction generatePKCEVerifier() {\n    const verifierLength = 56;\n    const array = new Uint32Array(verifierLength);\n    if (typeof crypto === 'undefined') {\n        const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n        const charSetLen = charSet.length;\n        let verifier = '';\n        for (let i = 0; i < verifierLength; i++) {\n            verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n        }\n        return verifier;\n    }\n    crypto.getRandomValues(array);\n    return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = await crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes)\n        .map((c) => String.fromCharCode(c))\n        .join('');\n}\nasync function generatePKCEChallenge(verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' &&\n        typeof crypto.subtle !== 'undefined' &&\n        typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n        console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n        return verifier;\n    }\n    const hashed = await sha256(verifier);\n    return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nasync function getCodeChallengeAndMethod(storage, storageKey, isPasswordRecovery = false) {\n    const codeVerifier = generatePKCEVerifier();\n    let storedCodeVerifier = codeVerifier;\n    if (isPasswordRecovery) {\n        storedCodeVerifier += '/PASSWORD_RECOVERY';\n    }\n    await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier);\n    const codeChallenge = await generatePKCEChallenge(codeVerifier);\n    const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n    return [codeChallenge, codeChallengeMethod];\n}\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;\nfunction parseResponseAPIVersion(response) {\n    const apiVersion = response.headers.get(_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME);\n    if (!apiVersion) {\n        return null;\n    }\n    if (!apiVersion.match(API_VERSION_REGEX)) {\n        return null;\n    }\n    try {\n        const date = new Date(`${apiVersion}T00:00:00.0Z`);\n        return date;\n    }\n    catch (e) {\n        return null;\n    }\n}\nfunction validateExp(exp) {\n    if (!exp) {\n        throw new Error('Missing exp claim');\n    }\n    const timeNow = Math.floor(Date.now() / 1000);\n    if (exp <= timeNow) {\n        throw new Error('JWT has expired');\n    }\n}\nfunction getAlgorithm(alg) {\n    switch (alg) {\n        case 'RS256':\n            return {\n                name: 'RSASSA-PKCS1-v1_5',\n                hash: { name: 'SHA-256' },\n            };\n        case 'ES256':\n            return {\n                name: 'ECDSA',\n                namedCurve: 'P-256',\n                hash: { name: 'SHA-256' },\n            };\n        default:\n            throw new Error('Invalid alg claim');\n    }\n}\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;\nfunction validateUUID(str) {\n    if (!UUID_REGEX.test(str)) {\n        throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not');\n    }\n}\nfunction userNotAvailableProxy() {\n    const proxyTarget = {};\n    return new Proxy(proxyTarget, {\n        get: (target, prop) => {\n            if (prop === '__isUserNotAvailableProxy') {\n                return true;\n            }\n            // Preventative check for common problematic symbols during cloning/inspection\n            // These symbols might be accessed by structuredClone or other internal mechanisms.\n            if (typeof prop === 'symbol') {\n                const sProp = prop.toString();\n                if (sProp === 'Symbol(Symbol.toPrimitive)' ||\n                    sProp === 'Symbol(Symbol.toStringTag)' ||\n                    sProp === 'Symbol(util.inspect.custom)') {\n                    // Node.js util.inspect\n                    return undefined;\n                }\n            }\n            throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the \"${prop}\" property of the session object is not supported. Please use getUser() instead.`);\n        },\n        set: (_target, prop) => {\n            throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`);\n        },\n        deleteProperty: (_target, prop) => {\n            throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`);\n        },\n    });\n}\n/**\n * Deep clones a JSON-serializable object using JSON.parse(JSON.stringify(obj)).\n * Note: Only works for JSON-safe data.\n */\nfunction deepClone(obj) {\n    return JSON.parse(JSON.stringify(obj));\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoryLocalStorageAdapter: () => (/* binding */ memoryLocalStorageAdapter)\n/* harmony export */ });\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nfunction memoryLocalStorageAdapter(store = {}) {\n    return {\n        getItem: (key) => {\n            return store[key] || null;\n        },\n        setItem: (key, value) => {\n            store[key] = value;\n        },\n        removeItem: (key) => {\n            delete store[key];\n        },\n    };\n}\n//# sourceMappingURL=local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzEuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvbGliL2xvY2FsLXN0b3JhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sNkNBQTZDO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi43MS4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXGxvY2FsLXN0b3JhZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXR1cm5zIGEgbG9jYWxTdG9yYWdlLWxpa2Ugb2JqZWN0IHRoYXQgc3RvcmVzIHRoZSBrZXktdmFsdWUgcGFpcnMgaW5cbiAqIG1lbW9yeS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG1lbW9yeUxvY2FsU3RvcmFnZUFkYXB0ZXIoc3RvcmUgPSB7fSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGdldEl0ZW06IChrZXkpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBzdG9yZVtrZXldIHx8IG51bGw7XG4gICAgICAgIH0sXG4gICAgICAgIHNldEl0ZW06IChrZXksIHZhbHVlKSA9PiB7XG4gICAgICAgICAgICBzdG9yZVtrZXldID0gdmFsdWU7XG4gICAgICAgIH0sXG4gICAgICAgIHJlbW92ZUl0ZW06IChrZXkpID0+IHtcbiAgICAgICAgICAgIGRlbGV0ZSBzdG9yZVtrZXldO1xuICAgICAgICB9LFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1sb2NhbC1zdG9yYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LockAcquireTimeoutError: () => (/* binding */ LockAcquireTimeoutError),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* binding */ NavigatorLockAcquireTimeoutError),\n/* harmony export */   ProcessLockAcquireTimeoutError: () => (/* binding */ ProcessLockAcquireTimeoutError),\n/* harmony export */   internals: () => (/* binding */ internals),\n/* harmony export */   navigatorLock: () => (/* binding */ navigatorLock),\n/* harmony export */   processLock: () => (/* binding */ processLock)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n\n/**\n * @experimental\n */\nconst internals = {\n    /**\n     * @experimental\n     */\n    debug: !!(globalThis &&\n        (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)() &&\n        globalThis.localStorage &&\n        globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'),\n};\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nclass LockAcquireTimeoutError extends Error {\n    constructor(message) {\n        super(message);\n        this.isAcquireTimeout = true;\n    }\n}\nclass NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\nclass ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with Safari being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function navigatorLock(name, acquireTimeout, fn) {\n    if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n        setTimeout(() => {\n            abortController.abort();\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n            }\n        }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    // Wrapping navigator.locks.request() with a plain Promise is done as some\n    // libraries like zone.js patch the Promise object to track the execution\n    // context. However, it appears that most browsers use an internal promise\n    // implementation when using the navigator.locks.request() API causing them\n    // to lose context and emit confusing log messages or break certain features.\n    // This wrapping is believed to help zone.js track the execution context\n    // better.\n    return await Promise.resolve().then(() => globalThis.navigator.locks.request(name, acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n        }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n        }, async (lock) => {\n        if (lock) {\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n            }\n            try {\n                return await fn();\n            }\n            finally {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n                }\n            }\n        }\n        else {\n            if (acquireTimeout === 0) {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n                }\n                throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n            }\n            else {\n                if (internals.debug) {\n                    try {\n                        const result = await globalThis.navigator.locks.query();\n                        console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n                    }\n                    catch (e) {\n                        console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n                    }\n                }\n                // Browser is not following the Navigator LockManager spec, it\n                // returned a null lock when we didn't use ifAvailable. So we can\n                // pretend the lock is acquired in the name of backward compatibility\n                // and user experience and just run the function.\n                console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n                return await fn();\n            }\n        }\n    }));\n}\nconst PROCESS_LOCKS = {};\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function processLock(name, acquireTimeout, fn) {\n    var _a;\n    const previousOperation = (_a = PROCESS_LOCKS[name]) !== null && _a !== void 0 ? _a : Promise.resolve();\n    const currentOperation = Promise.race([\n        previousOperation.catch(() => {\n            // ignore error of previous operation that we're waiting to finish\n            return null;\n        }),\n        acquireTimeout >= 0\n            ? new Promise((_, reject) => {\n                setTimeout(() => {\n                    reject(new ProcessLockAcquireTimeoutError(`Acquring process lock with name \"${name}\" timed out`));\n                }, acquireTimeout);\n            })\n            : null,\n    ].filter((x) => x))\n        .catch((e) => {\n        if (e && e.isAcquireTimeout) {\n            throw e;\n        }\n        return null;\n    })\n        .then(async () => {\n        // previous operations finished and we didn't get a race on the acquire\n        // timeout, so the current operation can finally start\n        return await fn();\n    });\n    PROCESS_LOCKS[name] = currentOperation.catch(async (e) => {\n        if (e && e.isAcquireTimeout) {\n            // if the current operation timed out, it doesn't mean that the previous\n            // operation finished, so we need contnue waiting for it to finish\n            await previousOperation;\n            return null;\n        }\n        throw e;\n    });\n    // finally wait for the current operation to finish successfully, with an\n    // error or with an acquire timeout error\n    return await currentOperation;\n}\n//# sourceMappingURL=locks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/locks.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polyfillGlobalThis: () => (/* binding */ polyfillGlobalThis)\n/* harmony export */ });\n/**\n * https://mathiasbynens.be/notes/globalthis\n */\nfunction polyfillGlobalThis() {\n    if (typeof globalThis === 'object')\n        return;\n    try {\n        Object.defineProperty(Object.prototype, '__magic__', {\n            get: function () {\n                return this;\n            },\n            configurable: true,\n        });\n        // @ts-expect-error 'Allow access to magic'\n        __magic__.globalThis = __magic__;\n        // @ts-expect-error 'Allow access to magic'\n        delete Object.prototype.__magic__;\n    }\n    catch (e) {\n        if (typeof self !== 'undefined') {\n            // @ts-expect-error 'Allow access to globals'\n            self.globalThis = self;\n        }\n    }\n}\n//# sourceMappingURL=polyfills.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzEuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvbGliL3BvbHlmaWxscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrYXV0aC1qc0AyLjcxLjFcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxccG9seWZpbGxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogaHR0cHM6Ly9tYXRoaWFzYnluZW5zLmJlL25vdGVzL2dsb2JhbHRoaXNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBvbHlmaWxsR2xvYmFsVGhpcygpIHtcbiAgICBpZiAodHlwZW9mIGdsb2JhbFRoaXMgPT09ICdvYmplY3QnKVxuICAgICAgICByZXR1cm47XG4gICAgdHJ5IHtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KE9iamVjdC5wcm90b3R5cGUsICdfX21hZ2ljX18nLCB7XG4gICAgICAgICAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgIH0pO1xuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yICdBbGxvdyBhY2Nlc3MgdG8gbWFnaWMnXG4gICAgICAgIF9fbWFnaWNfXy5nbG9iYWxUaGlzID0gX19tYWdpY19fO1xuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yICdBbGxvdyBhY2Nlc3MgdG8gbWFnaWMnXG4gICAgICAgIGRlbGV0ZSBPYmplY3QucHJvdG90eXBlLl9fbWFnaWNfXztcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBzZWxmICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAnQWxsb3cgYWNjZXNzIHRvIGdsb2JhbHMnXG4gICAgICAgICAgICBzZWxmLmdsb2JhbFRoaXMgPSBzZWxmO1xuICAgICAgICB9XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cG9seWZpbGxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/types.js":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/types.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* binding */ SIGN_OUT_SCOPES)\n/* harmony export */ });\nconst SIGN_OUT_SCOPES = ['global', 'local', 'others'];\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzEuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvbGliL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtanNAMi43MS4xXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcYXV0aC1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBTSUdOX09VVF9TQ09QRVMgPSBbJ2dsb2JhbCcsICdsb2NhbCcsICdvdGhlcnMnXTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/version.js":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/version.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.71.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzEuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZXJkZW1cXEdpdGh1YlxcaGVsYWxzb2Z0XFxlc2VucGFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrYXV0aC1qc0AyLjcxLjFcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjcxLjEnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.71.1/node_modules/@supabase/auth-js/dist/module/lib/version.js\n");

/***/ })

};
;