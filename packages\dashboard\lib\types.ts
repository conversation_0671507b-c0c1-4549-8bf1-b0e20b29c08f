// Catalog types
export interface Activity {
  id: string;
  name: Record<string, string>;
  description?: Record<string, string>;
  category_id: string;
  color: string;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: Record<string, string>;
  description?: Record<string, string>;
  color: string;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface Field {
  id: string;
  name: Record<string, string>;
  description?: Record<string, string>;
  type: "text" | "number" | "select" | "multiselect" | "boolean" | "date";
  is_required: boolean;
  created_at: string;
  updated_at: string;
}

export interface FieldOption {
  id: string;
  field_id: string;
  name: Record<string, string>;
  value: string;
  sort_order: number;
  created_at: string;
}

// User management types
export interface UserProfile {
  user_id: string;
  username: string;
  nickname?: string;
  bio?: string;
  gender?: string;
  join_date: string;
  birth_date?: string;
}

export interface UserAnalytics {
  active_users: number;
  new_users_today: number;
  total_users: number;
  user_growth: Array<{
    date: string;
    users: number;
  }>;
}

// Transaction types
export interface Transaction {
  id: string;
  from_user_id?: string;
  to_user_id?: string;
  amount: number;
  currency: string;
  type: string;
  status: string;
  created_at: string;
}

// Support types
export interface SupportTicket {
  id: string;
  user_id: string;
  assigned_to?: string;
  title: string;
  problem_description: string;
  status: string;
  type: string;
  created_at: string;
  updated_at: string;
}
