{"name": "@esenpai/dashboard", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --port 3002", "build": "next build", "start": "next start --port 3002", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@tanstack/react-query": "^5.0.0", "@tanstack/react-table": "^8.0.0", "shared": "workspace:*", "zod": "^3.22.0", "date-fns": "^3.0.0", "recharts": "^2.8.0", "lucide-react": "^0.263.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0"}}