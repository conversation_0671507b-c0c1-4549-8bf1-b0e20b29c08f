import { test, expect } from "vitest";
import { serviceClient } from "./utils/client";
import { mockCustomer, mockProvider } from "./mocks/auth.user";

const customer = mockCustomer({ soda_balance: 1000, cap_balance: 500 });
const provider = mockProvider({ soda_balance: 800, cap_balance: 300 });
const anotherProvider = mockProvider({ soda_balance: 50, cap_balance: 100 });

test("should successfully gift soda from customer to provider", async () => {
  if (!customer.client || !customer.data || !provider.data) {
    throw new Error("Customer or provider not defined");
  }

  const giftAmount = 150;

  // Get initial balances
  const { data: initialCustomerWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", customer.data.id)
    .single();

  const { data: initialProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", provider.data.id)
    .single();

  const initialCustomerBalance = initialCustomerWallet?.soda_balance || 0;
  const initialProviderBalance = initialProviderWallet?.soda_balance || 0;

  // Gift soda to provider
  const { data: transferId, error } = await customer.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: provider.data.id,
      p_soda_amount: giftAmount
    });

  expect(error).toBeNull();
  expect(transferId).toBeDefined();

  // Verify balances after transfer
  const { data: finalCustomerWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", customer.data.id)
    .single();

  const { data: finalProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", provider.data.id)
    .single();

  expect(finalCustomerWallet?.soda_balance).toBe(
    initialCustomerBalance - giftAmount
  );
  expect(finalProviderWallet?.soda_balance).toBe(
    initialProviderBalance + giftAmount
  );

  if (!transferId) throw new Error("Transfer ID is undefined");

  // Verify transfer record was created
  const { data: transfer } = await serviceClient
    .schema("app_transaction")
    .from("transfer")
    .select("*")
    .eq("id", transferId)
    .single();

  expect(transfer?.sender_id).toBe(customer.data.id);
  expect(transfer?.receiver_id).toBe(provider.data.id);
  expect(transfer?.soda_amount).toBe(giftAmount);
  expect(transfer?.cap_amount).toBe(0);
});

test("should fail when gifting soda with insufficient balance", async () => {
  if (!customer.client || !customer.data || !provider.data) {
    throw new Error("Customer or provider not defined");
  }

  const excessiveAmount = 10000; // More than customer's balance

  const { data, error } = await customer.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: provider.data.id,
      p_soda_amount: excessiveAmount
    });

  expect(error).not.toBeNull();
  expect(error?.message).toContain("Insufficient soda balance");
  expect(data).toBeNull();
});

test("should fail when gifting to non-existent or non-approved provider", async () => {
  if (!customer.client || !customer.data) {
    throw new Error("Customer not defined");
  }

  // Use a random UUID for a non-existent user
  const nonExistentUserId = "00000000-0000-0000-0000-000000000000";

  const { data, error } = await customer.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: nonExistentUserId,
      p_soda_amount: 100
    });

  expect(error).not.toBeNull();
  expect(error?.message).toContain("Provider not found or not approved");
  expect(data).toBeNull();
});

test("should fail when gifting zero or negative amount", async () => {
  if (!customer.client || !customer.data || !provider.data) {
    throw new Error("Customer or provider not defined");
  }

  // Test zero amount
  const { data: data1, error: error1 } = await customer.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: provider.data.id,
      p_soda_amount: 0
    });

  expect(error1).not.toBeNull();
  expect(error1?.message).toContain("Soda amount must be greater than zero");
  expect(data1).toBeNull();

  // Test negative amount (domain constraint will catch this)
  const { data: data2, error: error2 } = await customer.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: provider.data.id,
      p_soda_amount: -50
    });

  expect(error2).not.toBeNull();
  expect(error2?.message).toContain("token_unit_check");
  expect(data2).toBeNull();
});

test("should fail when trying to gift soda to yourself", async () => {
  if (!provider.client || !provider.data) {
    throw new Error("Provider not defined");
  }

  const { data, error } = await provider.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: provider.data.id,
      p_soda_amount: 50
    });

  expect(error).not.toBeNull();
  expect(error?.message).toContain("Cannot gift soda to yourself");
  expect(data).toBeNull();
});

test("should allow provider to gift soda to another provider", async () => {
  if (!provider.client || !provider.data || !anotherProvider.data) {
    throw new Error("Provider or another provider not defined");
  }

  const giftAmount = 100;

  // Get initial balances
  const { data: initialProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", provider.data.id)
    .single();

  const { data: initialAnotherProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", anotherProvider.data.id)
    .single();

  const initialProviderBalance = initialProviderWallet?.soda_balance || 0;
  const initialAnotherProviderBalance =
    initialAnotherProviderWallet?.soda_balance || 0;

  // Gift soda from provider to another provider
  const { data: transferId, error } = await provider.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: anotherProvider.data.id,
      p_soda_amount: giftAmount
    });

  expect(error).toBeNull();
  expect(transferId).toBeDefined();

  // Verify balances after transfer
  const { data: finalProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", provider.data.id)
    .single();

  const { data: finalAnotherProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance")
    .eq("user_id", anotherProvider.data.id)
    .single();

  expect(finalProviderWallet?.soda_balance).toBe(
    initialProviderBalance - giftAmount
  );
  expect(finalAnotherProviderWallet?.soda_balance).toBe(
    initialAnotherProviderBalance + giftAmount
  );

  if (!transferId) throw new Error("Transfer ID is undefined");

  // Verify transfer record was created
  const { data: transfer } = await serviceClient
    .schema("app_transaction")
    .from("transfer")
    .select("*")
    .eq("id", transferId)
    .single();

  expect(transfer?.sender_id).toBe(provider.data.id);
  expect(transfer?.receiver_id).toBe(anotherProvider.data.id);
  expect(transfer?.soda_amount).toBe(giftAmount);
  expect(transfer?.cap_amount).toBe(0);
});

test("should fail without proper permissions", async () => {
  if (!customer.client || !customer.data || !provider.data) {
    throw new Error("Customer or provider not defined");
  }

  // This test assumes the user doesn't have 'provider.soda.gift' capability
  // The actual behavior depends on the capability system implementation
  const { error } = await customer.client
    .schema("app_provider")
    .rpc("gift_soda_to_provider", {
      p_provider_id: provider.data.id,
      p_soda_amount: 50
    });

  // If the capability system is strict, this should fail
  // If not implemented yet, this test might pass
  if (error) {
    expect(error.message).toContain("Insufficient permissions");
  }
});
