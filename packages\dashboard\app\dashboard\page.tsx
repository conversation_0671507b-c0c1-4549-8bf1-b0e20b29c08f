"use client";

import { useQuery } from "@tanstack/react-query";
import { createClient } from "shared/lib/supabase/service";
import { Users, DollarSign, MessageSquare, Activity } from "lucide-react";

export default function DashboardPage() {
  const { data: dashboardStats, isLoading } = useQuery({
    queryKey: ["dashboard-stats"],
    queryFn: async () => {
      const supabase = createClient();

      // Use the new admin panel functions
      const [userStats, transactionStats, supportStats, providerStats] =
        await Promise.all([
          supabase.schema("app_account").rpc("get_user_stats"),
          supabase.schema("app_transaction").rpc("get_transaction_stats"),
          supabase.schema("app_support").rpc("get_support_stats"),
          supabase.schema("app_provider").rpc("get_provider_stats")
        ]);

      return {
        totalUsers: userStats.data?.total_users || 0,
        openTickets: supportStats.data?.open_tickets || 0,
        activeProviders: providerStats.data?.active_providers || 0,
        dailyRevenue: transactionStats.data?.daily_revenue || 0
      };
    },
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Loading dashboard...</div>
      </div>
    );
  }

  const metrics = [
    {
      title: "Total Users",
      value: dashboardStats?.totalUsers || 0,
      change: "+12%",
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Daily Revenue",
      value: `${dashboardStats?.dailyRevenue || 0} soda`,
      change: "+8%",
      icon: DollarSign,
      color: "text-green-600"
    },
    {
      title: "Open Tickets",
      value: dashboardStats?.openTickets || 0,
      change: "-3%",
      icon: MessageSquare,
      color: "text-orange-600"
    },
    {
      title: "Active Providers",
      value: dashboardStats?.activeProviders || 0,
      change: "+5%",
      icon: Activity,
      color: "text-purple-600"
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
        <p className="mt-1 text-sm text-gray-600">
          Welcome to the E-Senpai admin panel. Here's what's happening on your
          platform.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <div
              key={metric.title}
              className="bg-white overflow-hidden shadow rounded-lg"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Icon className={`h-6 w-6 ${metric.color}`} />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {metric.title}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {metric.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <span className="font-medium text-green-600">
                    {metric.change}
                  </span>
                  <span className="text-gray-500"> from last month</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Quick Actions
          </h3>
          <div className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            <button className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400">
              <div>
                <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-700 ring-4 ring-white">
                  <Users className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" />
                  Manage Users
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  View and manage user accounts, roles, and permissions.
                </p>
              </div>
            </button>

            <button className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400">
              <div>
                <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                  <MessageSquare className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" />
                  Support Tickets
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Review and respond to customer support requests.
                </p>
              </div>
            </button>

            <button className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400">
              <div>
                <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-700 ring-4 ring-white">
                  <Activity className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" />
                  Platform Analytics
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  View detailed analytics and performance metrics.
                </p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
