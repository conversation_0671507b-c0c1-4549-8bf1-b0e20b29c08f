/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0";
exports.ids = ["vendor-chunks/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/cookies.js":
/*!***********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/cookies.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyServerStorage: () => (/* binding */ applyServerStorage),\n/* harmony export */   createStorageFromOptions: () => (/* binding */ createStorageFromOptions)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(ssr)/../../node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\nconst BASE64_PREFIX = \"base64-\";\n/**\n * Creates a storage client that handles cookies correctly for browser and\n * server clients with or without properly provided cookie methods.\n *\n * @param options The options passed to createBrowserClient or createServer client.\n *\n * @param isServerClient Whether it's called from createServerClient.\n */\nfunction createStorageFromOptions(options, isServerClient) {\n    const cookies = options.cookies ?? null;\n    const cookieEncoding = options.cookieEncoding;\n    const setItems = {};\n    const removedItems = {};\n    let getAll;\n    let setAll;\n    if (cookies) {\n        if (\"get\" in cookies) {\n            // Just get is not enough, because the client needs to see what cookies\n            // are already set and unset them if necessary. To attempt to fix this\n            // behavior for most use cases, we pass \"hints\" which is the keys of the\n            // storage items. They are then converted to their corresponding cookie\n            // chunk names and are fetched with get. Only 5 chunks are fetched, which\n            // should be enough for the majority of use cases, but does not solve\n            // those with very large sessions.\n            const getWithHints = async (keyHints) => {\n                // optimistically find the first 5 potential chunks for the specified key\n                const chunkNames = keyHints.flatMap((keyHint) => [\n                    keyHint,\n                    ...Array.from({ length: 5 }).map((_, i) => `${keyHint}.${i}`),\n                ]);\n                const chunks = [];\n                for (let i = 0; i < chunkNames.length; i += 1) {\n                    const value = await cookies.get(chunkNames[i]);\n                    if (!value && typeof value !== \"string\") {\n                        continue;\n                    }\n                    chunks.push({ name: chunkNames[i], value });\n                }\n                // TODO: detect and log stale chunks error\n                return chunks;\n            };\n            getAll = async (keyHints) => await getWithHints(keyHints);\n            if (\"set\" in cookies && \"remove\" in cookies) {\n                setAll = async (setCookies) => {\n                    for (let i = 0; i < setCookies.length; i += 1) {\n                        const { name, value, options } = setCookies[i];\n                        if (value) {\n                            await cookies.set(name, value, options);\n                        }\n                        else {\n                            await cookies.remove(name, options);\n                        }\n                    }\n                };\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else if (\"getAll\" in cookies) {\n            getAll = async () => await cookies.getAll();\n            if (\"setAll\" in cookies) {\n                setAll = cookies.setAll;\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else {\n            // neither get nor getAll is present on cookies, only will occur if pure JavaScript is used, but cookies is an object\n            throw new Error(`@supabase/ssr: ${isServerClient ? \"createServerClient\" : \"createBrowserClient\"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)() ? \" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.\" : \"\"}`);\n        }\n    }\n    else if (!isServerClient && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)()) {\n        // The environment is browser, so use the document.cookie API to implement getAll and setAll.\n        const noHintGetAll = () => {\n            const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(document.cookie);\n            return Object.keys(parsed).map((name) => ({\n                name,\n                value: parsed[name] ?? \"\",\n            }));\n        };\n        getAll = () => noHintGetAll();\n        setAll = (setCookies) => {\n            setCookies.forEach(({ name, value, options }) => {\n                document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n            });\n        };\n    }\n    else if (isServerClient) {\n        throw new Error(\"@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)\");\n    }\n    else {\n        // getting cookies when there's no window but we're in browser mode can be OK, because the developer probably is not using auth functions\n        getAll = () => {\n            return [];\n        };\n        // this is NOT OK because the developer is using auth functions that require setting some state, so that must error out\n        setAll = () => {\n            throw new Error(\"@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed\");\n        };\n    }\n    if (!isServerClient) {\n        // This is the storage client to be used in browsers. It only\n        // works on the cookies abstraction, unlike the server client\n        // which only uses cookies to read the initial state. When an\n        // item is set, cookies are both cleared and set to values so\n        // that stale chunks are not left remaining.\n        return {\n            getAll, // for type consistency\n            setAll, // for type consistency\n            setItems, // for type consistency\n            removedItems, // for type consistency\n            storage: {\n                isServer: false,\n                getItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                        const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                        if (!cookie) {\n                            return null;\n                        }\n                        return cookie.value;\n                    });\n                    if (!chunkedCookie) {\n                        return null;\n                    }\n                    let decoded = chunkedCookie;\n                    if (chunkedCookie.startsWith(BASE64_PREFIX)) {\n                        decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                    }\n                    return decoded;\n                },\n                setItem: async (key, value) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key)));\n                    let encoded = value;\n                    if (cookieEncoding === \"base64url\") {\n                        encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(value);\n                    }\n                    const setCookies = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(key, encoded);\n                    setCookies.forEach(({ name }) => {\n                        removeCookies.delete(name);\n                    });\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    const setCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    delete setCookieOptions.name;\n                    const allToSet = [\n                        ...[...removeCookies].map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })),\n                        ...setCookies.map(({ name, value }) => ({\n                            name,\n                            value,\n                            options: setCookieOptions,\n                        })),\n                    ];\n                    if (allToSet.length > 0) {\n                        await setAll(allToSet);\n                    }\n                },\n                removeItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key));\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    if (removeCookies.length > 0) {\n                        await setAll(removeCookies.map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })));\n                    }\n                },\n            },\n        };\n    }\n    // This is the server client. It only uses getAll to read the initial\n    // state. Any subsequent changes to the items is persisted in the\n    // setItems and removedItems objects. createServerClient *must* use\n    // getAll, setAll and the values in setItems and removedItems to\n    // persist the changes *at once* when appropriate (usually only when\n    // the TOKEN_REFRESHED, USER_UPDATED or SIGNED_OUT events are fired by\n    // the Supabase Auth client).\n    return {\n        getAll,\n        setAll,\n        setItems,\n        removedItems,\n        storage: {\n            // to signal to the libraries that these cookies are\n            // coming from a server environment and their value\n            // should not be trusted\n            isServer: true,\n            getItem: async (key) => {\n                if (typeof setItems[key] === \"string\") {\n                    return setItems[key];\n                }\n                if (removedItems[key]) {\n                    return null;\n                }\n                const allCookies = await getAll([key]);\n                const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                    const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                    if (!cookie) {\n                        return null;\n                    }\n                    return cookie.value;\n                });\n                if (!chunkedCookie) {\n                    return null;\n                }\n                let decoded = chunkedCookie;\n                if (typeof chunkedCookie === \"string\" &&\n                    chunkedCookie.startsWith(BASE64_PREFIX)) {\n                    decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                }\n                return decoded;\n            },\n            setItem: async (key, value) => {\n                // We don't have an `onAuthStateChange` event that can let us know that\n                // the PKCE code verifier is being set. Therefore, if we see it being\n                // set, we need to apply the storage (call `setAll` so the cookie is\n                // set properly).\n                if (key.endsWith(\"-code-verifier\")) {\n                    await applyServerStorage({\n                        getAll,\n                        setAll,\n                        // pretend only that the code verifier was set\n                        setItems: { [key]: value },\n                        // pretend that nothing was removed\n                        removedItems: {},\n                    }, {\n                        cookieOptions: options?.cookieOptions ?? null,\n                        cookieEncoding,\n                    });\n                }\n                setItems[key] = value;\n                delete removedItems[key];\n            },\n            removeItem: async (key) => {\n                // Intentionally not applying the storage when the key is the PKCE code\n                // verifier, as usually right after it's removed other items are set,\n                // so application of the storage will be handled by the\n                // `onAuthStateChange` callback that follows removal -- usually as part\n                // of the `exchangeCodeForSession` call.\n                delete setItems[key];\n                removedItems[key] = true;\n            },\n        },\n    };\n}\n/**\n * When createServerClient needs to apply the created storage to cookies, it\n * should call this function which handles correcly setting cookies for stored\n * and removed items in the storage.\n */\nasync function applyServerStorage({ getAll, setAll, setItems, removedItems, }, options) {\n    const cookieEncoding = options.cookieEncoding;\n    const cookieOptions = options.cookieOptions ?? null;\n    const allCookies = await getAll([\n        ...(setItems ? Object.keys(setItems) : []),\n        ...(removedItems ? Object.keys(removedItems) : []),\n    ]);\n    const cookieNames = allCookies?.map(({ name }) => name) || [];\n    const removeCookies = Object.keys(removedItems).flatMap((itemName) => {\n        return cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName));\n    });\n    const setCookies = Object.keys(setItems).flatMap((itemName) => {\n        const removeExistingCookiesForItem = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName)));\n        let encoded = setItems[itemName];\n        if (cookieEncoding === \"base64url\") {\n            encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(encoded);\n        }\n        const chunks = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(itemName, encoded);\n        chunks.forEach((chunk) => {\n            removeExistingCookiesForItem.delete(chunk.name);\n        });\n        removeCookies.push(...removeExistingCookiesForItem);\n        return chunks;\n    });\n    const removeCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: 0,\n    };\n    const setCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n    };\n    // the NextJS cookieStore API can get confused if the `name` from\n    // options.cookieOptions leaks\n    delete removeCookieOptions.name;\n    delete setCookieOptions.name;\n    await setAll([\n        ...removeCookies.map((name) => ({\n            name,\n            value: \"\",\n            options: removeCookieOptions,\n        })),\n        ...setCookies.map(({ name, value }) => ({\n            name,\n            value,\n            options: setCookieOptions,\n        })),\n    ]);\n}\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/cookies.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createBrowserClient.js":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createBrowserClient.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.53.0/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cookies */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\n\nlet cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n    // singleton client is created only if isSingleton is set to true, or if isSingleton is not defined and we detect a browser\n    const shouldUseSingleton = options?.isSingleton === true ||\n        ((!options || !(\"isSingleton\" in options)) && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)());\n    if (shouldUseSingleton && cachedBrowserClient) {\n        return cachedBrowserClient;\n    }\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage } = (0,_cookies__WEBPACK_IMPORTED_MODULE_2__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, false);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_0__.VERSION} createBrowserClient`,\n            },\n        },\n        auth: {\n            ...options?.auth,\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            flowType: \"pkce\",\n            autoRefreshToken: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)(),\n            detectSessionInUrl: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)(),\n            persistSession: true,\n            storage,\n        },\n    });\n    if (shouldUseSingleton) {\n        cachedBrowserClient = client;\n    }\n    return client;\n}\n//# sourceMappingURL=createBrowserClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createBrowserClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createServerClient.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createServerClient.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.53.0/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cookies */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage, getAll, setAll, setItems, removedItems } = (0,_cookies__WEBPACK_IMPORTED_MODULE_1__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, true);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_0__.VERSION} createServerClient`,\n            },\n        },\n        auth: {\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            ...options?.auth,\n            flowType: \"pkce\",\n            autoRefreshToken: false,\n            detectSessionInUrl: false,\n            persistSession: true,\n            storage,\n        },\n    });\n    client.auth.onAuthStateChange(async (event) => {\n        // The SIGNED_IN event is fired very often, but we don't need to\n        // apply the storage each time it fires, only if there are changes\n        // that need to be set -- which is if setItems / removeItems have\n        // data.\n        const hasStorageChanges = Object.keys(setItems).length > 0 || Object.keys(removedItems).length > 0;\n        if (hasStorageChanges &&\n            (event === \"SIGNED_IN\" ||\n                event === \"TOKEN_REFRESHED\" ||\n                event === \"USER_UPDATED\" ||\n                event === \"PASSWORD_RECOVERY\" ||\n                event === \"SIGNED_OUT\" ||\n                event === \"MFA_CHALLENGE_VERIFIED\")) {\n            await (0,_cookies__WEBPACK_IMPORTED_MODULE_1__.applyServerStorage)({ getAll, setAll, setItems, removedItems }, {\n                cookieOptions: options?.cookieOptions ?? null,\n                cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n            });\n        }\n    });\n    return client;\n}\n//# sourceMappingURL=createServerClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createServerClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/index.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/index.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.combineChunks),\n/* harmony export */   createBrowserClient: () => (/* reexport safe */ _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient),\n/* harmony export */   createChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.createChunks),\n/* harmony export */   createServerClient: () => (/* reexport safe */ _createServerClient__WEBPACK_IMPORTED_MODULE_1__.createServerClient),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createBrowserClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createBrowserClient.js\");\n/* harmony import */ var _createServerClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createServerClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/createServerClient.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_2__) if([\"default\",\"createBrowserClient\",\"createServerClient\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFzQztBQUNEO0FBQ2I7QUFDQTtBQUN4QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jcmVhdGVCcm93c2VyQ2xpZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jcmVhdGVTZXJ2ZXJDbGllbnRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3R5cGVzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi91dGlsc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/types.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/types.js ***!
  \*********************************************************************************************************************************/
/***/ (() => {

eval("//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/base64url.js":
/*!*******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/base64url.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\".split(\"\");\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = \" \\t\\n\\r=\".split(\"\");\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    let queue = 0;\n    let queuedBits = 0;\n    const emitter = (byte) => {\n        queue = (queue << 8) | byte;\n        queuedBits += 8;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    };\n    stringToUTF8(str, emitter);\n    if (queuedBits > 0) {\n        queue = queue << (6 - queuedBits);\n        queuedBits = 6;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    }\n    return base64.join(\"\");\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const state = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    let queue = 0;\n    let queuedBits = 0;\n    for (let i = 0; i < str.length; i += 1) {\n        const codepoint = str.charCodeAt(i);\n        const bits = FROM_BASE64URL[codepoint];\n        if (bits > -1) {\n            // valid Base64-URL character\n            queue = (queue << 6) | bits;\n            queuedBits += 6;\n            while (queuedBits >= 8) {\n                stringFromUTF8((queue >> (queuedBits - 8)) & 0xff, state, emit);\n                queuedBits -= 8;\n            }\n        }\n        else if (bits === -2) {\n            // ignore spaces, tabs, newlines, =\n            continue;\n        }\n        else {\n            throw new Error(`Invalid Base64-URL character \"${str.at(i)}\" at position ${i}`);\n        }\n    }\n    return conv.join(\"\");\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/base64url.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/chunker.js":
/*!*****************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/chunker.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* binding */ MAX_CHUNK_SIZE),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isChunkLike: () => (/* binding */ isChunkLike)\n/* harmony export */ });\nconst MAX_CHUNK_SIZE = 3180;\nconst CHUNK_LIKE_REGEX = /^(.*)[.](0|[1-9][0-9]*)$/;\nfunction isChunkLike(cookieName, key) {\n    if (cookieName === key) {\n        return true;\n    }\n    const chunkLike = cookieName.match(CHUNK_LIKE_REGEX);\n    if (chunkLike && chunkLike[1] === key) {\n        return true;\n    }\n    return false;\n}\n/**\n * create chunks from a string and return an array of object\n */\nfunction createChunks(key, value, chunkSize) {\n    const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n    let encodedValue = encodeURIComponent(value);\n    if (encodedValue.length <= resolvedChunkSize) {\n        return [{ name: key, value }];\n    }\n    const chunks = [];\n    while (encodedValue.length > 0) {\n        let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n        const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n        // Check if the last escaped character is truncated.\n        if (lastEscapePos > resolvedChunkSize - 3) {\n            // If so, reslice the string to exclude the whole escape sequence.\n            // We only reduce the size of the string as the chunk must\n            // be smaller than the chunk size.\n            encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n        }\n        let valueHead = \"\";\n        // Check if the chunk was split along a valid unicode boundary.\n        while (encodedChunkHead.length > 0) {\n            try {\n                // Try to decode the chunk back and see if it is valid.\n                // Stop when the chunk is valid.\n                valueHead = decodeURIComponent(encodedChunkHead);\n                break;\n            }\n            catch (error) {\n                if (error instanceof URIError &&\n                    encodedChunkHead.at(-3) === \"%\" &&\n                    encodedChunkHead.length > 3) {\n                    encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n                }\n                else {\n                    throw error;\n                }\n            }\n        }\n        chunks.push(valueHead);\n        encodedValue = encodedValue.slice(encodedChunkHead.length);\n    }\n    return chunks.map((value, i) => ({ name: `${key}.${i}`, value }));\n}\n// Get fully constructed chunks\nasync function combineChunks(key, retrieveChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        return value;\n    }\n    let values = [];\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        values.push(chunk);\n    }\n    if (values.length > 0) {\n        return values.join(\"\");\n    }\n    return null;\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        await removeChunk(key);\n    }\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        await removeChunk(chunkName);\n    }\n}\n//# sourceMappingURL=chunker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/chunker.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/constants.js":
/*!*******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/constants.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS)\n/* harmony export */ });\nconst DEFAULT_COOKIE_OPTIONS = {\n    path: \"/\",\n    sameSite: \"lax\",\n    httpOnly: false,\n    // https://developer.chrome.com/blog/cookie-max-age-expires\n    // https://httpwg.org/http-extensions/draft-ietf-httpbis-rfc6265bis.html#name-cookie-lifetime-limits\n    maxAge: 400 * 24 * 60 * 60,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS91dGlscy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx1dGlsc1xcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBERUZBVUxUX0NPT0tJRV9PUFRJT05TID0ge1xuICAgIHBhdGg6IFwiL1wiLFxuICAgIHNhbWVTaXRlOiBcImxheFwiLFxuICAgIGh0dHBPbmx5OiBmYWxzZSxcbiAgICAvLyBodHRwczovL2RldmVsb3Blci5jaHJvbWUuY29tL2Jsb2cvY29va2llLW1heC1hZ2UtZXhwaXJlc1xuICAgIC8vIGh0dHBzOi8vaHR0cHdnLm9yZy9odHRwLWV4dGVuc2lvbnMvZHJhZnQtaWV0Zi1odHRwYmlzLXJmYzYyNjViaXMuaHRtbCNuYW1lLWNvb2tpZS1saWZldGltZS1saW1pdHNcbiAgICBtYXhBZ2U6IDQwMCAqIDI0ICogNjAgKiA2MCxcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/helpers.js":
/*!*****************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/helpers.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseCookieHeader: () => (/* binding */ parseCookieHeader),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeCookieHeader: () => (/* binding */ serializeCookieHeader)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(ssr)/../../node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js\");\n\n/**\n * @deprecated Since v0.4.0: Please use {@link parseCookieHeader}. `parse` will\n * not be available for import starting v1.0.0 of `@supabase/ssr`.\n */\nconst parse = cookie__WEBPACK_IMPORTED_MODULE_0__.parse;\n/**\n * @deprecated Since v0.4.0: Please use {@link serializeCookieHeader}.\n * `serialize` will not be available for import starting v1.0.0 of\n * `@supabase/ssr`.\n */\nconst serialize = cookie__WEBPACK_IMPORTED_MODULE_0__.serialize;\n/**\n * Parses the `Cookie` HTTP header into an array of cookie name-value objects.\n *\n * @param header The `Cookie` HTTP header. Decodes cookie names and values from\n * URI encoding first.\n */\nfunction parseCookieHeader(header) {\n    const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(header);\n    return Object.keys(parsed ?? {}).map((name) => ({\n        name,\n        value: parsed[name],\n    }));\n}\n/**\n * Converts the arguments to a valid `Set-Cookie` header. Non US-ASCII chars\n * and other forbidden cookie chars will be URI encoded.\n *\n * @param name Name of cookie.\n * @param value Value of cookie.\n */\nfunction serializeCookieHeader(name, value, options) {\n    return (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n}\nfunction isBrowser() {\n    return (typeof window !== \"undefined\" && typeof window.document !== \"undefined\");\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/helpers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/index.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/index.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.combineChunks),\n/* harmony export */   createChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.createChunks),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/helpers.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/constants.js\");\n/* harmony import */ var _chunker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunker */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/chunker.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/base64url.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS91dGlscy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUNFO0FBQ0Y7QUFDRTtBQUM1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxlcmRlbVxcR2l0aHViXFxoZWxhbHNvZnRcXGVzZW5wYWlcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx1dGlsc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vaGVscGVyc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jaHVua2VyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9iYXNlNjR1cmxcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/utils/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/version.js":
/*!***********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/version.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.6.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTMuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGVyZGVtXFxHaXRodWJcXGhlbGFsc29mdFxcZXNlbnBhaVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi41My4wXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFZFUlNJT04gPSAnMC42LjEnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.53.0/node_modules/@supabase/ssr/dist/module/version.js\n");

/***/ })

};
;