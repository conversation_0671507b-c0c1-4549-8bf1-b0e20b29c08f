import { describe, test, expect } from "vitest";
import { mockCustomer } from "./mocks/auth.user";
describe("app_account.profile", () => {
  const customer1 = mockCustomer();
  const customer2 = mockCustomer();

  describe("username validation", () => {
    test("accepts valid slug-friendly usernames", async () => {
      if (!customer1.client || !customer1.data)
        throw new Error("Customer not defined");

      const validUsernames = [
        "user123",
        "test-user",
        "user-123",
        "aaa",
        "123",
        "user-name-123"
      ];

      for (const username of validUsernames) {
        const { data, error } = await customer1.client
          .schema("app_account")
          .from("profile")
          .upsert({
            user_id: customer1.data.id,
            username: username
          })
          .select()
          .single();

        expect(error).toBeNull();
        expect(data?.username).toBe(username);
      }
    });

    test("rejects invalid username formats", async () => {
      if (!customer1.client || !customer1.data)
        throw new Error("Customer not defined");

      const invalidUsernames = [
        "User123", // uppercase
        "user_123", // underscore
        "user 123", // space
        "user@123", // special character
        "-user", // starts with hyphen
        "user-", // ends with hyphen
        "user--123", // double hyphen
        "aa", // too short
        "a", // too short
        ""
      ];

      for (const username of invalidUsernames) {
        const { error } = await customer1.client
          .schema("app_account")
          .from("profile")
          .upsert({
            user_id: customer1.data.id,
            username: username
          });

        expect(error).not.toBeNull();
      }
    });

    test("enforces 20 character limit", async () => {
      if (!customer1.client || !customer1.data)
        throw new Error("Customer not defined");

      // 21 characters - should fail
      const longUsername = "a".repeat(21);
      const { error: longError } = await customer1.client
        .schema("app_account")
        .from("profile")
        .upsert({
          user_id: customer1.data.id,
          username: longUsername
        });

      expect(longError).not.toBeNull();

      // 20 characters - should succeed
      const maxUsername = "a".repeat(20);
      const { data, error } = await customer1.client
        .schema("app_account")
        .from("profile")
        .upsert({
          user_id: customer1.data.id,
          username: maxUsername
        })
        .select()
        .single();

      expect(error).toBeNull();
      expect(data?.username).toBe(maxUsername);
    });

    test("enforces uniqueness", async () => {
      if (
        !customer1.client ||
        !customer1.data ||
        !customer2.client ||
        !customer2.data
      )
        throw new Error("Customers not defined");

      const username = "unique-user";

      // First user creates profile with username
      const { error: firstError } = await customer1.client
        .schema("app_account")
        .from("profile")
        .upsert({
          user_id: customer1.data.id,
          username: username
        });

      expect(firstError).toBeNull();

      // Second user tries to use same username - should fail
      const { error: secondError } = await customer2.client
        .schema("app_account")
        .from("profile")
        .upsert({
          user_id: customer2.data.id,
          username: username
        });

      expect(secondError).not.toBeNull();
      expect(secondError?.code).toBe("23505"); // unique_violation
    });
  });

  describe("nickname validation", () => {
    test("accepts valid nicknames", async () => {
      if (!customer1.client || !customer1.data)
        throw new Error("Customer not defined");

      const validNicknames = [
        "John Doe",
        "Player123",
        "Gaming Master",
        "🎮 Gamer",
        "Test User",
        null // nickname is optional
      ];

      for (const nickname of validNicknames) {
        const { data, error } = await customer1.client
          .schema("app_account")
          .from("profile")
          .upsert({
            user_id: customer1.data.id,
            username: `test-user-${Math.random().toString(36).substr(2, 9)}`,
            nickname: nickname
          })
          .select()
          .single();

        expect(error).toBeNull();
        expect(data?.nickname).toBe(nickname);
      }
    });

    test("enforces 30 character limit", async () => {
      if (!customer1.client || !customer1.data)
        throw new Error("Customer not defined");

      // 31 characters - should fail
      const longNickname = "a".repeat(31);
      const { error: longError } = await customer1.client
        .schema("app_account")
        .from("profile")
        .upsert({
          user_id: customer1.data.id,
          username: `test-user-${Math.random().toString(36).substr(2, 9)}`,
          nickname: longNickname
        });

      expect(longError).not.toBeNull();

      // 30 characters - should succeed
      const maxNickname = "a".repeat(30);
      const { data, error } = await customer1.client
        .schema("app_account")
        .from("profile")
        .upsert({
          user_id: customer1.data.id,
          username: `test-user-${Math.random().toString(36).substr(2, 9)}`,
          nickname: maxNickname
        })
        .select()
        .single();

      expect(error).toBeNull();
      expect(data?.nickname).toBe(maxNickname);
    });
  });

  describe("profile creation", () => {
    test("can create complete profile", async () => {
      if (!customer1.client || !customer1.data)
        throw new Error("Customer not defined");

      const profileData = {
        user_id: customer1.data.id,
        username: "complete-user",
        nickname: "Complete User",
        bio: "This is a test bio",
        gender: "male" as const,
        birth_date: "1990-01-01"
      };

      const { data, error } = await customer1.client
        .schema("app_account")
        .from("profile")
        .upsert(profileData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data?.user_id).toBe(profileData.user_id);
      expect(data?.username).toBe(profileData.username);
      expect(data?.nickname).toBe(profileData.nickname);
      expect(data?.bio).toBe(profileData.bio);
      expect(data?.gender).toBe(profileData.gender);
      expect(data?.birth_date).toBe(profileData.birth_date);
      expect(data?.join_date).toBeDefined();
    });
  });
});
