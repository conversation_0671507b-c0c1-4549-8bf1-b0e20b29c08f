import { afterAll, describe, expect, test } from "vitest";
import { mockCustomer, mockProvider, mockAdmin } from "./mocks/auth.user";
import { serviceClient } from "./utils/client";
import { findValidatedNotification } from "./utils/validation";

describe("Chat Notification System", () => {
  const admin = mockAdmin();
  const customer = mockCustomer();
  const provider = mockProvider();
  const createdNotificationIds: string[] = [];
  const createdConversationIds: string[] = [];
  const createdMessageIds: string[] = [];

  describe("Conversation Notifications", () => {
    test("new conversation notification is sent to other member", async () => {
      if (!customer.data || !provider.data || !customer.client)
        throw new Error("Customer or provider not defined");

      // Use the start_conversation RPC function which creates conversation and adds members
      const { data: conversationId } = await customer.client
        .schema("app_chat")
        .rpc("start_conversation", {
          v_member_ids: [customer.data.id, provider.data.id]
        });

      if (!conversationId) {
        throw new Error("Conversation creation failed");
      }

      createdConversationIds.push(conversationId);

      // Check if notification was created for provider
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "chat.conversation.new");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "chat.conversation.new",
        (data) => data.conversation_id === conversationId
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.conversation_id).toBe(conversationId);
      expect(validatedNotification!.data.initiator_username).toBeDefined();

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("no notification sent for group conversations", async () => {
      if (!customer.data || !provider.data || !admin.data)
        throw new Error("Users not defined");

      // Create a group conversation
      const { data: groupConversation } = await serviceClient
        .schema("app_chat")
        .from("conversation")
        .insert({
          is_group: true
        })
        .select()
        .single();

      if (!groupConversation) {
        throw new Error("Group conversation creation failed");
      }

      createdConversationIds.push(groupConversation.id);

      // Add multiple members to the group conversation
      await serviceClient
        .schema("app_chat")
        .from("member")
        .insert([
          {
            conversation_id: groupConversation.id,
            user_id: customer.data.id
          },
          {
            conversation_id: groupConversation.id,
            user_id: provider.data.id
          },
          {
            conversation_id: groupConversation.id,
            user_id: admin.data.id
          }
        ]);

      // Wait a moment for any potential notifications
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Check that no notifications were created for this group conversation
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("type", "chat.conversation.new");

      // Verify no notification exists for this specific group conversation
      const groupNotification = notifications?.find((n) => {
        try {
          const data = JSON.parse(n.data as string);
          return data.conversation_id === groupConversation.id;
        } catch {
          return false;
        }
      });

      expect(groupNotification).toBeUndefined();
    });
  });

  describe("Message Notifications", () => {
    test("new message notification is sent to conversation members", async () => {
      if (!customer.data || !provider.data)
        throw new Error("Customer or provider not defined");

      // Create a conversation
      const { data: conversation } = await serviceClient
        .schema("app_chat")
        .from("conversation")
        .insert({
          is_group: false
        })
        .select()
        .single();

      if (!conversation) {
        throw new Error("Conversation creation failed");
      }

      createdConversationIds.push(conversation.id);

      // Add members to the conversation
      await serviceClient
        .schema("app_chat")
        .from("member")
        .insert([
          {
            conversation_id: conversation.id,
            user_id: customer.data.id
          },
          {
            conversation_id: conversation.id,
            user_id: provider.data.id
          }
        ]);

      // Customer sends a message
      const { data: message } = await serviceClient
        .schema("app_chat")
        .from("message")
        .insert({
          conversation_id: conversation.id,
          sender_id: customer.data.id,
          content: "Hello, this is a test message!"
        })
        .select()
        .single();

      if (!message) {
        throw new Error("Message creation failed");
      }

      createdMessageIds.push(message.id);

      // Check if notification was created for provider (not sender)
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", provider.data.id)
        .eq("type", "chat.message.new");

      expect(notifications).toBeDefined();

      const validatedNotification = findValidatedNotification(
        notifications!,
        "chat.message.new",
        (data) => data.message_id === message.id
      );

      expect(validatedNotification).toBeDefined();
      expect(validatedNotification!.data.conversation_id).toBe(conversation.id);
      expect(validatedNotification!.data.message_id).toBe(message.id);
      expect(validatedNotification!.data.content_preview).toBe(
        '"Hello, this is a test message!"'
      );

      if (validatedNotification!.notification.id) {
        createdNotificationIds.push(validatedNotification!.notification.id);
      }
    });

    test("no notification sent to message sender", async () => {
      if (!customer.data || !provider.data)
        throw new Error("Customer or provider not defined");

      // Create a conversation
      const { data: conversation } = await serviceClient
        .schema("app_chat")
        .from("conversation")
        .insert({
          is_group: false
        })
        .select()
        .single();

      if (!conversation) {
        throw new Error("Conversation creation failed");
      }

      createdConversationIds.push(conversation.id);

      // Add members to the conversation
      await serviceClient
        .schema("app_chat")
        .from("member")
        .insert([
          {
            conversation_id: conversation.id,
            user_id: customer.data.id
          },
          {
            conversation_id: conversation.id,
            user_id: provider.data.id
          }
        ]);

      // Customer sends a message
      const { data: message } = await serviceClient
        .schema("app_chat")
        .from("message")
        .insert({
          conversation_id: conversation.id,
          sender_id: customer.data.id,
          content: "I should not get a notification for my own message"
        })
        .select()
        .single();

      if (!message) {
        throw new Error("Message creation failed");
      }

      createdMessageIds.push(message.id);

      // Check that customer did not receive a notification for their own message
      const { data: notifications } = await serviceClient
        .schema("app_account")
        .from("notification")
        .select("*")
        .eq("recipient_id", customer.data.id)
        .eq("type", "chat.message.new");

      // Verify no notification exists for this specific message sent by the customer
      const selfNotification = notifications?.find((n) => {
        try {
          const data = JSON.parse(n.data as string);
          return data.message_id === message.id;
        } catch {
          return false;
        }
      });

      expect(selfNotification).toBeUndefined();
    });
  });

  afterAll(async () => {
    // Cleanup created notifications
    if (createdNotificationIds.length > 0) {
      await serviceClient
        .schema("app_account")
        .from("notification")
        .delete()
        .in("id", createdNotificationIds);
    }

    // Cleanup created messages
    if (createdMessageIds.length > 0) {
      await serviceClient
        .schema("app_chat")
        .from("message")
        .delete()
        .in("id", createdMessageIds);
    }

    // Cleanup created conversations
    if (createdConversationIds.length > 0) {
      await serviceClient
        .schema("app_chat")
        .from("conversation")
        .delete()
        .in("id", createdConversationIds);
    }
  });
});
