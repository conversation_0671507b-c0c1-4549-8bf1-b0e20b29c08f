import { test, expect, describe, beforeEach } from "vitest";
import { mockAdmin, mockCustomer, mockSupportAgent } from "./mocks/auth.user";

const customer = mockCustomer();
const customer2 = mockCustomer();
const customer3 = mockCustomer(); // For different ticket states tests
const supportAgent = mockSupportAgent();
const admin = mockAdmin();

describe("Ticket Comments", () => {
  let ticketId: string;

  beforeEach(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { data, error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Test Comment Ticket",
        p_problem_description: "For testing comments",
        p_type: "report"
      });

    if (error) throw new Error(`Failed to create ticket: ${error.message}`);
    if (!data?.id) throw new Error("Ticket ID is undefined");

    ticketId = data.id;
  });

  test("ticket owner should be able to add comments", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { data, error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "This is my comment as the ticket owner"
      });

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(data?.comment_message).toBe(
      "This is my comment as the ticket owner"
    );
    expect(data?.ticket_id).toBe(ticketId);
  });

  test("assigned support agent should be able to add comments", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!supportAgent.client)
      throw new Error("Support agent client is undefined");
    if (!supportAgent.data?.id)
      throw new Error("Support agent ID is undefined");

    // Assign ticket to support agent
    await admin.client.schema("app_support").rpc("assign_ticket", {
      p_ticket_id: ticketId,
      p_assigned_to: supportAgent.data.id
    });

    // Support agent adds comment
    const { data, error } = await supportAgent.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "Support agent response"
      });

    expect(error).toBeNull();
    expect(data?.comment_message).toBe("Support agent response");
  });

  test("admin should be able to add comments to any ticket", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");

    const { data, error } = await admin.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "Admin comment on any ticket"
      });

    expect(error).toBeNull();
    expect(data?.comment_message).toBe("Admin comment on any ticket");
  });

  test("unrelated user should not be able to add comments", async () => {
    if (!customer2.client) throw new Error("Customer2 client is undefined");

    const { error } = await customer2.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "This should fail"
      });

    expect(error).toBeDefined();
    if (error?.message) {
      expect(error.message).toContain(
        "Insufficient permissions to comment on this ticket"
      );
    }
  });

  test("should not allow comments on closed tickets", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.client) throw new Error("Customer client is undefined");

    // Close the ticket
    await admin.client.schema("app_support").rpc("update_ticket_status", {
      p_ticket_id: ticketId,
      p_new_status: "closed"
    });

    // Try to add comment
    const { error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "This should fail on closed ticket"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain("Cannot add comments to closed tickets");
  });

  test("should enforce 10 consecutive comment limit", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    // Add 10 consecutive comments from the same user
    for (let i = 0; i < 10; i++) {
      const { error } = await customer.client
        .schema("app_support")
        .rpc("create_ticket_comment", {
          p_ticket_id: ticketId,
          p_comment_message: `Comment ${i + 1}`
        });
      expect(error).toBeNull();
    }

    // 11th consecutive comment should fail
    const { error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "This should fail - 11th consecutive comment"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain(
      "Maximum consecutive comment limit (10) reached"
    );
  });

  test("should allow comments after another user comments", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");

    // Add 10 consecutive comments from customer
    for (let i = 0; i < 10; i++) {
      const { error } = await customer.client
        .schema("app_support")
        .rpc("create_ticket_comment", {
          p_ticket_id: ticketId,
          p_comment_message: `Customer comment ${i + 1}`
        });
      expect(error).toBeNull();
    }

    // Admin adds a comment (breaks the consecutive chain)
    const { error: adminError } = await admin.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "Admin breaks the chain"
      });
    expect(adminError).toBeNull();

    // Customer should now be able to comment again
    const { data, error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: ticketId,
        p_comment_message: "Customer can comment again after admin"
      });

    expect(error).toBeNull();
    expect(data?.comment_message).toBe(
      "Customer can comment again after admin"
    );
  });

  test("should fail to comment on non-existent ticket", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");

    const { error } = await customer.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: "00000000-0000-0000-0000-000000000000",
        p_comment_message: "This should fail"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain("Ticket not found");
  });
});

describe("Comment Permissions with Different Ticket States", () => {
  let openTicketId: string;
  let closedTicketId: string;

  beforeEach(async () => {
    if (!customer3.client) throw new Error("Customer3 client is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");

    // Create open ticket
    const openTicket = await customer3.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Open Ticket",
        p_problem_description: "Open ticket for testing",
        p_type: "report"
      });

    if (openTicket.error)
      throw new Error(
        `Failed to create open ticket: ${openTicket.error.message}`
      );
    if (!openTicket.data?.id) throw new Error("Open ticket ID is undefined");
    openTicketId = openTicket.data.id;

    // Create and close a ticket
    const closedTicket = await customer3.client
      .schema("app_support")
      .rpc("create_ticket", {
        p_title: "Closed Ticket",
        p_problem_description: "Ticket to be closed",
        p_type: "report"
      });

    if (closedTicket.error)
      throw new Error(
        `Failed to create closed ticket: ${closedTicket.error.message}`
      );
    if (!closedTicket.data?.id)
      throw new Error("Closed ticket ID is undefined");
    closedTicketId = closedTicket.data.id;

    await admin.client.schema("app_support").rpc("update_ticket_status", {
      p_ticket_id: closedTicketId,
      p_new_status: "closed"
    });
  });

  test("should allow comments on open tickets", async () => {
    if (!customer3.client) throw new Error("Customer3 client is undefined");

    const { data, error } = await customer3.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: openTicketId,
        p_comment_message: "Comment on open ticket"
      });

    expect(error).toBeNull();
    expect(data?.comment_message).toBe("Comment on open ticket");
  });

  test("should not allow comments on closed tickets", async () => {
    if (!customer3.client) throw new Error("Customer3 client is undefined");

    const { error } = await customer3.client
      .schema("app_support")
      .rpc("create_ticket_comment", {
        p_ticket_id: closedTicketId,
        p_comment_message: "This should fail on closed ticket"
      });

    expect(error).toBeDefined();
    expect(error?.message).toContain("Cannot add comments to closed tickets");
  });
});
