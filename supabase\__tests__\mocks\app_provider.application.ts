import { afterAll, beforeAll, expect } from "vitest";
import { MockUser } from "./auth.user";
import { mockService } from "./app_provider.service";
import { serviceClient } from "../utils/client";
import { readFileSync } from "fs";
import { join } from "path";
import { fileTypeFromBuffer } from "file-type";

type MockApplication = {
  id?: string;
};

export function mockApplication(customer: MockUser) {
  const application: MockApplication = {};

  beforeAll(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const submitApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .insert({
        user_id: customer.data.id,
        application_status: "draft"
      });

    expect(submitApplication.error).toBeNull();

    const viewApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .select("*")
      .eq("user_id", customer.data.id)
      .single();

    expect(viewApplication.data?.user_id).toBe(customer.data.id);

    application.id = viewApplication.data?.user_id;
  });

  afterAll(async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    // clean application
    await serviceClient
      .schema("app_provider")
      .from("application")
      .delete()
      .eq("user_id", customer.data?.id);
  });

  return application;
}

export function mockFilledOutApplication(customer: MockUser) {
  const application = mockApplication(customer);
  const service = mockService(customer);

  // Track created storage objects for cleanup
  const createdStorageObjects: Array<{ bucket: string; filename: string }> = [];

  // Upload required media files
  beforeAll(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    // Upload avatar
    const avatarPath = join(__dirname, "../objects", "test-avatar.jpg");
    const avatarBuffer = readFileSync(avatarPath);
    const avatarType = await fileTypeFromBuffer(avatarBuffer);

    const avatarUpload = await customer.client.storage
      .from("account_avatar")
      .upload(customer.data.id, avatarBuffer, {
        upsert: true,
        contentType: avatarType?.mime
      });

    if (!avatarUpload.error) {
      createdStorageObjects.push({
        bucket: "account_avatar",
        filename: customer.data.id
      });
    }

    // Upload voice
    const voicePath = join(__dirname, "../objects", "test-voice.mp3");
    const voiceBuffer = readFileSync(voicePath);
    const voiceType = await fileTypeFromBuffer(voiceBuffer);

    const voiceUpload = await customer.client.storage
      .from("provider_voice")
      .upload(customer.data.id, voiceBuffer, {
        upsert: true,
        contentType: voiceType?.mime
      });

    if (!voiceUpload.error) {
      createdStorageObjects.push({
        bucket: "provider_voice",
        filename: customer.data.id
      });
    }

    // Upload gallery images (3 minimum)
    const galleryFiles = [
      "test-gallery-1.webp",
      "test-gallery-2.webp",
      "test-gallery-3.webp"
    ];

    for (let i = 0; i < galleryFiles.length; i++) {
      const galleryPath = join(__dirname, "../objects", galleryFiles[i]);
      const galleryBuffer = readFileSync(galleryPath);
      const galleryType = await fileTypeFromBuffer(galleryBuffer);

      const filename = `${customer.data.id}_${i + 1}`;
      const galleryUpload = await customer.client.storage
        .from("provider_gallery")
        .upload(filename, galleryBuffer, {
          upsert: true,
          contentType: galleryType?.mime
        });

      if (!galleryUpload.error) {
        createdStorageObjects.push({ bucket: "provider_gallery", filename });
      }
    }
  });

  afterAll(async () => {
    // Clean up tracked storage objects
    const bucketGroups = new Map<string, string[]>();

    // Group filenames by bucket
    for (const { bucket, filename } of createdStorageObjects) {
      if (!bucketGroups.has(bucket)) {
        bucketGroups.set(bucket, []);
      }
      bucketGroups.get(bucket)!.push(filename);
    }

    // Clean up each bucket
    for (const [bucket, filenames] of bucketGroups) {
      try {
        if (filenames.length > 0) {
          await serviceClient.storage.from(bucket).remove(filenames);
        }
      } catch (error) {
        console.warn(`Failed to clean up ${bucket}:`, error);
      }
    }

    // Clear the tracking array
    createdStorageObjects.length = 0;
  });

  return { application, service };
}
